import axios, { type AxiosInstance, type AxiosResponse } from 'axios';
import { useLocalStorage } from '@vueuse/core';
import { addPending, removePending } from './axiosCancel';
import { useUser } from '@/store/modules/user';
import { useGlobSetting } from '@/hooks/setting';
import { setupAxiosMock } from '@/../axios-mock';
import router from '@/router';

export const isShowErrorMessage = useLocalStorage('isShowErrorMessage', true);

declare module 'axios' {
  interface AxiosRequestConfig {
    showErrorMsg?: boolean;
  }
  interface InternalAxiosRequestConfig {
    showErrorMsg?: boolean;
  }
}

const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_GLOB_API_URL,
  timeout: 15000,
});

// 在开发环境且启用 mock 时，设置 axios mock
const { useMock } = useGlobSetting();
console.log('🔍 Debug info:', {
  isDev: import.meta.env.DEV,
  useMock: false,
  mode: import.meta.env.MODE,
  baseURL: import.meta.env.VITE_GLOB_API_URL,
});

if (import.meta.env.DEV && useMock) {
  const mockInstance = setupAxiosMock(service);
  console.log('🚀 Axios Mock is enabled', mockInstance);
}

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    removePending(config);
    addPending(config);

    const token = useUser().getToken;
    if (token && config.headers) {
      config.headers.Authorization = token;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    removePending(response.config);

    // 处理 blob 文件下载
    if (response.request?.responseType === 'blob') {
      return response;
    }

    const { code, msg } = response.data;

    if (code === 401) {
      useUser().logout();

      // 确保router已经初始化
      if (router && router.currentRoute) {
        router.replace({
          name: 'Login',
          query: {
            redirect: router.currentRoute.value.fullPath,
          },
        });
      } else {
        // 如果router未初始化，直接跳转到登录页
        window.location.href = '/login';
      }

      return Promise.reject(response.data);
    }

    if (code !== 200) {
      if (isShowErrorMessage.value && response.config?.showErrorMsg !== false) {
        window.$message.error(msg || '请求错误');
      }
      return Promise.reject(response.data);
    }
    return response.data;
  },
  (error) => {
    if (axios.isCancel(error)) {
      console.warn('请求被取消：', error.message);
    } else if (error.config?.showErrorMsg !== false) {
      window.$message.error(error.response?.data?.msg || error.message || '网络异常');
    }
    return Promise.reject(error);
  }
);

export default service;
