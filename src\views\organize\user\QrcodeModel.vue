<!--迁移弹窗-->
<template>
  <n-modal
    preset="dialog"
    title="激活账号"
    style="width: 450px"
    :show-icon="false"
    v-model:show="dialogVisible"
    :mask-closable="false"
  >
    <div class="text-center">请使用融掌通APP扫描下方二维码完成工作机与账号的绑定</div>
    <div class="text-center">扫码后可前往工作机查看绑定状态</div>
    <n-space justify="center" class="my-[20px]">
      <div v-if="qrcodeLoading" class="w-[200px] h-[200px] flex items-center justify-center">
        <n-spin />
      </div>
      <div v-else-if="qrcodeError" class="w-[200px] h-[200px] flex items-center justify-center">
        <n-icon :size="100" color="lightGrey">
          <ImageOutlineIcon />
        </n-icon>
      </div>
      <n-image
        v-else-if="qrcodeUrl"
        ref="qrcodeRef"
        class="w-[200px] h-[200px]"
        preview-disabled
        :src="qrcodeUrl"
      />
    </n-space>
    <div v-if="qrcodeError" class="text-red-500 text-center">
      生成二维码失败，<span class="underline cursor-pointer" @click="getQrcode(curRow)">
        请点击此处重试
      </span>
    </div>
    <n-space justify="center" class="mt-[20px]">
      <n-button @click="close">取消</n-button>
      <n-button type="primary" @click="close">确认</n-button>
    </n-space>
  </n-modal>
</template>

<script setup lang="tsx">
  import { ref, watch } from 'vue';
  import QRCode from 'qrcode';
  import { ImageOutline as ImageOutlineIcon } from '@vicons/ionicons5';
  import { getActivationCodeApi } from '@/api/system';
  import { debounce } from 'lodash-es';

  const qrcodeRef = ref();
  const qrcodeUrl = ref();
  const qrcodeError = ref(false);
  const qrcodeLoading = ref(true);
  const dialogVisible = ref(false);
  const curRow = ref({});

  watch(
    () => dialogVisible.value,
    (value) => {
      if (!value) {
        reset();
      }
    }
  );

  const open = (row) => {
    curRow.value = row;
    dialogVisible.value = true;
    getQrcode(row);
  };

  const close = (_row) => {
    dialogVisible.value = false;
  };

  const getQrcode = debounce(
    (row) => {
      qrcodeLoading.value = true;
      return getActivationCodeApi({
        loginName: row.mdmLoginName,
      })
        .then((res) => {
          if (res.code !== 200) {
            throw new Error(res.message);
          }

          return QRCode.toDataURL(res.data, { width: 200 });
        })
        .then((url) => {
          qrcodeUrl.value = url;
        })
        .catch(() => {
          qrcodeError.value = true;
        })
        .finally(() => {
          qrcodeLoading.value = false;
        });
    },
    1000,
    { leading: true, trailing: false }
  );

  const reset = () => {
    qrcodeUrl.value = '';
    qrcodeError.value = false;
    qrcodeLoading.value = true;
  };

  defineExpose({
    open,
    close,
  });
</script>
