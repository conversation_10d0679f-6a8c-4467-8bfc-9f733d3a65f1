<template>
  <div class="icon-preview-container">
    <h2>Antd Icons 预览</h2>
    <n-input v-model:value="searchValue" placeholder="搜索图标名称" @input="handleSearch" />
    <n-list
      class="flex flex-wrap listRef"
      ref="listRef"
      style="overflow-y: auto"
      @scroll="handleScroll"
    >
      <n-list-item
        v-for="icon in displayedItems"
        :key="icon"
        class="icon-item"
        @click="copyIcon(icon)"
      >
        <n-icon :component="AntdIcons[icon]" size="40" class="icon-component" />
        <p class="icon-name">{{ icon }}</p>
      </n-list-item>
    </n-list>
  </div>
</template>

<script lang="ts" setup>
  import * as AntdIcons from '@vicons/antd';
  import { computed, onMounted, ref } from 'vue';
  import type { Ref } from 'vue';
  import { debounce } from 'lodash-es';
  import { useCopyPaste } from '@/hooks/useCopyPaste';
  const allItems = ref(Object.keys(AntdIcons));
  // 点击复制图标名到剪贴板
  const displayedItems: Ref<any[]> = ref([]);
  const loading = ref(false);
  const batchSize = 20;
  let currentIndex = 0;
  const searchValue = ref('');
  const filterList = computed(() => {
    return allItems.value.filter((item) => item.includes(searchValue.value));
  });
  const { copy } = useCopyPaste();
  onMounted(() => {
    loadMore();
  });
  function copyIcon(iconName) {
    copy(iconName);
    alert(`已复制图标名: ${iconName}`);
  }
  const handleSearch = debounce(() => {
    currentIndex = 0;
    displayedItems.value = [];
    loading.value = false;
    loadMore();
  }, 500);
  const listRef = ref();
  const handleScroll = () => {
    const listEl = document.querySelector('.listRef');
    if (!listEl) return;
    const { scrollTop, scrollHeight, clientHeight } = listEl;
    if (scrollHeight - scrollTop <= clientHeight + 20) {
      loadMore();
    }
  };
  function loadMore() {
    if (loading.value) return;
    loading.value = true;

    setTimeout(() => {
      const nextBatch = filterList.value.slice(currentIndex, currentIndex + batchSize);
      displayedItems.value.push(...nextBatch);
      currentIndex += batchSize;
      loading.value = false;
      const listEl = document.querySelector('.listRef');
      if (listEl) {
        const { scrollHeight, clientHeight } = listEl;
        if (
          scrollHeight === clientHeight &&
          displayedItems.value.length < filterList.value.length
        ) {
          loadMore();
        }
      }
    }, 500);
  }
</script>

<style scoped>
  .icon-preview-container {
    padding: 20px;
  }

  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 20px;
  }

  .icon-item {
    width: 140px;
    height: 140px;
    margin: 10px 0;
    text-align: center;
    cursor: pointer;
    border: 1px solid #e4e4e4;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .icon-item:hover {
    background-color: #f5f5f5;
  }

  .icon-component {
    font-size: 24px;
    color: #333;
  }

  .icon-name {
    margin-top: 8px;
    font-size: 12px;
    word-break: break-all;
  }
  .listRef {
    gap: 10px;
    max-height: calc(100vh - 100px);
  }
</style>
