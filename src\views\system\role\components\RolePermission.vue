<template>
  <div class="tree" v-loading="loading">
    <n-tree
      ref="menuTree"
      :data="menuData"
      key-field="id"
      label-field="title"
      :checked-keys="checkedKeys"
      checkable
      cascade
      show-checkbox
      @update:checked-keys="onCheckedKeysChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { get_role_by_id, deployRelationPermission } from '@/api/system/role';
  import { getMenuList } from '@/api/system/menu';
  import { usePermission } from '@/hooks/web/usePermission';
  import { ref, watch, defineProps } from 'vue';
  import { useMessage } from 'naive-ui';
  import { useUser } from '@/store/modules/user';

  interface IMenuItem {
    id: number;
    title: string;
    children?: IMenuItem[];
  }

  const userStore = useUser();

  const props = defineProps({
    roleId: {
      type: [Number, String],
      default: '',
    },
    tabValue: {
      type: String,
      default: 'menu',
    },
  });

  const loading = ref(false);
  const menuData = ref<IMenuItem[]>([]);
  const { handleMenuData } = usePermission();
  const message = useMessage();
  const checkedKeys = ref<(string | number)[]>([]);
  const menuTree = ref();
  const roleDetail = ref({});
  const initActiveButtons = ref<string[]>([]);
  defineExpose({
    sure,
    roleDetail,
  });
  watch(
    () => props.roleId,
    () => {
      getData();
    },
    { immediate: true }
  );
  function getMenus() {
    return getMenuList().then((res) => {
      if (res.code === 200 && res.data) {
        menuData.value = handleMenuData(res.data); // 所有菜单
      } else {
        menuData.value = [];
      }
      return res;
    });
  }
  /**
   * 获取详情
   */
  async function getData() {
    loading.value = true;
    roleDetail.value = {};
    if (!menuData.value || !menuData.value.length) {
      await getMenus();
    }
    return get_role_by_id(props.roleId)
      .then((roleRes) => {
        if (roleRes.code === 200 && roleRes.data) {
          roleDetail.value = { ...roleRes.data };
          const { menus = [], buttons = [] } = roleRes.data;

          // 获取所有叶子节点
          const leafNodes = getLeafNodes(menuData.value);
          getDefaultReviewChecked(menus, menuData.value, buttons);
          // 只选中存在于叶子节点中的权限
          const validCheckedKeys = [...menus, ...buttons, ...initActiveButtons.value].filter(
            (key) => leafNodes.includes(key)
          );

          console.log('叶子节点:', leafNodes);
          console.log('角色权限:', { menus, buttons });
          console.log('有效的选中keys:', validCheckedKeys);

          // 设置选中状态
          setTimeout(() => {
            checkedKeys.value = validCheckedKeys;
          }, 200);
        } else {
          checkedKeys.value = [];
          roleDetail.value = {};
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
  //查看默认回显
  function getDefaultReviewChecked(menus, menuData, activeButtons) {
    if (!menuData || !menuData.length || !menus || !menus.length) {
      return;
    }
    for (let i = 0; i < menuData.length; i++) {
      const item = menuData[i];
      if (item.children && item.children.length > 0) {
        const childId = item.children[0].id;
        if (childId && typeof childId === 'string' && menus.includes(item.id)) {
          !activeButtons.includes(childId) && initActiveButtons.value.push(childId);
        } else {
          getDefaultReviewChecked(menus, item.children, activeButtons);
        }
      }
    }
  }
  function onCheckedKeysChange(keys: (string | number)[]) {
    checkedKeys.value = keys;
  }

  /**
   * 获取树形结构中的所有叶子节点ID
   */
  function getLeafNodes(treeData: any[]): (string | number)[] {
    const leafNodes: (string | number)[] = [];
    function traverse(nodes: any[]) {
      if (!nodes || !Array.isArray(nodes)) return;

      nodes.forEach((node) => {
        if (!node.children || node.children.length === 0) {
          // 叶子节点
          leafNodes.push(node.id);
        } else {
          // 有子节点，继续遍历
          traverse(node.children);
        }
      });
    }

    traverse(treeData);
    return leafNodes;
  }

  function sure(restParams?: any) {
    return new Promise((resolve, reject) => {
      let menus = menuTree.value.getCheckedData()?.keys ?? [];
      const halfMenus = menuTree.value.getIndeterminateData()?.keys ?? [];
      menus.push(...halfMenus);
      const buttonCode = menus.filter((item: any) => typeof item === 'string');
      menus = menus.filter((item: any) => typeof item === 'number');
      if (menus.length > 0) {
        const postData = {
          roleId: props.roleId,
          menus: [...menus],
          permissions: [],
          buttonCode,
          ...restParams,
        };
        deployRelationPermission(postData).then((res) => {
          if (res.code == 200) {
            message.success('配置成功');
            userStore.getInfo();
            if (props.tabValue === 'menu') {
              setTimeout(() => {
                window.location.reload();
              }, 1500);
            }
          }
        });
        resolve(postData);
      } else {
        message.warning('菜单权限不能为空');
        reject(new Error('菜单权限不能为空'));
      }
    });
  }
</script>

<style lang="less" scoped>
  .tree-title {
    font-size: 18px;
    padding: 15px 0;
  }
  .tree {
    ::v-deep .n-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
</style>
