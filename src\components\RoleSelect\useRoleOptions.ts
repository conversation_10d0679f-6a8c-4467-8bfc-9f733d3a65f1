import { ref, computed } from 'vue';
import { getRoleListApi } from '@/api/system/trafficRule';

// 全局状态
const roleOptions = ref<any[]>([]);
const loading = ref(false);
const loaded = ref(false);

async function loadOptions() {
  if (loading.value || loaded.value) return;

  loading.value = true;
  try {
    const { data } = await getRoleListApi();
    roleOptions.value = data.filter((item) => {
      if (item.status === 1) return;
      return item;
    });
    loaded.value = true;
  } catch (error) {
    console.error('加载角色列表失败:', error);
  } finally {
    loading.value = false;
  }
}

function reloadOptions() {
  roleOptions.value = [];
  loaded.value = false;
  setTimeout(() => {
    loadOptions();
  });
}

export function useRoleOptions() {
  return {
    roleOptions: computed(() => roleOptions.value),
    loading: computed(() => loading.value),
    loaded: computed(() => loaded.value),
    loadOptions,
    reloadOptions,
  };
}
