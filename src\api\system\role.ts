import { get, put, del, post } from '@/utils/lib/axios.package';

export const get_admin_role_list = () => {
  return get('/cms/admin/role/list');
};
// 角色禁用启用
export const updateRoleStatus = (id) => {
  return put(`/cms/admin/role/${id}`);
};
/*
 * 删除角色
 * */

export const del_role = (id) => {
  return del(`/cms/admin/role/${id}`);
};
export const add_role = (obj) => {
  return post('/cms/admin/role/addOrUpDate', obj);
};
export const update_role = (obj) => {
  return post(`/cms/admin/role/addOrUpDate`, obj);
};
export const get_permission_list = () => {
  return get('/cms/admin/permission/list');
};

export const get_menus_permission = () => {
  return get('/cms/admin/role/menus/permission');
};

export const get_role_by_id = (id) => {
  return get(`/cms/admin/role/${id}`);
};

export const get_permission_by_id = (id) => {
  return get(`/cms/admin/permission/${id}`);
};
export const deployRelationPermission = (obj) => {
  return post('/cms/admin/role/relation', obj);
};

export const updateRoleReceive = (obj) => {
  return post('/cms/clue/role/receive/saveOrUpdate', obj);
};
export const get_receive_by_id = (id) => {
  return get(`/cms/clue/role/receive/find/${id}`);
};

// 更新角色拨打电话权限
export const updateRoleMdm = (obj: { id: number; roleMdmPermission: number | null }) => {
  return post('/cms/admin/role/updateMdm', obj);
};
// 更新其他权限
export const updateRoleMobile = (obj: { id: number; roleMobilePermission: number | null }) => {
  return post('/cms/admin/role/update-other-permission', obj);
};
