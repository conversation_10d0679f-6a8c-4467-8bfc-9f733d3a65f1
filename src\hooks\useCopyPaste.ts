// hooks/useCopyPaste.js
import { ref } from 'vue';

export function useCopyPaste() {
  const copiedText = ref('');
  const copied = ref(false);
  const isSupported = ref(!!navigator.clipboard);

  const copy = async (text) => {
    if (!isSupported.value) {
      fallbackCopy(text);
      return;
    }
    try {
      await navigator.clipboard.writeText(text);
      copiedText.value = text;
      copied.value = true;
      setTimeout(() => (copied.value = false), 1500);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const fallbackCopy = (text) => {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      copiedText.value = text;
      copied.value = true;
      setTimeout(() => (copied.value = false), 1500);
    } catch (err) {
      console.error('回退复制失败:', err);
    }
    document.body.removeChild(textarea);
  };

  const paste = async () => {
    if (!isSupported.value) {
      console.warn('浏览器不支持 Clipboard API');
      return null;
    }
    try {
      return await navigator.clipboard.readText();
    } catch (err) {
      console.error('粘贴失败:', err);
      return null;
    }
  };

  return {
    copy,
    paste,
    copied,
    copiedText,
    isSupported,
  };
}
