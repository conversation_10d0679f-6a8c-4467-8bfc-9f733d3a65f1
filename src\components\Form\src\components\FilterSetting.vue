<template>
  <n-tooltip trigger="hover">
    <template #trigger>
      <div class="cursor-pointer">
        <n-popover trigger="click" :width="240" class="toolbar-popover" placement="bottom-end">
          <template #trigger>
            <div class="flex justify-center items-center">
              <n-icon size="18">
                <SettingOutlined />
              </n-icon>
            </div>
          </template>
          <template #header>
            <div class="table-toolbar-inner-popover-title">
              <n-space>
                <n-button text type="info" size="small" class="mt-1" @click="checkALl"
                  >全选</n-button
                >
                <n-divider vertical />
                <n-button text type="info" size="small" class="mt-1" @click="checkNone"
                  >全不选</n-button
                >
                <n-divider vertical />
                <n-button text type="info" size="small" class="mt-1" @click="setDefault"
                  >恢复默认</n-button
                >
              </n-space>
            </div>
          </template>
          <div class="table-toolbar-inner">
            <n-checkbox-group v-model:value="checkList" @update:value="onChange">
              <div v-for="element in columnsList" :key="element.field" class="py-[3px]">
                <n-checkbox
                  :disabled="element.noHidden"
                  :value="element.field"
                  :label="element.label"
                />
              </div>
            </n-checkbox-group>
          </div>
        </n-popover>
      </div>
    </template>
    <span>筛选项设置</span>
  </n-tooltip>
</template>

<script setup lang="ts">
  import { SettingOutlined } from '@vicons/antd';
  import { FormSchema } from '../../index';
  import { ref, watch } from 'vue';
  import { cloneDeep } from 'lodash-es';
  import { useFormContext } from '../hooks/useFormContext';

  const props = defineProps({
    columns: {
      type: Array as PropType<FormSchema[]>,
      default: () => [],
    },
    activeColumns: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  });

  const form: any = useFormContext();
  const columnsList = ref<FormSchema[]>([]);
  // 勾选的列
  const checkList = ref<string[]>([]);
  // 默认的列
  const defaultCheckList = ref<string[]>([]);
  // 全部列
  const allColumns = ref<string[]>([]);

  watch(
    () => props.columns,
    () => {
      init();
    }
  );
  watch(
    () => props.activeColumns,
    () => {
      checkList.value = props.activeColumns;
    },
    { immediate: true, deep: true }
  );

  // 设置列
  function setColumns(key: string | string[]): void {
    form.setColumnsShow(key);
  }
  //切换
  function onChange(checkList: string[]) {
    setColumns(checkList);
  }
  // 全选
  function checkALl() {
    checkList.value = [...allColumns.value];
    setColumns(checkList.value);
  }
  // 全不选
  function checkNone() {
    checkList.value = props.columns.filter((item) => item.noHidden).map((item) => item.field);
    setColumns(checkList.value);
  }
  // 恢复默认
  function setDefault() {
    checkList.value = [...defaultCheckList.value];
    setColumns(checkList.value);
  }
  //初始化
  function init() {
    const columns = props.columns;
    defaultCheckList.value = columns.filter((item) => !item.hidden).map((item) => item.field);
    allColumns.value = columns.map((item) => item.field);
    columnsList.value = cloneDeep(columns);
  }
</script>
