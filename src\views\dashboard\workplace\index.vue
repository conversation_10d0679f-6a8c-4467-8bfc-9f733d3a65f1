<template>
  <n-card :bordered="false">
    <BasicForm
      :enable-cache="true"
      @register="register"
      @submit="reloadTable"
      @reset="reloadTable()"
    >
      <template #mediaPlatformSource="{ model, field }">
        <SourceMediaSelect v-model="model[field]" type="media" placeholder="请选择来源媒体" />
      </template>
      <!-- <template #blongUserId="{ model, field }">
        <NSelect
          v-model:value="model[field]"
          label-field="username"
          value-field="id"
          :options="userList"
          placeholder="请选择归属人"
          filterable
        />
      </template> -->
      <template #actionButton>
        <n-button
          v-permission="{ action: 'export' }"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </n-button>
      </template>
    </BasicForm>
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="tableColumns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      :actionColumn="actionColumn"
      :scroll-x="3500"
      ref="action"
      :striped="true"
    >
      <template #tableTitle>
        <n-space class="w-full" :wrap-item="false" align="center" justify="space-between">
          <n-space>
            <n-tabs
              class="w-[230px] mr-5"
              :value="tabValue"
              type="line"
              animated
              @update:value="
                (val) => {
                  (tabValue = val), reloadTable();
                }
              "
            >
              <!-- followStatusOptions 按照1,0,2排序 -->
              <n-tab-pane
                v-for="item in followStatusOptions.sort(
                  (a, b) => [1, 0, 2].indexOf(a.value) - [1, 0, 2].indexOf(b.value)
                )"
                :name="item.value"
                :tab="item.label"
                :key="item.value"
              />
            </n-tabs>
            <n-button v-permission="{ action: 'add' }" type="primary" @click="handleOpenForm">
              <template #icon>
                <n-icon>
                  <PlusOutlined />
                </n-icon>
              </template>
              新增线索
            </n-button>
            <n-button
              v-permission="{ action: 'claim' }"
              type="primary"
              @click="handleOpenClueClaim"
            >
              认领线索
            </n-button>
          </n-space>
          当前{{ clueCount }}条线索待领取
        </n-space>
      </template>
    </BasicTable>
  </n-card>

  <FormModal ref="formModal" @close="reloadTable" />
  <ClueClaim ref="clueClaim" @close="reloadTable" />
  <UpdateCustomerModal ref="updateCustomerModalRef" @success="reloadTable" />
  <AddFollowUpDrawer
    ref="addFollowUpDrawer"
    :communication-status="actionCurrentRecord.communicationStatus"
    @submit-success="reloadTable"
  />
  <DetailDrawer ref="detailDrawer" @close="reloadTable" />
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import {
    reactive,
    useTemplateRef,
    ref,
    onMounted,
    computed,
    onUnmounted,
    onActivated,
  } from 'vue';
  import { PlusOutlined, HeartFilled, HeartOutlined } from '@vicons/antd';
  import FormModal from '@/views/client/myClients/FormModal.vue';
  import { insertColumnsAt } from '@/utils/tableUtils';
  import ClueClaim from '@/views/client/myClients/ClueClaim.vue';
  import UpdateCustomerModal from './components/UpdateCustomerModal/UpdateCustomerModal.vue';
  import DetailDrawer from '@/components/DetailDrawer/index.vue';
  import AddFollowUpDrawer from '@/components/DetailDrawer/components/AddFollowUpDrawer.vue';
  import SourceMediaSelect from '@/components/SourceMediaSelect/index.vue';
  import {
    FollowStatusOptions,
    ContactStatusOptions,
    IntentionDegreeOptions,
    IsAddWeChatOptions,
    ClueTypeOptions,
    ClueBidTypeOptions,
    InvalidTypeOptions,
  } from '@/views/client/enum';
  import {
    getFollowUpListApi,
    exportFollowUpListApi,
    clueLikeApi,
    clueCountApi,
  } from '@/api/dashboard/workplace';
  import dayjs from 'dayjs';
  import { useUser } from '@/store/modules/user';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import { getChannelListApi } from '@/api/client';
  import { useDateOptions } from '@/composables/useDateOptions';
  import { promisifyDialog } from '@/utils/nativeUtils';
  import emitter, { EventNames, bufferedEmitter } from '@/utils/eventBus';
  import { JumpWorkbench } from '@/views/message/detail/enum';
  import { useDrawerOrder } from '@/composables/useDrawerOrder';

  defineOptions({
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'dashboard_workplace',
  });

  const userStore = useUser();
  const detailDrawerRef = useTemplateRef<InstanceType<typeof DetailDrawer>>('detailDrawer');
  const addFollowUpDrawerRef =
    useTemplateRef<InstanceType<typeof AddFollowUpDrawer>>('addFollowUpDrawer');
  const updateCustomerModalRef = ref<InstanceType<typeof UpdateCustomerModal> | null>(null);
  const tabValue = ref('1');
  const exportLoading = ref(false);
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  const clueClaimRef = useTemplateRef<InstanceType<typeof ClueClaim>>('clueClaim');
  const formModalRef = useTemplateRef<InstanceType<typeof FormModal>>('formModal');
  const followStatusOptions = ref(FollowStatusOptions);
  const actionCurrentRecord = ref({ communicationStatus: null });
  // 归属人list
  // const userList = ref<any[]>([]);
  const channelList = ref<any[]>([]);

  const { open } = useDrawerOrder();
  const schemas = computed<FormSchema[]>(() => {
    return [
      {
        field: 'createTimeQuery',
        label: '创建时间',
        component: 'NDatePicker',
        childKey: ['startTime', 'endTime'],
        defaultValue: [
          dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.createTimeQuery,
        },
        noHidden: multiDateNoHidden.value.createTimeQuery,
      },
      {
        field: 'clueEnableAssignQuery',
        component: 'NDatePicker',
        label: '提交时间',
        childKey: ['clueEnableAssignStart', 'clueEnableAssignEnd'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.clueEnableAssignQuery,
        },
        noHidden: multiDateNoHidden.value.clueEnableAssignQuery,
      },
      {
        field: 'triageTimeQuery',
        label: '分发时间',
        component: 'NDatePicker',
        childKey: ['triageStartTime', 'triageEndTime'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.triageTimeQuery,
        },
        noHidden: multiDateNoHidden.value.triageTimeQuery,
      },
      {
        field: 'lastFollowTimeQuery',
        label: '跟进时间',
        component: 'NDatePicker',
        childKey: ['lastFollowStartTime', 'lastFollowEndTime'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.lastFollowTimeQuery,
        },
        noHidden: multiDateNoHidden.value.lastFollowTimeQuery,
      },
      {
        field: 'name',
        component: 'NInput',
        label: '姓名',
        componentProps: {
          placeholder: '请输入姓名',
        },
      },
      {
        field: 'mobileNo',
        component: 'NInput',
        label: '电话',
        componentProps: {
          placeholder: '请输入电话号码',
          showButton: false,
          maxlength: 11,
          onInput: () => {
            const { mobileNo } = getFieldsValue();
            const formattedValue = mobileNo.replace(/\D/g, '');
            if (mobileNo !== formattedValue) {
              setFieldsValue({ mobileNo: formattedValue });
            }
          },
        },
      },
      // {
      //   field: 'blongUserId',
      //   label: '归属人',
      //   slot: 'blongUserId',
      // },
      {
        field: 'channelCode',
        component: 'NSelect',
        label: '线索来源',
        componentProps: {
          placeholder: '请选择线索来源',
          options: channelList.value,
          multiple: true,
        },
      },
      // {
      //   field: 'sourceMedia',
      //   component: 'NSelect',
      //   label: '来源媒体',
      //   componentProps: {
      //     placeholder: '请选择来源媒体',
      //     options: MediaSourceOptions,
      //     multiple: true,
      //   },
      // },
      {
        field: 'sourceMedia',
        label: '来源媒体',
        slot: 'mediaPlatformSource',
      },
      {
        field: 'communicationStatus',
        component: 'NSelect',
        label: '通讯状态',
        componentProps: {
          placeholder: '请选择通讯状态',
          options: ContactStatusOptions,
          multiple: true,
        },
      },
      {
        field: 'intent',
        component: 'NSelect',
        label: '意向度',
        componentProps: {
          placeholder: '请选择意向度',
          options: IntentionDegreeOptions,
          multiple: true,
        },
      },
      {
        field: 'addWeChat',
        component: 'NSelect',
        label: '是否加微',
        componentProps: {
          placeholder: '请选择是否加微',
          options: IsAddWeChatOptions,
        },
      },
      {
        field: 'sourceChannelCode',
        component: 'NInput',
        label: '来源渠道',
        componentProps: {
          placeholder: '请输入来源渠道',
        },
      },
      {
        field: 'clueType',
        component: 'NSelect',
        label: '线索类型',
        componentProps: {
          placeholder: '请选择线索类型',
          options: ClueTypeOptions,
        },
      },
      {
        field: 'invalidType',
        component: 'NSelect',
        label: '无效类型',
        componentProps: {
          placeholder: '请选择无效类型',
          options: InvalidTypeOptions,
        },
      },
      {
        field: 'clueBidType',
        component: 'NSelect',
        label: '线索出价类型',
        componentProps: {
          placeholder: '请选择线索出价类型',
          options: ClueBidTypeOptions,
        },
      },
    ];
  });
  const [register, { getFieldsValue, setFieldsValue, getFormModel, getActiveColumns }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });

  // 使用组合式函数创建 clearable 状态
  const { multiDateClearable, multiDateNoHidden } = useDateOptions(getFormModel, getActiveColumns);
  const actionColumn = reactive({
    width: 180,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record) {
      return (
        <n-space>
          <n-button
            v-permission={{ action: 'follow_up' }}
            type="primary"
            text
            onClick={() => {
              actionCurrentRecord.value = { communicationStatus: null, ...record };
              addFollowUpDrawerRef.value?.openDrawer(record.publicClueId);
            }}
          >
            跟进
          </n-button>
          <n-button
            v-permission={{ action: 'detail' }}
            type="primary"
            text
            onClick={() => {
              open();
              // detailDrawerRef.value?.openDrawer(record.publicClueId);
            }}
          >
            详情
          </n-button>
          <n-button
            v-permission={{ action: 'update_info' }}
            type="primary"
            text
            onClick={() => {
              updateCustomerModalRef.value!.openModal(record.publicClueId);
            }}
          >
            补充资料
          </n-button>
        </n-space>
      );
    },
  });
  const clueCount = ref(0);
  // 生成最终的表格列配置 - 在第2个位置和第7个位置插入列
  const tableColumns = insertColumnsAt(columns, {
    at: 0, // 第二个位置（索引从0开始）
    column: {
      title: '特别关注',
      key: 'likeTime',
      width: 80,
      align: 'center',
      render: (_record: any) => {
        return (
          <n-icon
            size={20}
            onClick={async () => {
              if (_record.likeTime) {
                const result = await promisifyDialog(window.$dialog.warning)({
                  title: '取消关注确认',
                  content: '请确认是否取消关注该客户,取消关注后,该客户将恢复默认排序',
                  positiveText: '确定',
                  negativeText: '取消',
                  maskClosable: false,
                });
                if (result.source !== 'positive') {
                  return;
                }
              }

              window.$loading.start();
              clueLikeApi({
                clueId: _record.publicClueId,
                likeStatus: _record.likeTime ? 0 : 1,
              })
                .then(() => {
                  _record.likeTime = _record.likeTime ? 0 : 1;
                  reloadTable();

                  if (_record.likeTime) {
                    window.$message.success('特别关注成功，已置顶显示');
                  } else {
                    window.$message.success('取消关注成功');
                  }
                })
                .finally(() => {
                  window.$loading.finish();
                });
            }}
          >
            {_record.likeTime ? <HeartFilled color="red" /> : <HeartOutlined color="grey" />}
          </n-icon>
        );
      },
    },
  });

  const loadDataTable = async (params) => {
    const { data: clueCountData } = await clueCountApi();
    clueCount.value = clueCountData;
    const { data } = await getFollowUpListApi({
      ...getFieldsValue(),
      ...params,
      followStatus: tabValue.value,
    });
    return data;
  };
  function reloadTable() {
    actionRef.value!.reload();
  }
  function handleOpenForm() {
    formModalRef.value!.formDialogModel = true;
  }
  function handleExport() {
    try {
      exportLoading.value = true;
      window.open(
        exportFollowUpListApi({
          ...getFieldsValue(),
          pageNumber: 1,
          pageSize: 10000,
          token: userStore.getToken,
          followStatus: tabValue.value,
        })
      );
    } finally {
      exportLoading.value = false;
    }
  }
  function handleOpenClueClaim() {
    clueClaimRef.value!.formDialogModel = true;
  }

  // async function getUserList() {
  //   try {
  //     const { data } = await getUserListApi();

  //     userList.value = data;
  //   } catch (err) {
  //     console.log(err);
  //   }
  // }

  async function getChannelList() {
    try {
      const { data } = await getChannelListApi();

      channelList.value = data.map((item) => ({
        label: item.channelName,
        value: item.channelCode,
      }));
    } catch (err) {
      console.log(err);
    }
  }

  function onHandleMsgDetail(opts: { fromMessage: string; clueId: string }) {
    const { fromMessage, clueId } = opts;
    if (!fromMessage) return;

    // 查看详情
    if (fromMessage && String(fromMessage) === String(JumpWorkbench.OpenDetail)) {
      if (clueId) {
        detailDrawerRef.value?.openDrawer(Number(clueId));
      }
    }
    // 领取线索
    if (fromMessage && String(fromMessage) === String(JumpWorkbench.ReceiveClue)) {
      handleOpenClueClaim();
    }
  }

  function onHandleFollowStatus(opts: { followStatus: string }) {
    const { followStatus: val } = opts;
    if (!followStatusOptions.value.find((item) => String(item.value) === String(val))) {
      return;
    }

    setTimeout(() => {
      tabValue.value = val;
      reloadTable();
    }, 0);
  }

  onMounted(() => {
    // getUserList();
    getChannelList();
    emitter.on('workCallFinish', () => {
      reloadTable();
    });
    onActivated(() => {
      reloadTable();
    });

    bufferedEmitter.on(EventNames.JUMP_WORKBENCH_AFTER, onHandleMsgDetail);
    bufferedEmitter.on(EventNames.JUMP_WORKBENCH_TAB, onHandleFollowStatus);
  });
  onUnmounted(() => {
    emitter.off('workCallFinish');
    bufferedEmitter.off(EventNames.JUMP_WORKBENCH_AFTER);
    bufferedEmitter.off(EventNames.JUMP_WORKBENCH_TAB);
  });
</script>

<style lang="less" scoped>
  :deep(.n-tabs) {
    .n-tab-pane {
      padding: 0 !important;
    }
  }
</style>
