/**
 * 根据文件url获取文件名
 * @param url 文件url
 */
function getFileName(url) {
  const num = url.lastIndexOf('/') + 1;
  let fileName = url.substring(num);
  //把参数和文件名分割开
  fileName = decodeURI(fileName.split('?')[0]);
  return fileName;
}

/**
 * 根据文件地址下载文件
 * @param {*} sUrl
 */
export function downloadByUrl({
  url,
  target = '_blank',
  fileName,
}: {
  url: string;
  target?: '_self' | '_blank';
  fileName?: string;
}): Promise<boolean> {
  return new Promise<boolean>((resolve, reject) => {
    try {
      console.log(`🔗 开始下载URL: ${url}`);
      console.log(`📝 文件名: ${fileName || '自动提取'}`);

      // 创建下载链接
      const link = document.createElement('a');
      link.href = url;
      link.target = target;

      // 设置下载文件名
      const finalFileName = fileName || getFileName(url);
      if (finalFileName) {
        link.download = finalFileName;
        console.log(`💾 设置下载文件名: ${finalFileName}`);
      }

      // 添加到DOM并触发下载
      link.style.display = 'none';
      document.body.appendChild(link);

      // 触发点击事件
      if (document.createEvent) {
        const e = document.createEvent('MouseEvents');
        e.initEvent('click', true, true);
        link.dispatchEvent(e);
      } else {
        // 兼容性处理
        link.click();
      }

      // 清理DOM
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);

      console.log(`✅ 下载链接已触发`);
      resolve(true);
    } catch (error) {
      console.error(`❌ downloadByUrl 失败:`, error);
      reject(error);
    }
  });
}

// 下载流文件
/**
 * 使用 Blob 对象触发浏览器下载
 * @param blob Blob 数据对象
 * @param filename 下载保存的文件名
 */
export function downloadByBlob(blob: Blob, filename: string) {
  const blobUrl = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = blobUrl;
  a.download = filename;
  a.style.display = 'none';

  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);

  URL.revokeObjectURL(blobUrl); // 释放资源
}
