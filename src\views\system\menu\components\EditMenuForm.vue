<template>
  <n-form-item label="菜单名" path="title" :rule="rules.common">
    <n-input v-model:value="ruleForm.title" clearable />
  </n-form-item>
  <n-form-item label="地址：" path="path" :rule="rules.common">
    <n-input v-model:value="ruleForm.path" placeholder="请输入地址" autocomplete="off" />
  </n-form-item>
  <n-form-item label="父级菜单：">
    <n-cascader
      v-model:value="ruleForm.parentId"
      label-field="title"
      value-field="id"
      :options="props.menuList"
      clearable
      placeholder="自为父级"
    />
  </n-form-item>
  <n-form-item label="icon：" path="icon">
    <n-input
      style="width: 70%"
      v-model:value="ruleForm.icon"
      placeholder="请输入icon"
      autocomplete="off"
    />
    <a
      style="color: #2d8cf0; margin-top: 5px; font-size: 12px; margin-left: 3px"
      :href="`/iconPreview`"
      target="_blank"
      >前往复制icon</a
    >
  </n-form-item>
  <n-form-item label="排序：" path="sort">
    <n-input-number
      type="text"
      style="width: 100%"
      v-model:value="ruleForm.sort"
      autocomplete="off"
      placeholder="请输入排序内容,数字越大越靠前"
    />
  </n-form-item>
  <n-form-item label="是否在菜单栏展示：" path="hiddenInMenu" :rule="rules.hidden">
    <n-radio-group v-model:value="ruleForm.hiddenInMenu" name="hiddenInMenu">
      <n-space>
        <n-radio :checked="ruleForm.hiddenInMenu === false" :value="false"> 展示 </n-radio>
        <n-radio :checked="ruleForm.hiddenInMenu === true" :value="true"> 不展示 </n-radio>
      </n-space>
    </n-radio-group>
  </n-form-item>
</template>

<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import type { FormItemRule } from 'naive-ui';
  const props = defineProps({
    formVal: {
      type: Object,
      default: () => ({}),
    },
    menuList: {
      type: Array,
      default: () => [],
    },
    isType: {
      type: String,
      default: 'add',
    },
  });
  interface formType {
    title: string;
    icon: string;
    path: string;
    parentId: string | number | null;
    sort: number | undefined;
    hiddenInMenu: boolean;
  }
  const defaultForm: formType = {
    title: '',
    icon: '',
    path: '',
    parentId: null,
    sort: undefined,
    hiddenInMenu: false,
  };
  const hiddenValid = (rule: FormItemRule, value: Number) => {
    if (value === undefined || value === null) {
      return new Error('请选择');
    } else {
      return true;
    }
  };
  const emit = defineEmits(['submit', 'closeModal', 'update:form-val']);
  const ruleForm = ref({ ...defaultForm });
  const rules = ref({
    common: [{ required: true, message: '此项不能为空', trigger: ['input', 'blur', 'change'] }],
    hidden: [{ validator: hiddenValid, trigger: ['input', 'blur', 'change'] }],
  });
  watch(
    () => ruleForm.value,
    (val) => {
      emit('update:form-val', val);
    }
  );
  function filterCascader(list) {
    if (props.isType === 'edit') {
      for (let i = 0; i < list.length; i++) {
        let item: any = list[i];
        if (ruleForm.value.parentId === item?.id) {
          return true;
        }
        if (item.children && item.children.length > 0) {
          let existParentId = filterCascader(item.children);
          if (existParentId) return true;
        }
      }
      return false;
    }
  }
  onMounted(() => {
    ruleForm.value = { ...defaultForm, ...props.formVal };
    let existParentId = filterCascader(props.menuList);
    if (!existParentId) {
      ruleForm.value.parentId = null;
    }
  });
</script>
