import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { OptionsSharp } from '@vicons/ionicons5';
import { renderIcon } from '@/utils/index';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/system',
    name: 'System',
    redirect: '/system/menu',
    component: Layout,
    meta: {
      title: '系统设置',
      icon: renderIcon(OptionsSharp),
      sort: 1,
    },
    children: [
      {
        path: 'menu',
        name: 'system_menu',
        meta: {
          title: '系统菜单',
          buttonsPreFix: 'system_menu_',
          buttons: [
            { key: 'add', name: '新增菜单' },
            { key: 'edit', name: '编辑菜单' },
            { key: 'delete', name: '删除菜单' },
          ],
          keepAlive: true,
        },
        component: () => import('@/views/system/menu/menu.vue'),
      },
      {
        path: 'role',
        name: 'system_role',
        meta: {
          title: '系统角色',
          buttonsPreFix: 'system_role_',
          buttons: [
            { key: 'add', name: '新增角色' },
            { key: 'edit', name: '编辑角色' },
            { key: 'status', name: '禁用/启用' },
            { key: 'remove', name: '删除角色' },
            { key: 'save', name: '保存' },
          ],
          keepAlive: true,
        },
        component: () => import('@/views/system/role/role.vue'),
      },
      {
        path: 'param',
        name: 'system_param',
        meta: {
          title: '系统参数',
          keepAlive: true,
        },
        component: () => import('@/views/system/param/index.vue'),
      },
      {
        path: 'log',
        name: 'system_log',
        meta: {
          title: '系统日志',
          keepAlive: true,
        },
        component: () => import('@/views/system/log/index.vue'),
      },
      {
        path: 'traffic-rule',
        name: 'system_traffic_rule',
        meta: {
          title: '流量分配管理',
          buttonsPreFix: 'system_traffic_rule_',
          buttons: [
            { key: 'add', name: '新增规则' },
            { key: 'edit', name: '编辑规则' },
            { key: 'delete', name: '删除规则' },
            { key: 'status', name: '启用/禁用' },
          ],
          keepAlive: true,
        },
        component: () => import('@/views/system/trafficRule/index.vue'),
      },
    ],
  },
];

export default routes;
