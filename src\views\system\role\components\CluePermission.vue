<template>
  <n-space vertical>
    <n-checkbox-group :value="checkedKeys" @update:value="handleUpdateValue">
      <n-space item-style="display: flex;" vertical>
        <n-checkbox v-for="item in items" :key="item.id" class="mb-[10px]" :value="item.id">
          <n-tooltip trigger="hover">
            <template #trigger>
              <div class="flex items-center">
                {{ item.name
                }}<n-icon size="15">
                  <HelpCircleOutline />
                </n-icon>
              </div>
            </template>
            {{ item.tips }}
          </n-tooltip>
        </n-checkbox>
      </n-space>
    </n-checkbox-group>

    <n-data-table :columns="columns" :data="data" :bordered="true" />
  </n-space>
</template>

<script setup lang="ts">
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { ref, watch } from 'vue';
  import { isEmpty } from '@/utils/is';
  defineExpose({
    sure,
  });
  const props = defineProps({
    roleId: {
      type: [Number, String],
      default: '',
    },
    roleDetail: {
      type: Object,
      default: () => ({}),
    },
  });
  const checkedKeys = ref<any[]>([3]);
  watch(
    () => props.roleDetail.id,
    () => {
      getData();
    },
    { immediate: true }
  );

  const items = [
    {
      id: 0,
      name: '全部',
      tips: '用户可访问系统中所有数据，无任何限制',
    },
    {
      id: 1,
      name: '本部门',
      tips: '仅限访问用户所属部门创建的数据（不包含子部门）',
    },
    {
      id: 2,
      name: '本部门以及下级部门',
      tips: '可访问本部门以所有子部门的数据（递归包含下级）',
    },
    {
      id: 3,
      name: '本人',
      tips: '仅限访问用户自己创建的数据',
    },
    {
      id: 4,
      name: '本人及下级',
      tips: '可访问本人及直属下属创建的数据（需明确“下级”定义：如直接汇报下属/团队下属）',
    },
  ];

  const columns = [
    {
      title: '权限范围',
      key: 'department',
    },
    {
      title: '含义说明',
      key: 'person',
    },
  ];

  const data = [
    {
      department: '全部',
      person: '用户可访问系统中所有数据，无任何限制',
    },
    {
      department: '本部门',
      person: '仅限访问用户所属部门创建的数据（不包含子部门）',
    },
    {
      department: '本部门及下级部门',
      person: '可访问本部门以所有子部门的数据（递归包含下级）',
    },
    {
      department: '本人',
      person: '仅限访问用户自己创建的数据',
    },
    {
      department: '本人及下级',
      person: '可访问本人及直属下属创建的数据（需明确“下级”定义：如直接汇报下属/团队下属）',
    },
  ];

  function handleUpdateValue(_, meta) {
    checkedKeys.value = meta.actionType === 'check' ? [meta.value] : [];
  }

  function getData() {
    const { roleCluePermission } = props.roleDetail;
    if (!isEmpty(roleCluePermission)) {
      checkedKeys.value = [roleCluePermission];
    } else {
      checkedKeys.value = [];
    }
  }

  function sure() {
    const postData = {
      roleCluePermission: checkedKeys.value?.[0] ?? null,
    };
    return postData;
  }
</script>

<style lang="less" scoped></style>
