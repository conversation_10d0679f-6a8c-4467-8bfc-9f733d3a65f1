<template>
  <n-radio-group v-model:value="modelValue" :name="name">
    <n-radio-button
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      :label="option.label"
      :disabled="option.disabled"
    >
      <div class="flex items-center">
        {{ option.label }}
        <n-tooltip v-if="option.tip" trigger="hover" :to="false">
          <template #trigger>
            <n-icon class="ml-1" :size="16"><QuestionCircleOutlined /></n-icon>
          </template>
          {{ option.tip }}
        </n-tooltip>
      </div>
    </n-radio-button>
  </n-radio-group>
</template>

<script lang="ts" setup>
  import { NRadioGroup, NRadioButton, NTooltip, NIcon } from 'naive-ui';
  import { QuestionCircleOutlined } from '@vicons/antd';

  interface Option {
    value: string | number;
    label: string;
    tip?: string;
    disabled?: boolean;
  }

  defineProps<{
    options: Option[];
    name?: string;
  }>();

  const modelValue = defineModel<string | number | null>();
</script>

<style scoped>
  .ml-1 {
    margin-left: 4px;
    vertical-align: middle;
  }
</style>
