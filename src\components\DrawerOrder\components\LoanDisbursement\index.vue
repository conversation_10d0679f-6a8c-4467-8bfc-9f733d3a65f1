<template>
  <n-space vertical>
    <n-card>
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="放款阶段" :status="2" />
          </template>
          <n-space vertical>
            <div>
              <SubTitle title="放款结果" />
              <n-data-table :columns="columns" :data="data" />
            </div>
            <div>
              <SubTitle title="还款计划" />
              <n-data-table :columns="columns1" :data="data1" />
            </div>
          </n-space>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import { ref } from 'vue';
  const searchLoading = ref(false);
  const columns = [
    {
      title: '放款结果',
      key: 'status',
      align: 'center',
    },
    {
      title: '审批备注',
      key: 'examineRemark',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info" disabled={searchLoading.value} onClick={handleSearch}>
            状态查询
          </n-button>
        );
      },
    },
  ];
  const data = [
    {
      key: '1',
      status: '待审核',
      examineRemark: '',
    },
  ];
  const columns1 = [
    {
      title: '还款状态',
      key: 'status',
      align: 'center',
    },
    {
      title: '结算状态',
      key: 'examineRemark',
      align: 'center',
    },
    {
      title: '期数',
      key: 'status1',
      align: 'center',
    },
    {
      title: '应还款日',
      key: 'examineRemark1',
      align: 'center',
    },
    {
      title: '应还金额-月供',
      key: 'status2',
      align: 'center',
    },
    {
      title: '应还本金',
      key: 'examineRemark2',
      align: 'center',
    },
    {
      title: '应还利息',
      key: 'status3',
      align: 'center',
    },
    {
      title: '应还罚息',
      key: 'examineRemark3',
      align: 'center',
    },
    {
      title: '其他费用',
      key: 'examineRemark4',
      align: 'center',
    },
    {
      title: '总应还金额',
      key: 'status4',
      align: 'center',
    },
    {
      title: '实还金额',
      key: 'examineRemark5',
      align: 'center',
    },
  ];
  const data1 = [
    {
      status: '待审核',
      examineRemark: '',
      status1: '待审核',
      examineRemark1: '',
      status2: '待审核',
      examineRemark2: '',
      status3: '待审核',
      examineRemark3: '',
      status4: '待审核',
      examineRemark4: '',
      status5: '待审核',
      examineRemark5: '',
    },
  ];
  function handleSearch() {
    searchLoading.value = true;
    setTimeout(() => {
      searchLoading.value = false;
    }, 5000);
  }
</script>

<style lang="less" scoped></style>
