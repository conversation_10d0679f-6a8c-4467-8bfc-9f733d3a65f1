<template>
  <n-image class="c-image" :src="src" :width="width" :height="height">
    <template #error>
      <n-icon :size="width" class="c-image-error" color="lightGrey">
        <ImageOutlineIcon />
      </n-icon>
    </template>
    <template #placeholder>
      <n-icon :size="width" class="c-image-error" color="lightGrey">
        <ImageOutlineIcon />
      </n-icon>
    </template>
  </n-image>
</template>
<script lang="ts" setup>
  import { defineProps } from 'vue';
  import { ImageOutline as ImageOutlineIcon } from '@vicons/ionicons5';

  defineProps({
    src: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
  });
</script>
<style lang="less" scoped>
  .c-image {
    .c-image-error {
      width: 100%;
    }
  }
</style>
