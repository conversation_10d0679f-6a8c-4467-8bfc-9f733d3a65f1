<template>
  <n-modal
    v-model:show="show"
    preset="card"
    v-bind="$attrs"
    :auto-focus="false"
    :mask-closable="false"
    :close-on-esc="false"
    :style="{ width: typeof width === 'number' ? `${width}px` : width }"
  >
    <!-- Body Content Slot -->
    <slot></slot>

    <!-- Footer Slot -->
    <template #footer>
      <slot name="footer">
        <n-space justify="end">
          <n-button @click="handleNegativeClick">{{ negativeText }}</n-button>
          <n-button type="primary" :loading="isLoading" @click="handlePositiveClick">
            {{ positiveText }}
          </n-button>
        </n-space>
      </slot>
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { NModal, NButton, NSpace } from 'naive-ui';

  const props = defineProps({
    width: {
      type: [String, Number],
      default: 500,
    },
    positiveText: {
      type: String,
      default: '确认',
    },
    negativeText: {
      type: String,
      default: '取消',
    },
    onConfirm: {
      type: Function as PropType<() => Promise<any>>,
      default: () => Promise.resolve(true),
    },
  });

  const show = defineModel('show', { type: Boolean, default: false });
  const isLoading = ref(false);

  async function handlePositiveClick() {
    isLoading.value = true;
    try {
      const result = await props.onConfirm();
      if (result !== false) {
        show.value = false;
      }
    } finally {
      isLoading.value = false;
    }
  }

  function handleNegativeClick() {
    show.value = false;
  }
</script>
