<template>
  <div class="title">
    <span class="mr-2 text-lg font-bold">{{ title }}</span>
    <n-tag v-if="status === 1" type="success" size="small">已完成</n-tag>
    <n-tag v-else-if="status === 2" type="info" size="small">进行中</n-tag>
    <n-tag v-else-if="status === 3" type="warning" size="small">未开始</n-tag>
  </div>
</template>

<script setup lang="ts">
  defineProps<{
    title: string;
    status: number;
  }>();
</script>

<style lang="less" scoped>
  .title {
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background-color: #2d8cf0;
      margin-right: 10px;
    }
  }
</style>
