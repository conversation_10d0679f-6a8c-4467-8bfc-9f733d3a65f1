<template>
  <BaseModal v-model:show="show" title="字段显示设置" :width="680" :show-footer="false">
    <div class="header-controls">
      <n-checkbox
        v-model:checked="allChecked"
        :indeterminate="isIndeterminate"
        @update:checked="handleCheckAll"
      >
        全选
      </n-checkbox>
      <n-button text type="primary" @click="resetFields">恢复默认</n-button>
    </div>

    <!-- 分组模式 -->
    <div v-if="grouping" class="groups-container">
      <div v-for="group in storedFieldGroups" :key="group.title" class="field-group">
        <h3 v-if="showGroupTitles" class="group-title">{{ group.title }}</h3>
        <transition-group name="flip-list" tag="div" class="field-list">
          <div
            v-for="(item, index) in group.fields"
            :key="item.key"
            class="draggable-item"
            :class="{ 'is-dragging': draggedItem === item }"
            draggable="true"
            @dragstart="onDragStart($event, index, group)"
            @dragover.prevent
            @dragenter.prevent="onDragEnter($event)"
            @dragleave="onDragLeave"
            @drop="onDrop($event, index, group)"
            @dragend="onDragEnd"
          >
            <div class="item-left">
              <n-icon class="handle" :size="20"><DragIndicatorFilled /></n-icon>
              <n-tag type="info" size="small" class="order-tag">{{ index + 1 }}</n-tag>
            </div>
            <n-checkbox v-model:checked="item.visible" :label="item.label" />
          </div>
        </transition-group>
      </div>
    </div>

    <!-- 平铺模式 -->
    <div v-else class="groups-container">
      <transition-group name="flip-list" tag="div" class="field-list">
        <div
          v-for="(item, index) in flatFieldList"
          :key="item.key"
          class="draggable-item"
          :class="{ 'is-dragging': draggedItem === item }"
          draggable="true"
          @dragstart="onFlatDragStart($event, item)"
          @dragover.prevent
          @dragenter.prevent="onDragEnter($event)"
          @dragleave="onDragLeave"
          @drop="onFlatDrop($event, index)"
          @dragend="onDragEnd"
        >
          <div class="item-left">
            <n-icon class="handle" :size="20"><DragIndicatorFilled /></n-icon>
            <n-tag type="info" size="small" class="order-tag">{{ index + 1 }}</n-tag>
          </div>
          <n-checkbox v-model:checked="item.visible" :label="item.label" />
        </div>
      </transition-group>
    </div>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { NCheckbox, NButton, NIcon, NTag, useMessage } from 'naive-ui';
  import { DragIndicatorFilled } from '@vicons/material';
  import { BaseModal } from '@/components/Modal';
  import { useFieldControl } from './useFieldControl';
  import type { FieldGroup, StoredGroupState, StoredFieldState } from './types';

  const props = withDefaults(
    defineProps<{
      storageKey: string; // 用于本地存储的键名
      initialConfigs: FieldGroup[]; // 初始字段分组配置
      allowCrossGroupDrag?: boolean; // 是否允许跨分组拖拽
      grouping?: boolean; // 是否使用分组模式
    }>(),
    {
      allowCrossGroupDrag: false,
      grouping: true,
    }
  );

  const show = defineModel<boolean>('show');
  const message = useMessage();

  const { storedFieldGroups, resetFields } = useFieldControl(
    props.storageKey,
    props.initialConfigs
  );

  const showGroupTitles = computed(() => props.grouping && storedFieldGroups.value?.length > 1);

  const flatFieldList = computed(() => {
    return storedFieldGroups.value?.flatMap((g) => g.fields || []) || [];
  });

  const draggedItem = ref<StoredFieldState | null>(null);
  const draggedIndex = ref<number>(-1);
  const draggedGroup = ref<StoredGroupState | null>(null);

  const allChecked = computed({
    get: () => {
      if (!Array.isArray(storedFieldGroups.value)) return false;
      return storedFieldGroups.value.every(
        (group) => Array.isArray(group.fields) && group.fields.every((item) => item.visible)
      );
    },
    set: (value: boolean) => {
      if (!Array.isArray(storedFieldGroups.value)) return;
      storedFieldGroups.value.forEach((group) => {
        if (Array.isArray(group.fields)) {
          group.fields.forEach((item) => {
            item.visible = value;
          });
        }
      });
    },
  });

  const isIndeterminate = computed(() => {
    if (!Array.isArray(storedFieldGroups.value)) return false;
    return (
      !allChecked.value &&
      storedFieldGroups.value.some(
        (group) => Array.isArray(group.fields) && group.fields.some((item) => item.visible)
      )
    );
  });

  function handleCheckAll(checked: boolean) {
    allChecked.value = checked;
  }

  function onDragEnter(event: DragEvent) {
    const target = event.currentTarget as HTMLElement;
    if (target) {
      target.classList.add('drag-over');
    }
  }

  function onDragLeave(event: DragEvent) {
    const target = event.currentTarget as HTMLElement;
    if (target) {
      target.classList.remove('drag-over');
    }
  }

  function onDragStart(event: DragEvent, index: number, group: StoredGroupState) {
    draggedItem.value = group.fields[index];
    draggedIndex.value = index;
    draggedGroup.value = group;
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.dropEffect = 'move';
    }
  }

  function onDrop(event: DragEvent, dropIndex: number, group: StoredGroupState) {
    const target = event.currentTarget as HTMLElement;
    if (target) target.classList.remove('drag-over');
    event.preventDefault();

    if (!draggedGroup.value || !draggedItem.value) return;

    const isSameGroup = draggedGroup.value.title === group.title;

    if (!isSameGroup) {
      if (!props.allowCrossGroupDrag) {
        message.warning('不能跨分组操作');
        return;
      }
      draggedGroup.value.fields.splice(draggedIndex.value, 1);
      group.fields.splice(dropIndex, 0, draggedItem.value);
    } else {
      if (draggedIndex.value !== dropIndex) {
        const fields = group.fields;
        const item = fields.splice(draggedIndex.value, 1)[0];
        fields.splice(dropIndex, 0, item);
      }
    }
  }

  function onFlatDragStart(event: DragEvent, item: StoredFieldState) {
    draggedItem.value = item;
    draggedGroup.value = storedFieldGroups.value.find((g) => g.fields.includes(item)) || null;
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.dropEffect = 'move';
    }
  }

  function onFlatDrop(event: DragEvent, dropIndex: number) {
    const target = event.currentTarget as HTMLElement;
    if (target) target.classList.remove('drag-over');
    event.preventDefault();

    if (!draggedGroup.value || !draggedItem.value) return;

    const originalFieldIndex = draggedGroup.value.fields.findIndex(
      (f) => f.key === draggedItem.value!.key
    );
    if (originalFieldIndex === -1) return;

    // 从原分组中移除
    const itemToMove = draggedGroup.value.fields.splice(originalFieldIndex, 1)[0];

    // 定位目标位置并插入
    const targetItem = flatFieldList.value[dropIndex];
    if (targetItem) {
      const targetGroup =
        storedFieldGroups.value.find((g) => g.fields.some((f) => f.key === targetItem.key)) || null;
      if (targetGroup) {
        const targetIndexInGroup = targetGroup.fields.findIndex((f) => f.key === targetItem.key);
        targetGroup.fields.splice(targetIndexInGroup, 0, itemToMove);
      }
    } else {
      // 如果是拖拽到末尾，则添加到最后一个分组
      const lastGroup = storedFieldGroups.value[storedFieldGroups.value.length - 1];
      lastGroup.fields.push(itemToMove);
    }
  }

  function onDragEnd() {
    draggedItem.value = null;
    draggedIndex.value = -1;
    draggedGroup.value = null;
  }
</script>

<style scoped>
  .header-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .groups-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .group-title {
    font-weight: bold;
    margin-bottom: 12px;
    font-size: 16px;
  }

  .field-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 16px;
    position: relative;
  }

  .draggable-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fff;
    cursor: move;
    user-select: none;
    transition: all 0.2s;
  }

  .draggable-item.is-dragging {
    opacity: 0.5;
    background: #c8ebfb;
  }

  .draggable-item.drag-over {
    border: 1px dashed #2080f0;
    background-color: #f5f7fa;
  }

  .item-left {
    display: flex;
    align-items: center;
  }

  .handle {
    margin-right: 8px;
  }

  .order-tag {
    margin-right: 8px;
  }

  /* transition-group 动画 */
  .flip-list-move {
    transition: transform 0.2s;
  }

  .flip-list-enter-active,
  .flip-list-leave-active {
    transition: all 0.2s;
  }

  .flip-list-enter-from,
  .flip-list-leave-to {
    opacity: 0;
    transform: translateY(30px);
  }
</style>
