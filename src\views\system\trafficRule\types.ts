// 流量分配规则类型定义

export interface TrafficRule {
  configId: number; // 配置id
  ruleName: string; // 规则名称
  remark?: string; // 规则备注
  mediaPlatformSource: string; // 分配媒体
  roleIds: number[]; // 角色ID数组
  createBy: string; // 添加人
  createTime: string; // 添加时间
  updateBy: string; // 更新人
  updateTime: string; // 更新时间
  status: number; // 状态 (EnableDisable)
  isDefault?: number; // 是否为默认配置 (isDefault为1定义为默认配置且固定在顶部，0为非默认)
  rules?: AssignRule[]; // 分配规则数组
}

export interface AssignRule {
  sourceId: string; // 线索来源
  mediaPlatformSource: string; // 来源媒体
  platformId?: string | null; // 平台（第三级）
  distributeRatio: number | null; // 分配比例（%）
}

export interface TrafficRuleForm {
  configId?: number; // 编辑时传入的ID (对应configId)
  ruleName: string; // 规则名称
  remark?: string; // 规则备注
  status: number; // 状态：1-启用，0-禁用
  roleIds: number[]; // 分配角色
  assignMedia: string[][]; // 分配媒体 (二维数组)
  rules: AssignRule[]; // 分配规则数组
}

export interface MediaTreeNode {
  value: string;
  label: string;
  children?: MediaTreeNode[];
  disabled?: boolean;
}

export interface RoleOption {
  id: number;
  name: string;
  roleCode: string;
}

export interface SourceOption {
  sourceId: string;
  sourceName: string;
}
