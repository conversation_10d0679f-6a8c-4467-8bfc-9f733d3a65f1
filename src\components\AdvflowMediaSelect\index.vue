<template>
  <NSelect
    :value="modelValue"
    :options="options"
    :placeholder="placeholder"
    :multiple="multiple"
    :filterable="filterable"
    :clearable="clearable"
    @update:value="$emit('update:modelValue', $event)"
  />
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import { NSelect } from 'naive-ui';
  import { useAdvflowMediaOptions } from '@/components/AdvflowMediaSelect/useAdvflowMediaOptions';

  interface Props {
    modelValue?: string | string[];
    placeholder?: string;
    multiple?: boolean;
    filterable?: boolean;
    clearable?: boolean;
  }

  withDefaults(defineProps<Props>(), {
    placeholder: '请选择投放平台',
    multiple: false,
    filterable: true,
    clearable: true,
  });

  defineEmits<{
    'update:modelValue': [value: string | string[] | undefined];
  }>();

  const { advflowMediaOptions, loadOptions } = useAdvflowMediaOptions();

  const options = computed(() =>
    advflowMediaOptions.value.map((item) => ({
      label: item.platformName,
      value: item.platformCode,
    }))
  );

  onMounted(() => {
    loadOptions();
  });
</script>
