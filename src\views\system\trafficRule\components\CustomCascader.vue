<template>
  <div class="custom-cascader">
    <n-input readonly :value="displayValue" :placeholder="placeholder" @click="toggleDropdown">
      <template #suffix>
        <n-icon size="14">
          <DownOutlined />
        </n-icon>
      </template>
    </n-input>

    <div v-if="showDropdown" class="cascader-dropdown" :style="{ zIndex: props.zIndex }">
      <div class="cascader-panel" :style="{ width: panelWidth + 'px' }">
        <!-- 第一层 -->
        <div class="cascader-column">
          <!-- <div class="cascader-column-header">请选择</div> -->
          <div class="cascader-options">
            <div
              v-for="option in options"
              :key="option.value"
              class="cascader-option"
              :class="{
                'is-selected': isNodeSelected(option.value, []),
                'is-indeterminate': isNodeIndeterminate(option.value, []),
                'is-active': activeNode === option.value,
                'is-disabled': option.disabled,
              }"
              @click="handleNodeClick(option, [])"
              @mouseenter="handleNodeHover(option, [])"
            >
              <n-checkbox
                :checked="isNodeSelected(option.value, [])"
                :indeterminate="isNodeIndeterminate(option.value, [])"
                :disabled="option.disabled"
                @click.stop="handleCheckboxClick(option, [])"
              />
              <span class="option-label">{{ option.label }}</span>
              <n-icon
                v-if="option.children && option.children.length > 0"
                class="expand-icon"
                @click.stop="handleNodeClick(option, [])"
              >
                <RightOutlined />
              </n-icon>
            </div>
          </div>
        </div>

        <!-- 动态层级 -->
        <div
          v-for="(level, index) in activeLevels"
          :key="`level-${index}`"
          class="cascader-column"
          v-show="activeLevels.length > 0"
        >
          <!-- 第三级（platform）模糊搜索 -->
          <div v-if="level.parentPath.length === 2" class="cascader-column-search">
            <n-input
              v-model:value="thirdLevelSearch"
              size="small"
              clearable
              placeholder="搜索投放平台"
              @keydown.stop
            />
          </div>
          <div class="cascader-options">
            <div
              v-for="option in getFilteredOptions(level)"
              :key="option.value"
              class="cascader-option"
              :class="{
                'is-selected': isNodeSelected(option.value, level.parentPath),
                'is-indeterminate': isNodeIndeterminate(option.value, level.parentPath),
                'is-active': activeNode === option.value,
                'is-disabled': option.disabled,
              }"
              @click="handleNodeClick(option, level.parentPath)"
              @mouseenter="handleNodeHover(option, level.parentPath)"
            >
              <n-checkbox
                :checked="isNodeSelected(option.value, level.parentPath)"
                :indeterminate="isNodeIndeterminate(option.value, level.parentPath)"
                :disabled="option.disabled"
                @click.stop="handleCheckboxClick(option, level.parentPath)"
              />
              <span class="option-label">{{ option.label }}</span>
              <n-icon
                v-if="option.children && option.children.length > 0"
                class="expand-icon"
                @click.stop="handleNodeClick(option, level.parentPath)"
              >
                <RightOutlined />
              </n-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
  import { DownOutlined, RightOutlined } from '@vicons/antd';

  interface CascaderOption {
    value: string;
    label: string;
    children?: CascaderOption[];
    disabled?: boolean;
  }

  interface Props {
    value?: string[][];
    options: CascaderOption[];
    placeholder?: string;
    multiple?: boolean;
    zIndex?: number;
  }

  interface Emits {
    (e: 'update:value', value: string[][]): void;
    (e: 'blocked-parent-click'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择',
    multiple: true,
    zIndex: 3000,
  });

  const emit = defineEmits<Emits>();

  const showDropdown = ref(false);
  const selectedPaths = ref<string[][]>(props.value || []);
  const activeNode = ref<string>('');
  const activePath = ref<string[]>([]);

  // 动态层级
  const activeLevels = ref<
    Array<{
      parentLabel: string;
      parentPath: string[];
      options: CascaderOption[];
    }>
  >([]);

  // 第三级平台搜索关键字
  const thirdLevelSearch = ref('');

  // 显示值
  const displayValue = computed(() => {
    if (selectedPaths.value.length === 0) return '';

    // 显示所有选中的路径
    const labels = selectedPaths.value.map((path) => {
      return getPathLabels(path).join(' / ');
    });

    return labels.join(', ');
  });

  // 计算面板宽度
  const panelWidth = computed(() => {
    const baseWidth = 200; // 第一列宽度
    const columnCount = 1 + activeLevels.value.length; // 总列数
    const maxColumns = 3; // 最大显示列数
    const actualColumns = Math.min(columnCount, maxColumns);
    return actualColumns * baseWidth;
  });

  // 获取路径标签
  function getPathLabels(path: string[]): string[] {
    const labels: string[] = [];
    let current = props.options;

    for (const value of path) {
      const option = current.find((opt) => opt.value === value);
      if (option) {
        labels.push(option.label);
        current = option.children || [];
      }
    }

    return labels;
  }

  // 检查节点是否被选中（需要完整路径匹配）
  function isNodeSelected(value: string, parentPath: string[] = []): boolean {
    const currentPath = [...parentPath, value];

    // 首先检查是否在当前选中的路径中
    const isInSelectedPaths = selectedPaths.value.some((path) => arraysEqual(path, currentPath));

    // 如果不在当前选中路径中，检查是否是被禁用的选项（表示已经在其他地方被选择）
    if (!isInSelectedPaths) {
      const option = findOptionByPath(currentPath, props.options);
      if (option && option.disabled) {
        return true; // 被禁用的选项应该显示为已选中
      }
    }

    return isInSelectedPaths;
  }

  // 检查节点是否半选中（有子级被选中但自身未被选中）
  function isNodeIndeterminate(value: string, parentPath: string[] = []): boolean {
    const currentPath = [...parentPath, value];

    // 如果当前节点已被选中，则不是半选状态
    if (isNodeSelected(value, parentPath)) {
      return false;
    }

    // 检查是否有子级被选中（包括当前选中的和禁用的）
    const currentPathStr = currentPath.join('/');

    // 检查当前选中的路径
    const hasSelectedChildren = selectedPaths.value.some((path) => {
      const pathStr = path.join('/');
      return pathStr.startsWith(currentPathStr + '/');
    });

    if (hasSelectedChildren) {
      return true;
    }

    // 检查禁用的子选项（递归检查所有子级）
    const option = findOptionByPath(currentPath, props.options);
    if (option && option.children) {
      return hasDisabledChildren(option.children, currentPath);
    }

    return false;
  }

  // 检查是否有禁用的子级选项
  function hasDisabledChildren(children: CascaderOption[], parentPath: string[]): boolean {
    for (const child of children) {
      const childPath = [...parentPath, child.value];

      // 如果子级被禁用，说明有子级被选中
      if (child.disabled) {
        return true;
      }

      // 递归检查更深层的子级
      if (child.children && hasDisabledChildren(child.children, childPath)) {
        return true;
      }
    }
    return false;
  }

  // 处理节点悬停
  function handleNodeHover(option: CascaderOption, _parentPath: string[]) {
    // 如果选项被禁用，不响应悬停
    if (option.disabled) {
      return;
    }
    // 悬停时只设置激活状态，不展开
    activeNode.value = option.value;
  }

  // 处理节点点击
  function handleNodeClick(option: CascaderOption, parentPath: string[]) {
    // 如果选项被禁用，不响应点击
    if (option.disabled) {
      return;
    }

    const currentPath = [...parentPath, option.value];

    // 点击时展开子级
    if (option.children && option.children.length > 0) {
      updateActiveLevels(currentPath, option);
    } else {
      // 如果是叶子节点，直接选中
      handleCheckboxClick(option, parentPath);
    }
  }

  // 处理复选框点击
  function handleCheckboxClick(option: CascaderOption, parentPath: string[]) {
    // 如果选项被禁用，不允许操作
    if (option.disabled) {
      return;
    }

    const currentPath = [...parentPath, option.value];

    if (isNodeSelected(option.value, parentPath)) {
      removeSelection(currentPath);
    } else {
      // 当当前节点是父级且存在已选中的子级时，拦截并通知外层
      const currentPathStr = currentPath.join('/');
      const hasSelectedDescendant = selectedPaths.value.some((p) =>
        p.join('/').startsWith(currentPathStr + '/')
      );
      const isParent = Array.isArray(option.children) && option.children.length > 0;
      if (isParent && hasSelectedDescendant) {
        emit('blocked-parent-click');
        return;
      }

      addSelection(currentPath);
    }

    // 立即触发更新事件
    const newValue = [...selectedPaths.value];
    emit('update:value', newValue);
  }

  // 更新活动层级
  function updateActiveLevels(path: string[], option: CascaderOption) {
    activePath.value = path;
    activeNode.value = option.value;

    // 清除当前层级之后的所有层级
    const currentLevel = path.length - 1;
    activeLevels.value = activeLevels.value.slice(0, currentLevel);

    // 添加新层级
    if (option.children && option.children.length > 0) {
      activeLevels.value.push({
        parentLabel: option.label,
        parentPath: path,
        options: option.children,
      });
    }
  }

  // 获取过滤后的（支持第三级模糊搜索）选项
  function getFilteredOptions(level: { parentPath: string[]; options: CascaderOption[] }) {
    if (level.parentPath.length === 2 && thirdLevelSearch.value.trim().length > 0) {
      const kw = thirdLevelSearch.value.trim().toLowerCase();
      return level.options.filter((opt) => opt.label.toLowerCase().includes(kw));
    }
    return level.options;
  }

  // 添加选择
  function addSelection(path: string[]) {
    // 移除已存在的相同路径
    selectedPaths.value = selectedPaths.value.filter((existing) => !arraysEqual(existing, path));

    // 只添加当前路径，不自动添加父级路径
    selectedPaths.value.push([...path]);
  }

  // 移除选择
  function removeSelection(path: string[]) {
    const pathStr = path.join('/');

    // 移除当前路径和所有子路径
    selectedPaths.value = selectedPaths.value.filter((existing) => {
      const existingStr = existing.join('/');
      return !existingStr.startsWith(pathStr);
    });
  }

  // 数组比较
  function arraysEqual(a: string[], b: string[]): boolean {
    return a.length === b.length && a.every((val, i) => val === b[i]);
  }

  // 根据路径查找选项
  function findOptionByPath(path: string[], options: CascaderOption[]): CascaderOption | null {
    if (path.length === 0) return null;

    let current = options;
    let option: CascaderOption | null = null;

    for (let i = 0; i < path.length; i++) {
      const value = path[i];
      option = current.find((opt) => opt.value === value) || null;

      if (!option) return null;

      if (i < path.length - 1) {
        current = option.children || [];
      }
    }

    return option;
  }

  // 切换下拉框
  function toggleDropdown() {
    showDropdown.value = !showDropdown.value;

    // 打开时重置状态，关闭时清空激活层级
    if (showDropdown.value) {
      activeNode.value = '';
      activePath.value = [];
      activeLevels.value = [];
      thirdLevelSearch.value = '';
    } else {
      activeLevels.value = [];
    }
  }

  // 点击外部关闭下拉框
  function handleClickOutside(event: Event) {
    const target = event.target as HTMLElement;

    // 查找最近的 custom-cascader 容器
    const cascader = target.closest('.custom-cascader');

    // 如果点击的不是当前组件内部，则关闭下拉框
    if (!cascader) {
      showDropdown.value = false;
    }
  }

  // 监听外部值变化
  watch(
    () => props.value,
    (newValue) => {
      if (newValue) {
        selectedPaths.value = [...newValue];
      }
    },
    { deep: true }
  );

  onMounted(() => {
    document.addEventListener('click', handleClickOutside);
  });

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
  });
</script>

<style scoped>
  .custom-cascader {
    position: relative;
    width: 100%;
  }

  .custom-cascader :deep(.n-input .n-input__input-el) {
    cursor: pointer !important;
  }

  .custom-cascader :deep(.n-input) {
    cursor: pointer !important;
  }

  .custom-cascader :deep(.n-input__input-el) {
    cursor: pointer !important;
  }

  .cascader-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e6;
    border-radius: 6px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
    max-height: 400px;
    overflow: hidden;
    margin-top: 4px;
    min-width: 200px;
    max-width: 600px;
  }

  .cascader-panel {
    display: flex;
    overflow-x: auto;
    max-width: 600px;
  }

  .cascader-column {
    flex: 0 0 200px;
    width: 200px;
    border-right: 1px solid #f0f0f0;
  }

  .cascader-column:last-child {
    border-right: none;
  }

  .cascader-column-header {
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }

  .cascader-options {
    max-height: 320px;
    overflow-y: auto;
  }

  /* 第三级搜索栏样式优化 */
  .cascader-column-search {
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fff;
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .cascader-column-search :deep(.n-input) {
    width: 100%;
  }

  .cascader-column-search :deep(.n-input .n-input__input-el) {
    font-size: 12px;
  }

  .cascader-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
  }

  .cascader-option:hover {
    background-color: #f5f5f5;
  }

  .cascader-option.is-active {
    background-color: #e6f7ff;
  }

  .cascader-option.is-selected {
    color: #1890ff;
  }

  .cascader-option.is-indeterminate {
    background-color: #f0f8ff;
  }

  .cascader-option.is-disabled {
    color: #ccc;
    cursor: not-allowed;
    background-color: #fafafa;
  }

  .cascader-option.is-disabled:hover {
    background-color: #fafafa;
  }

  .cascader-option.is-disabled .option-label {
    color: #ccc;
  }

  .cascader-option.is-disabled .expand-icon {
    color: #ddd;
  }

  .option-label {
    flex: 1;
    margin-left: 8px;
    font-size: 14px;
  }

  .expand-icon {
    color: #ccc;
    font-size: 12px;
    padding: 2px;
    border-radius: 2px;
    transition: all 0.2s;
  }

  .expand-icon:hover {
    background-color: #f0f0f0;
    color: #666;
  }

  .cascader-option.is-selected .expand-icon {
    color: #1890ff;
  }

  .cascader-option.is-active .expand-icon {
    color: #1890ff;
    background-color: #e6f7ff;
  }
</style>
