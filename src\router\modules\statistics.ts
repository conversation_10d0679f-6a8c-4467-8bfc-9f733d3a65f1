import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { OptionsSharp } from '@vicons/ionicons5';
import { renderIcon } from '@/utils/index';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/statistics',
    name: 'Statistics',
    redirect: '/statistics/seller-data-monitor',
    component: Layout,
    meta: {
      title: '统计总览',
      icon: renderIcon(OptionsSharp),
      sort: 1,
    },
    children: [
      {
        path: 'seller-data-monitor',
        name: 'statistics_seller_data_monitor',
        meta: {
          title: '销售数据监控',
          buttonsPreFix: 'statistics_seller_data_monitor_',
          buttons: [
            { key: 'export', name: '导出' },
            { key: 'detail', name: '渠道详情' },
          ],
          keepAlive: true,
        },
        component: () => import('@/views/statistics/sellerDataMonitor/index.vue'),
      },
      {
        path: 'seller-data-monitor-detail',
        name: 'statistics_seller_data_monitor_detail',
        meta: {
          title: '销售数据监控详情',
          buttonsPreFix: 'statistics_seller_data_monitor_detail_',
          buttons: [{ key: 'export', name: '导出' }],
          keepAlive: true,
        },
        component: () => import('@/views/statistics/sellerDataMonitor/detail.vue'),
      },
    ],
  },
];

export default routes;
