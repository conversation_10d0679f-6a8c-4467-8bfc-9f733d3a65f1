# VoiceControl 语音控制组件

一个功能完整的语音播放控制组件，支持音频文件播放、暂停、停止、音量控制、播放速度调节、继续播放等功能。

## 功能特性

- 🎵 **音频播放控制**: 播放、暂停、停止、重新开始
- 📁 **文件选择**: 支持本地音频文件选择和URL输入
- 🔊 **音量控制**: 可调节播放音量
- ⚡ **播放速度**: 支持0.5x到2x的播放速度调节
- 📊 **进度控制**: 可视化进度条，支持拖拽跳转
- 🔄 **继续播放**: 自动保存播放位置，支持从上次位置继续播放
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **现代化UI**: 基于Naive UI设计，美观易用
- 📝 **事件监听**: 完整的事件回调支持
- 🔧 **程序控制**: 支持通过ref调用组件方法

## 基础用法

```vue
<template>
  <VoiceControl />
</template>

<script setup>
import VoiceControl from '@/components/VoiceControl/index.vue'
</script>
```

## 预设音频源

```vue
<template>
  <VoiceControl src="https://example.com/audio.mp3" />
</template>
```

## 自动播放

```vue
<template>
  <VoiceControl 
    src="https://example.com/audio.mp3" 
    :autoplay="true" 
  />
</template>
```

## 继续播放功能

```vue
<template>
  <VoiceControl
    src="https://example.com/audio.mp3"
    :enablePlaybackPosition="true"
    @playbackPositionSaved="onPositionSaved"
    @playbackPositionCleared="onPositionCleared"
  />
</template>

<script setup>
const onPositionSaved = (position) => {
  console.log('播放位置已保存:', position)
}

const onPositionCleared = () => {
  console.log('播放位置已清除')
}
</script>
```

## 事件监听

```vue
<template>
  <VoiceControl
    src="https://example.com/audio.mp3"
    @play="onPlay"
    @pause="onPause"
    @stop="onStop"
    @ended="onEnded"
    @error="onError"
    @timeupdate="onTimeUpdate"
    @volumechange="onVolumeChange"
    @speedchange="onSpeedChange"
    @playbackPositionSaved="onPositionSaved"
    @playbackPositionCleared="onPositionCleared"
  />
</template>

<script setup>
const onPlay = () => {
  console.log('开始播放')
}

const onPause = () => {
  console.log('暂停播放')
}

const onStop = () => {
  console.log('停止播放')
}

const onEnded = () => {
  console.log('播放结束')
}

const onError = (error) => {
  console.log('播放错误:', error)
}

const onTimeUpdate = (currentTime, duration) => {
  console.log(`播放进度: ${currentTime}s / ${duration}s`)
}

const onVolumeChange = (volume) => {
  console.log(`音量变化: ${volume}`)
}

const onSpeedChange = (speed) => {
  console.log(`播放速度变化: ${speed}x`)
}

const onPositionSaved = (position) => {
  console.log(`播放位置已保存: ${position}s`)
}

const onPositionCleared = () => {
  console.log('播放位置已清除')
}
</script>
```

## 程序控制

```vue
<template>
  <div>
    <VoiceControl ref="voiceControl" src="https://example.com/audio.mp3" />
    <n-space>
      <n-button @click="play">播放</n-button>
      <n-button @click="pause">暂停</n-button>
      <n-button @click="stop">停止</n-button>
      <n-button @click="restart">重新开始</n-button>
      <n-button @click="continuePlay">继续播放</n-button>
      <n-button @click="setVolume">设置音量</n-button>
      <n-button @click="setSpeed">设置速度</n-button>
      <n-button @click="savePosition">保存位置</n-button>
      <n-button @click="clearPosition">清除位置</n-button>
    </n-space>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const voiceControl = ref()

const play = () => {
  voiceControl.value?.play()
}

const pause = () => {
  voiceControl.value?.pause()
}

const stop = () => {
  voiceControl.value?.stop()
}

const restart = () => {
  voiceControl.value?.restart()
}

const continuePlay = () => {
  voiceControl.value?.continuePlay()
}

const setVolume = () => {
  voiceControl.value?.setVolume(0.5) // 设置音量为50%
}

const setSpeed = () => {
  voiceControl.value?.setSpeed(1.5) // 设置播放速度为1.5x
}

const savePosition = () => {
  voiceControl.value?.savePlaybackPosition()
}

const clearPosition = () => {
  voiceControl.value?.clearPlaybackPosition()
}
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| src | string | '' | 音频文件URL |
| autoplay | boolean | false | 是否自动播放 |
| loop | boolean | false | 是否循环播放 |
| preload | string | 'metadata' | 预加载策略 ('none' \| 'metadata' \| 'auto') |
| enablePlaybackPosition | boolean | true | 是否启用播放位置保存功能 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| play | - | 开始播放时触发 |
| pause | - | 暂停播放时触发 |
| stop | - | 停止播放时触发 |
| ended | - | 播放结束时触发 |
| error | error: string | 播放错误时触发 |
| timeupdate | currentTime: number, duration: number | 播放进度更新时触发 |
| volumechange | volume: number | 音量变化时触发 |
| speedchange | speed: number | 播放速度变化时触发 |
| playbackPositionSaved | position: number | 播放位置保存时触发 |
| playbackPositionCleared | - | 播放位置清除时触发 |

## Methods

通过ref可以调用以下方法：

| 方法名 | 参数 | 说明 |
|--------|------|------|
| play | - | 开始播放 |
| pause | - | 暂停播放 |
| stop | - | 停止播放 |
| restart | - | 重新开始播放 |
| continuePlay | - | 继续播放（从保存的位置） |
| setVolume | volume: number | 设置音量 (0-1) |
| setSpeed | speed: number | 设置播放速度 |
| seek | time: number | 跳转到指定时间 |
| savePlaybackPosition | - | 保存播放位置 |
| loadPlaybackPosition | - | 加载播放位置 |
| clearPlaybackPosition | - | 清除播放位置 |

## 继续播放功能

### 功能特性

- **自动保存**: 暂停或停止时自动保存当前播放位置
- **本地存储**: 播放位置保存在浏览器的localStorage中
- **文件识别**: 每个音频文件都有独立的播放位置记录
- **继续播放**: 点击"继续播放"按钮从上次位置继续
- **位置管理**: 可以查看和清除保存的播放位置

### 使用场景

- **长音频播放**: 适合播客、有声书等长音频内容
- **学习场景**: 语言学习、课程音频等需要断点续播的场景
- **工作场景**: 会议录音、培训音频等需要分次听完的内容

### 存储机制

- 使用localStorage存储播放位置
- 每个文件都有唯一的存储键
- 支持跨会话保存（关闭浏览器后重新打开仍可继续）
- 自动清理无效的存储数据

## 支持的音频格式

- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)
- AAC (.aac)
- M4A (.m4a)
- WebM (.webm)

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 注意事项

1. **自动播放限制**: 现代浏览器对自动播放有限制，需要用户交互后才能播放音频
2. **文件大小**: 建议音频文件大小控制在合理范围内，避免影响加载性能
3. **跨域问题**: 如果音频文件来自不同域名，需要确保服务器设置了正确的CORS头
4. **移动端**: 在移动设备上，音频播放可能受到系统音量控制影响
5. **存储限制**: localStorage有存储大小限制，建议不要保存过多的播放位置数据
6. **隐私模式**: 在隐私模式下，localStorage可能不可用，继续播放功能会受限

## 样式定制

组件使用BEM命名规范，可以通过CSS变量或覆盖样式来自定义外观：

```css
.voice-control {
  --primary-color: #18a058;
  --border-color: #e5e7eb;
  --background-color: #fff;
}
```

## 开发

```bash
# 查看演示
npm run dev

# 访问演示页面
http://localhost:3000/voice/index
``` 