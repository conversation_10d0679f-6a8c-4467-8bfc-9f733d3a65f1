// 系统管理 - 流量分配规则接口
import { get, post, put } from '@/utils/lib/axios.package';
import { get_admin_role_list } from './role';

// 获取角色列表 (使用现有接口)
export const getRoleListApi = get_admin_role_list;

// 获取分配媒体
export function getSourcesApi() {
  return get('/cms/volumeofflow/customize/sources');
}

// 获取流量分配规则列表
export function getTrafficRuleListApi(params: any) {
  return get('/cms/volumeofflow/customize/list', params);
}

// 新增非默认配置
export function addTrafficRuleApi(data: any) {
  return post('/cms/volumeofflow/customize/save', data);
}

// 编辑非默认配置
export function updateTrafficRuleApi(data: any) {
  return post('/cms/volumeofflow/customize/update', data);
}

// 编辑默认配置
export function updateDefaultRuleApi(data: any) {
  return post('/cms/volumeofflow/default/update', data);
}

// 删除流量分配规则
export function deleteTrafficRuleApi(configId: number) {
  return put(`/cms/volumeofflow/customize/delete/${configId}`);
}

// 更新规则状态
export function updateTrafficRuleStatusApi(status: number, configId: number) {
  return get('/cms/volumeofflow/customize/status', { status, configId });
}
