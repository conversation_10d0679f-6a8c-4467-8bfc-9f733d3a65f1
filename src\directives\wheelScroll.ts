import type { Directive } from 'vue';

const wheelScroll: Directive<HTMLElement> = {
  mounted(el: HTMLElement) {
    const handleWheel = (e: WheelEvent) => {
      if (el.scrollWidth > el.clientWidth) {
        e.preventDefault();
        el.scrollLeft += e.deltaY;
      }
    };

    el.addEventListener('wheel', handleWheel, { passive: false });

    (el as any).__vWheelScroll__ = handleWheel;
  },
  unmounted(el: HTMLElement) {
    const handleWheel = (el as any).__vWheelScroll__;
    if (handleWheel) {
      el.removeEventListener('wheel', handleWheel);
    }
  },
};

export default wheelScroll;
