<template>
  <div class="table-actions mb-10px">
    <n-button v-if="!isHighSeas" type="primary" @click="openUpdateFallUpModal">提交进件</n-button>
  </div>
  <n-data-table
    :columns="columns"
    :data="data"
    :pagination="{ pageSize: 15 }"
    :scroll-x="0"
    max-height="560px"
    :render-cell="renderCell"
  />
  <BaseModal
    v-model:show="showModal"
    title="更新订单状态"
    :width="600"
    positive-text="保存"
    :on-confirm="handleConfirm"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="managementCode" label="进件资方">
        <n-select
          v-model:value="model.managementCode"
          :options="submissionCapitalOptions"
          disabled
        />
      </n-form-item>
      <n-form-item path="businessOrderNode" label="订单节点">
        <n-select
          v-model:value="model.businessOrderNode"
          :options="orderNodeOptions"
          label-field="desc"
          value-field="code"
          @update:value="handleGetOrderNode"
        />
      </n-form-item>
      <n-form-item path="nodeStatus" label="订单状态">
        <n-select v-model:value="model.nodeStatus" :options="orderStatusOptions" />
      </n-form-item>
      <!-- 动态表单项 -->
      <template v-if="model.nodeStatus === orderStatusEnum.PASS">
        <!-- 预审 -->
        <template v-if="model.orderNode === orderNodeEnum.PRE_AUDIT">
          <n-form-item label="预审链接" path="ext.signUrl">
            <n-input v-model:value="model.ext.signUrl" placeholder="请输入">
              <!-- <template #prefix>https://</template> -->
            </n-input>
          </n-form-item>
        </template>
        <!-- 授信 -->
        <template v-if="model.orderNode === orderNodeEnum.CREDIT">
          <n-form-item label="进件产品" path="ext.productName">
            <n-input v-model:value="model.ext.productName" placeholder="请输入" />
          </n-form-item>
          <n-form-item label="授信金额" path="ext.creditLine">
            <n-input v-model:value="model.ext.creditLine" placeholder="请输入">
              <template #suffix>元</template>
            </n-input>
          </n-form-item>
          <n-form-item label="授信利率" path="ext.productRate">
            <n-input v-model:value="model.ext.productRate" placeholder="请输入">
              <template #suffix>%</template>
            </n-input>
          </n-form-item>
          <n-form-item label="授信期数" path="ext.term">
            <n-input v-model:value="model.ext.term" placeholder="请输入">
              <template #suffix>期</template>
            </n-input>
          </n-form-item>
        </template>
        <!-- 放款 -->
        <template v-if="model.orderNode === orderNodeEnum.LOAN">
          <n-form-item label="进件产品" path="ext.productName">
            <n-input v-model:value="model.ext.productName" placeholder="请输入" />
          </n-form-item>
          <n-form-item label="放款金额" path="ext.loanAmount">
            <n-input v-model:value="model.ext.loanAmount" placeholder="请输入">
              <template #suffix>元</template>
            </n-input>
          </n-form-item>
          <n-form-item label="月供" path="ext.monthlyPayment">
            <n-input v-model:value="model.ext.monthlyPayment" placeholder="请输入">
              <template #suffix>元</template>
            </n-input>
          </n-form-item>
        </template>
      </template>
      <n-form-item
        v-if="model.nodeStatus === orderStatusEnum.REJECT"
        path="remark"
        label="未通过原因"
      >
        <n-input
          v-model:value="model.remark"
          type="textarea"
          placeholder="请输入未通过原因"
          :autosize="{ minRows: 3 }"
          maxlength="200"
          show-count
        />
      </n-form-item>
    </n-form>
  </BaseModal>
  <BaseModal
    v-model:show="showSubmitModal"
    title="提交贷款进件"
    :width="400"
    positive-text="确认"
    :on-confirm="handleFallUpConfirm"
  >
    <n-form
      ref="fallUpFormRef"
      :model="fallUpModel"
      :rules="fallUpRules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="managementCode" label="进件资方">
        <n-select
          v-model:value="fallUpModel.managementCode"
          :options="showSubmissionCapitalOptions"
        />
      </n-form-item>
      <p class="text-gray-400 text-12px">(置灰资方：由于已存在该用户不可再次进件)</p>
    </n-form>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { h, ref, reactive, withDirectives, resolveDirective, computed } from 'vue';
  import {
    NDataTable,
    NButton,
    NForm,
    NFormItem,
    NSelect,
    NInput,
    NTime,
    type FormInst,
    type FormRules,
  } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import type { DataTableColumns } from 'naive-ui';
  import type { LoanOrderRecord, fallUpParams } from '@/api/detail';
  import {
    orderStatusEnum,
    orderNodeEnum,
    orderStatusOptions,
    submissionCapitalOptions,
    submissionCapital,
  } from '@/enums/detailEnum';
  import { addLoanApplyInfo, updateOrderStatus, getOrderNode } from '@/api/detail';
  import type { UpdateOrderStatusParams } from '@/api/detail';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  const isHighSeas = computed(() => route.name === 'HighSeas');

  const props = withDefaults(
    defineProps<{
      data: LoanOrderRecord[];
      clueInfoVo: any;
      managementCodes: string[];
    }>(),
    {}
  );

  const permission = resolveDirective('permission');
  interface OrderNodeOptionsType {
    code: string;
    desc: string;
    orderNodeCode: string;
    LoanCode: string;
  }
  const orderNodeOptions = ref([]);
  // const showOrderNodeOptions = computed(() => {
  //   return orderNodeOptions.value.map((item) => ({
  //     value: item.code,
  //     label: item.desc,
  //   }));
  // });
  const emit = defineEmits(['update-success']);
  const formRef = ref<FormInst | null>(null);

  const showModal = ref(false);
  const showSubmissionCapitalOptions = computed(() =>
    submissionCapitalOptions.map((item) => ({
      ...item,
      disabled: !(props.managementCodes?.filter((el) => el === String(item.value))?.length > 0),
    }))
  );
  // 初始表单状态
  const initialFormState: UpdateOrderStatusParams = {
    innerOrderNo: '',
    businessOrderNode: '',
    orderNode: '',
    nodeStatus: null,
    remark: '',
    managementCode: '',
    ext: {
      signUrl: undefined,
      productName: undefined,
      creditLine: undefined,
      productRate: undefined,
      term: undefined,
      loanAmount: undefined,
      monthlyPayment: undefined,
    },
  };

  const model = reactive<UpdateOrderStatusParams>({ ...initialFormState });

  const validateNumber = (fieldName: string) => {
    return (_rule, value) => {
      if (!value && value !== 0) {
        return new Error(`请输入${fieldName}`);
      }
      const num = Number(value);
      return !isNaN(num) ? true : new Error(`请输入有效数字`);
    };
  };

  const rules: FormRules = {
    businessOrderNode: {
      required: true,
      message: '请选择订单节点',
      trigger: 'change',
      type: 'any',
    },
    nodeStatus: { required: true, type: 'number', message: '请选择订单状态', trigger: 'change' },
    remark: {
      required: true,
      message: '请输入未通过原因',
      trigger: ['blur', 'input'],
    },
    'ext.signUrl': { required: true, message: '请输入预审链接', trigger: ['blur', 'input'] },
    'ext.productName': { required: true, message: '请输入进件产品', trigger: ['blur', 'input'] },
    'ext.creditLine': {
      required: true,
      message: '请输入授信金额',
      validator: validateNumber('授信金额'),
      trigger: ['blur', 'input'],
    },
    'ext.productRate': {
      required: true,
      message: '请输入授信利率',
      validator: validateNumber('授信利率'),
      trigger: ['blur', 'input'],
    },
    'ext.term': {
      required: true,
      message: '请输入授信期数',
      validator: validateNumber('授信期数'),
      trigger: ['blur', 'input'],
    },
    'ext.loanAmount': {
      required: true,
      message: '请输入放款金额',
      validator: validateNumber('放款金额'),
      trigger: ['blur', 'input'],
    },
    'ext.monthlyPayment': {
      required: true,
      message: '请输入月供',
      validator: validateNumber('月供'),
      trigger: ['blur', 'input'],
    },
  };

  async function openUpdateStatusModal(row: LoanOrderRecord) {
    // 重置表单
    Object.assign(model, JSON.parse(JSON.stringify(initialFormState)));
    // 填充表单
    model.managementCode = row.managementCode ? Number(row.managementCode) : '';
    model.innerOrderNo = row.innerOrderNo;
    model.businessOrderNode = row.businessOrderNode;
    model.orderNode = row.orderNode;
    model.nodeStatus = row.status;
    model.remark = row.failMessage;
    model.ext = {
      signUrl: row.signUrl,
      productName: row.productName,
      creditLine: row.creditLine,
      productRate: row.productRate,
      term: row.term,
      loanAmount: row.loanAmount,
      monthlyPayment: row.monthlyPayment,
    };
    try {
      let { data } = await getOrderNode(row.managementCode);
      orderNodeOptions.value = data || [];
    } catch (error) {}
    showModal.value = true;
  }

  // 订单跟进（德易专用）
  function openOrderFollowUpModal(row: LoanOrderRecord) {
    console.log('订单跟进:', row);
  }

  async function handleConfirm() {
    try {
      await formRef.value?.validate();
      let { orderNode, ...rest } = model;
      await updateOrderStatus(rest);

      window.$message.success('更新成功');
      emit('update-success');

      showModal.value = false;
    } catch (errors) {
      window.$message.error('请检查表单输入');
      return false;
    }
  }

  const createColumns = (): DataTableColumns<LoanOrderRecord> => [
    {
      //title: '订单ID'
      title: '渠道贷款订单ID',
      key: 'loanOrderId',
      ellipsis: {
        tooltip: true,
      },
      width: 130,
      align: 'center',
    },
    {
      title: '贷款订单ID',
      key: 'innerOrderNo',
      ellipsis: {
        tooltip: true,
      },
      width: 130,
      align: 'center',
    },
    {
      title: '订单节点',
      key: 'businessOrderNodeStr',
      width: 120,
      align: 'center',
      render(row) {
        return row.businessOrderNodeStr || '--';
      },
    },
    {
      title: '订单状态',
      key: 'status',
      width: 100,
      align: 'center',
      render(row) {
        return orderStatusOptions.find((opt) => opt.value === row.status)?.label || '--';
      },
    },
    {
      title: '进件资方',
      key: 'managementCode',
      width: 100,
      align: 'center',
      render: (row) =>
        submissionCapitalOptions.find((opt) => String(opt.value) === String(row.managementCode))
          ?.label || '-',
    },
    { title: '进件产品', key: 'productName', width: 100, align: 'center' },
    { title: '产品利率(%)', key: 'productRate', width: 110, align: 'center' },
    { title: '可贷金额(万)', key: 'loanAmount', width: 110, align: 'center' },
    {
      title: '授信金额(元)',
      key: 'creditLine',
      width: 120,
      align: 'center',
    },
    {
      title: '授信期数(期)',
      key: 'term',
      width: 120,
      align: 'center',
    },
    {
      title: '授信利率(%)',
      key: 'productRate',
      width: 120,
      align: 'center',
    },
    {
      title: '实际贷款金额(元)',
      key: 'loanAmount',
      width: 140,
      align: 'center',
    },
    {
      title: '月供(元)',
      key: 'monthlyPayment',
      width: 120,
      align: 'center',
    },
    { title: '未通过原因', key: 'failMessage', width: 120, align: 'center' },
    {
      title: '订单创建时间',
      key: 'createTime',
      width: 200,
      align: 'center',
      render(row) {
        return h(NTime, { time: new Date(row.createTime) });
      },
    },
    {
      title: '订单更新时间',
      key: 'updateTime',
      width: 200,
      align: 'center',
      render(row) {
        return h(NTime, { time: new Date(row.updateTime) });
      },
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 100,
      align: 'center',
      render(row) {
        // 判断是否显示按钮
        if (
          row.status === orderStatusEnum.REJECT ||
          (row.status === orderStatusEnum.PASS && row.orderNode === orderNodeEnum.LOAN)
        ) {
          return '';
        }

        // 判断进件资方是否是德易
        const isDeYi = String(row.managementCode) === String(submissionCapital.DE_SHUN);
        const buttonText = isDeYi ? '订单跟进' : '更新状态';
        const onClick = isDeYi
          ? () => openOrderFollowUpModal(row)
          : () => openUpdateStatusModal(row);

        return withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick,
            },
            { default: () => buttonText }
          ),
          [[permission, { action: `detail_status` }]]
        );
      },
    },
  ];

  const columns = createColumns();

  const renderCell = (value: any) => {
    if (!value) return '--';
    return value;
  };
  const showSubmitModal = ref(false);
  const fallUpFormRef = ref<FormInst | null>(null);
  // 初始表单状态
  const initialFallUpFormState: fallUpParams = {
    managementCode: '',
  };
  const fallUpModel = reactive<fallUpParams>({ ...initialFallUpFormState });

  const fallUpRules: FormRules = {
    managementCode: {
      required: true,
      message: '请选择进件资方',
      trigger: 'change',
      type: 'number',
    },
  };

  function openUpdateFallUpModal() {
    // 重置表单
    Object.assign(fallUpModel, JSON.parse(JSON.stringify(initialFallUpFormState)));
    showSubmitModal.value = true;
  }
  function handleGetOrderNode() {
    let options: OrderNodeOptionsType[] = [...orderNodeOptions.value];
    model.orderNode = options.find(
      (item: OrderNodeOptionsType) => String(item.code) === String(model.businessOrderNode)
    )?.orderNodeCode;
  }
  async function handleFallUpConfirm() {
    try {
      await fallUpFormRef.value?.validate();
      await addLoanApplyInfo(
        Object.assign({}, fallUpModel, {
          clueId: props.clueInfoVo.publicClueId,
        })
      );
      window.$message.success('更新成功');
      emit('update-success');
      showSubmitModal.value = false;
    } catch (errors) {
      window.$message.error('提交进件失败');
      return false;
    }
  }
</script>

<style scoped>
  .w-full {
    width: 100%;
  }
</style>
<style lang="less" scoped>
  .table-actions {
    margin: 0 0 10px;
  }
</style>
