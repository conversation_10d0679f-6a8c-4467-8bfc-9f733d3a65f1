<template>
  <NMenu
    :options="menus"
    :inverted="inverted"
    :mode="mode"
    :collapsed="collapsed"
    :collapsed-width="64"
    :collapsed-icon-size="20"
    :indent="22"
    :expanded-keys="openKeys"
    :value="getSelectedKeys"
    key-field="path"
    label-field="title"
    @update:value="clickMenuItem"
    @update:expanded-keys="menuExpanded"
  />
</template>

<script lang="ts">
  import {
    defineComponent,
    ref,
    onMounted,
    reactive,
    computed,
    watch,
    toRefs,
    unref,
    h,
  } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { generatorMenu, generatorMenuMix } from '@/utils';
  import { useProjectSettingStore } from '@/store/modules/projectSetting';
  import { useProjectSetting } from '@/hooks/setting/useProjectSetting';
  import { useUser } from '@/store/modules/user';
  import { formatMenu, sortMenu } from '@/utils';
  import RenderIcon from '@/components/RenderIcon/index.vue';
  import { cloneDeep } from 'lodash-es';

  export default defineComponent({
    name: 'AppMenu',
    components: {},
    props: {
      mode: {
        // 菜单模式
        type: String,
        default: 'vertical',
      },
      collapsed: {
        // 侧边栏菜单是否收起
        type: Boolean,
      },
      //位置
      location: {
        type: String,
        default: 'left',
      },
    },
    emits: ['update:collapsed', 'clickMenuItem'],
    setup(props, { emit }) {
      // 当前路由
      const currentRoute = useRoute();
      const router = useRouter();
      const settingStore = useProjectSettingStore();
      const menus = ref<any[]>([]);
      const selectedKeys = ref<string>(currentRoute.path as string);
      const headerMenuSelectKey = ref<string>('');
      const userStore = useUser();

      const { navMode } = useProjectSetting();
      const checkMenu = ref<number | null>(null);
      // 获取当前打开的子菜单
      const matched = currentRoute.matched;

      const getOpenKeys = matched && matched.length ? matched.map((item) => item.path) : [];

      const state = reactive({
        openKeys: getOpenKeys,
      });

      const inverted = computed(() => {
        return ['dark', 'header-dark'].includes(settingStore.navTheme);
      });

      const getSelectedKeys = computed(() => {
        let location = props.location;
        return location === 'left' || (location === 'header' && unref(navMode) === 'horizontal')
          ? unref(selectedKeys)
          : unref(headerMenuSelectKey);
      });

      // 监听分割菜单
      watch(
        () => settingStore.menuSetting.mixMenu,
        () => {
          updateMenu();
          if (props.collapsed) {
            emit('update:collapsed', !props.collapsed);
          }
        }
      );
      // 跟随页面路由变化，切换菜单选中状态
      watch(
        () => currentRoute.fullPath,
        () => {
          updateMenu();
        }
      );

      function updateSelectedKeys() {
        const matched = currentRoute.matched;
        state.openKeys = matched.map((item) => item.path);
        const activeMenu: string = (currentRoute.meta?.activeMenu as string) || '';
        selectedKeys.value = activeMenu ? (activeMenu as string) : (currentRoute.path as string);
      }

      async function updateMenu() {
        let menusPermission = userStore.getMenus;
        if (!menusPermission || menusPermission.length === 0) {
          let res = await userStore.getInfo();
          menusPermission = res?.menus ?? [];
        }
        if (!settingStore.menuSetting.mixMenu) {
          menus.value = menusPermission;
        } else {
          //混合菜单
          const firstRouteName: string = (currentRoute.matched[0].path as string) || '';
          menus.value = generatorMenuMix(menusPermission, firstRouteName, props.location);
          const activeMenu: string = currentRoute?.matched[0].meta?.activeMenu as string;
          headerMenuSelectKey.value = (activeMenu ? activeMenu : firstRouteName) || '';
        }
        menus.value = sortMenu(handleOptions(menus.value));
        updateSelectedKeys();
      }
      function handleOptions(list) {
        const copyList = cloneDeep(list);
        let menus = copyList.reduce((pre, cur) => {
          if (!cur.hiddenInMenu) {
            let icon = cur.icon;
            !cur.icon && delete cur.icon;
            cur.icon && typeof cur.icon === 'string' && (cur.icon = () => h(RenderIcon, { icon }));
            if (cur.children) {
              cur.children = handleOptions(cur.children);
            }
            pre.push(cur);
          }
          return pre;
        }, []);
        menus = formatMenu(menus);
        return menus;
      }
      // 点击菜单
      function clickMenuItem(key: string, item) {
        let { path } = item;
        if (/http(s)?:/.test(path)) {
          window.open(path);
        } else {
          router.push({ path });
        }
        emit('clickMenuItem' as any, path);
      }

      //展开菜单
      function menuExpanded(openKeys: string[]) {
        if (!openKeys) return;
        const latestOpenKey = openKeys.find((key) => state.openKeys.indexOf(key) === -1);
        const isExistChildren = findChildrenLen(latestOpenKey as string);
        state.openKeys = isExistChildren ? (latestOpenKey ? [latestOpenKey] : []) : openKeys;
      }

      //查找是否存在子路由
      function findChildrenLen(key: string) {
        if (!key) return false;
        const subRouteChildren: string[] = [];
        for (const { children, key } of unref(menus)) {
          if (children && children.length) {
            subRouteChildren.push(key as string);
          }
        }
        return subRouteChildren.includes(key);
      }
      onMounted(() => {
        updateMenu();
      });

      return {
        ...toRefs(state),
        inverted,
        menus,
        selectedKeys,
        headerMenuSelectKey,
        getSelectedKeys,
        clickMenuItem,
        menuExpanded,
        checkMenu,
      };
    },
  });
</script>
