<template>
  <FormDialog
    v-model="formDialogModel"
    title="移交线索"
    :form-model="formModel"
    :rules="rules"
    label-width="110px"
    @submit="handleSubmit"
    @close="handleClose"
  >
    <n-form-item label="移交人部门：" path="deptId">
      <n-tree-select
        v-model:value="formModel.deptId"
        label-field="branchName"
        key-field="id"
        children-field="childrenBranch"
        :options="deptTree"
        placeholder="请选择移交人部门"
        @update:value="handleParentBranchChange"
      />
    </n-form-item>
    <n-form-item label="移交人：" path="extendUserId">
      <n-select
        v-model:value="formModel.extendUserId"
        label-field="username"
        value-field="id"
        placeholder="请选择移交人"
        :options="userOptions"
      />
    </n-form-item>
  </FormDialog>
</template>

<script lang="ts" setup>
  import FormDialog from '@/components/FormDialog/index.vue';
  import { reactive, ref } from 'vue';
  import { getSystemUserListApi, transferClueApi } from '@/api/system';
  import rules from './rules';

  const props = defineProps({
    deptTree: {
      type: Array,
      default: () => [],
    },
    ids: {
      type: Array,
      default: () => [],
    },
    // 原有用户ID
    originalUserId: {
      type: Number,
      default: null,
    },
  });

  const userOptions = ref([]);
  const formState = {
    // 部门id，不需要传
    deptId: null,
    // 原来的ID
    originalUserId: null,
    // 继承人ID
    extendUserId: null,
  };
  const formModel = reactive({ ...formState });
  const formDialogModel = ref(false);
  const emits = defineEmits(['close', 'submit-success']);
  const handleSubmit = async (params, done) => {
    try {
      // 处理参数
      const handleParams = { ...params };
      handleParams.ids = props.ids;
      handleParams.originalUserId = props.originalUserId;
      await transferClueApi(handleParams);
      window.$message.success('移交成功');
      formDialogModel.value = false;
      emits('submit-success');
    } catch (err) {
      console.log(err);
    } finally {
      done();
    }
  };
  const handleClose = () => {
    Object.assign(formModel, formState);
    emits('close');
  };
  const handleParentBranchChange = (e) => {
    getSystemUserList(e);
  };
  const getSystemUserList = async (branchId) => {
    const { data } = await getSystemUserListApi({
      branchId,
      pageNumber: 1,
      pageSize: 9999,
    });
    userOptions.value = data?.records?.filter(
      (item) => item.id !== props.originalUserId && item.locked === 0
    );
  };

  defineExpose({
    formDialogModel,
  });
</script>
