<template>
  <n-card :bordered="false">
    <BasicForm
      :enable-cache="true"
      @register="register"
      @submit="reloadTable"
      @reset="reloadTable()"
    >
      <template #actionButton>
        <n-button
          v-permission="{ action: 'export' }"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </n-button>
      </template>
    </BasicForm>
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="tableColumns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      :actionColumn="actionColumn"
      :scroll-x="0"
      ref="action"
      :striped="true"
    />
  </n-card>
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { useDateOptions } from '@/composables/useDateOptions';
  import { reactive, useTemplateRef, ref, onMounted, computed, onActivated } from 'vue';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import { useDrawerOrder } from '@/composables/useDrawerOrder';
  defineOptions({
    name: 'DashboardOrderManage',
  });
  const exportLoading = ref(false);
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  const { open: openDrawerOrder } = useDrawerOrder();
  const schemas = computed<FormSchema[]>(() => {
    return [
      {
        field: 'loanOrderId',
        component: 'NInput',
        label: '贷款订单ID',
        componentProps: {
          placeholder: '请输入贷款订单ID',
        },
      },
      {
        field: 'channelLoanOrderId',
        component: 'NInput',
        label: '渠道贷款订单ID',
        componentProps: {
          placeholder: '请输入渠道贷款订单ID',
        },
      },
      {
        field: 'customerName',
        component: 'NInput',
        label: '客户姓名',
        componentProps: {
          placeholder: '请输入客户姓名',
        },
      },
      {
        field: 'contactPhone',
        component: 'NInput',
        label: '联系电话',
        componentProps: {
          placeholder: '请输入联系电话',
          showButton: false,
          maxlength: 11,
          onInput: () => {
            const { contactPhone } = getFieldsValue();
            const formattedValue = contactPhone.replace(/\D/g, '');
            if (contactPhone !== formattedValue) {
              setFieldsValue({ contactPhone: formattedValue });
            }
          },
        },
      },
      {
        field: 'capitalProvider',
        component: 'NSelect',
        label: '进件资方',
        componentProps: {
          placeholder: '请选择进件资方',
          options: [],
          clearable: true,
        },
      },
      {
        field: 'productInfo',
        component: 'NSelect',
        label: '产品信息',
        componentProps: {
          placeholder: '请选择产品信息',
          options: [],
          clearable: true,
        },
      },
      {
        field: 'loanAmount',
        component: 'NInput',
        label: '贷款金额',
        componentProps: {
          placeholder: '请输入贷款金额',
        },
      },
      {
        field: 'loanPeriod',
        component: 'NSelect',
        label: '贷款期限',
        componentProps: {
          placeholder: '请选择贷款期限（月）',
          options: [
            { label: '12个月', value: 12 },
            { label: '24个月', value: 24 },
            { label: '36个月', value: 36 },
            { label: '48个月', value: 48 },
            { label: '60个月', value: 60 },
          ],
          clearable: true,
        },
      },
      {
        field: 'orderStatus',
        component: 'NSelect',
        label: '订单状态',
        defaultValue: '1',
        componentProps: {
          placeholder: '请选择订单状态',
          options: [
            { label: '跟进中', value: '1' },
            { label: '订单取消', value: '2' },
            { label: '订单完结', value: '3' },
          ],
          clearable: true,
        },
      },
      {
        field: 'orderNode',
        component: 'NSelect',
        label: '订单节点',
        componentProps: {
          placeholder: '请选择订单节点',
          options: [
            { label: '预审阶段', value: '1' },
            { label: '进件签约', value: '2' },
            { label: '后置阶段', value: '3' },
            { label: '放款阶段', value: '4' },
          ],
          clearable: true,
        },
      },
      {
        field: 'orderSubNode',
        component: 'NSelect',
        label: '订单子节点',
        componentProps: {
          placeholder: '请选择订单子节点',
          options: [],
          clearable: true,
        },
      },
      {
        field: 'nodeStatus',
        component: 'NSelect',
        label: '节点状态',
        componentProps: {
          placeholder: '请选择节点状态',
          options: [],
          clearable: true,
        },
      },
      {
        field: 'gpsInstallStatus',
        component: 'NSelect',
        label: 'GPS安装节点',
        componentProps: {
          placeholder: '请选择GPS安装节点',
          options: [],
          clearable: true,
        },
      },
      {
        field: 'faceSignStatus',
        component: 'NSelect',
        label: '面签节点状态',
        componentProps: {
          placeholder: '请选择面签节点状态',
          options: [],
          clearable: true,
        },
      },
      {
        field: 'mortgageStatus',
        component: 'NSelect',
        label: '抵押节点状态',
        componentProps: {
          placeholder: '请选择抵押节点状态',
          options: [],
          clearable: true,
        },
      },
      {
        field: 'orderStartTimeQuery',
        label: '创建订单时间',
        component: 'NDatePicker',
        childKey: ['orderStartTimeBegin', 'orderStartTimeEnd'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.orderStartTimeQuery,
        },
        noHidden: multiDateNoHidden.value.orderStartTimeQuery,
      },
      {
        field: 'lastFollowTimeQuery',
        label: '最近跟进时间',
        component: 'NDatePicker',
        childKey: ['lastFollowTimeBegin', 'lastFollowTimeEnd'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.lastFollowTimeQuery,
        },
        noHidden: multiDateNoHidden.value.lastFollowTimeQuery,
      },
      {
        field: 'orderEndTimeQuery',
        label: '订单结束时间',
        component: 'NDatePicker',
        childKey: ['orderEndTimeBegin', 'orderEndTimeEnd'],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: multiDateClearable.value.orderEndTimeQuery,
        },
        noHidden: multiDateNoHidden.value.orderEndTimeQuery,
      },
    ];
  });

  const actionColumn = reactive({
    width: 180,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render() {
      return (
        <n-space>
          <n-button v-permission={{ action: 'follow_up' }} type="primary" text onClick={() => {}}>
            跟进
          </n-button>
        </n-space>
      );
    },
  });
  const tableColumns = [...columns];
  const [register, { getFieldsValue, setFieldsValue, getFormModel, getActiveColumns }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 120,
    schemas,
  });
  // 使用组合式函数创建 clearable 状态
  const { multiDateClearable, multiDateNoHidden } = useDateOptions(getFormModel, getActiveColumns);
  const loadDataTable = async (params) => {
    return params;
  };
  function reloadTable() {
    actionRef.value!.reload();
  }

  function handleExport() {
    try {
      exportLoading.value = true;
      //   window.open(
      //     exportFollowUpListApi({
      //       ...getFieldsValue(),
      //       pageNumber: 1,
      //       pageSize: 10000,
      //       token: userStore.getToken,
      //       followStatus: tabValue.value,
      //     })
      //   );
    } finally {
      exportLoading.value = false;
    }
  }

  onMounted(() => {
    reloadTable();
    // 默认打开 DrawerOrder 组件用于调试
    openDrawerOrder();
    onActivated(() => {
      reloadTable();
    });
  });
</script>

<style lang="less" scoped>
  :deep(.n-tabs) {
    .n-tab-pane {
      padding: 0 !important;
    }
  }
</style>
