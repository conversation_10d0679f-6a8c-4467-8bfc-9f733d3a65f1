// 指定元素打开全屏
export function openFullscreen(element: HTMLElement = document.documentElement) {
  const requestMethod =
    element.requestFullscreen ||
    // 兼容旧版浏览器
    (element as any).webkitRequestFullscreen ||
    (element as any).mozRequestFullScreen ||
    (element as any).msRequestFullscreen;

  if (requestMethod) {
    requestMethod.call(element);
  } else {
    console.warn('当前浏览器不支持全屏 API');
  }
}

/**
 * 监听某个指定元素是否退出全屏
 * @param element 要监听的元素
 * @param onExit 回调：退出全屏时调用
 * @param onEnter 回调：进入全屏时调用（可选）
 * @returns 返回取消监听的方法
 */
export function watchElementFullscreen(
  element: HTMLElement,
  onExit: () => void,
  onEnter?: () => void
) {
  const handler = () => {
    const fullscreenEl =
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement;

    if (fullscreenEl === element) {
      onEnter?.();
    } else {
      onExit();
    }
  };

  document.addEventListener('fullscreenchange', handler);
  document.addEventListener('webkitfullscreenchange', handler);
  document.addEventListener('mozfullscreenchange', handler);
  document.addEventListener('MSFullscreenChange', handler);

  return () => {
    document.removeEventListener('fullscreenchange', handler);
    document.removeEventListener('webkitfullscreenchange', handler);
    document.removeEventListener('mozfullscreenchange', handler);
    document.removeEventListener('MSFullscreenChange', handler);
  };
}
