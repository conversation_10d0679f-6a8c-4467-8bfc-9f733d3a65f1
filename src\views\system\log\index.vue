<template>
  <n-card :bordered="false">
    <BasicForm
      :showSlotConfig="false"
      @register="register"
      @submit="reloadTable"
      @reset="reloadTable"
    >
      <template #adminId="{ model, field }">
        <NSelect
          v-model:value="model[field]"
          label-field="username"
          value-field="id"
          :options="adminList"
          placeholder="请选择操作人"
          filterable
        />
      </template>
      <template #actionButton>
        <n-button :loading="exportLoading" @click="handleExport"> 导出 </n-button>
      </template>
    </BasicForm>
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.createTime"
      :scroll-x="1090"
      ref="action"
      :striped="true"
    />
  </n-card>
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { useTemplateRef, ref, onMounted, onActivated } from 'vue';
  import { getSystemLogListApi, getSystemUserListApi, exportSystemLogApi } from '@/api/system';
  import dayjs from 'dayjs';
  import { useUser } from '@/store/modules/user';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';

  defineOptions({
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'system_log',
  });

  const userStore = useUser();
  const exportLoading = ref(false);
  const adminList = ref([]);
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');
  const schemas: FormSchema[] = [
    {
      field: 'dateQuery',
      component: 'NDatePicker',
      childKey: ['startDate', 'endDate'],
      defaultValue: [dayjs().subtract(7, 'day').format('YYYYMMDD'), dayjs().format('YYYYMMDD')],
      label: '操作时间',
      componentProps: {
        type: 'daterange',
        'value-format': 'yyyyMMdd',
        format: 'yyyy-MM-dd',
        'is-date-disabled': createMonthRangeDisabledFn(3),
        clearable: false,
      },
    },
    {
      field: 'adminId',
      label: '操作人',
      slot: 'adminId',
    },
  ];
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });

  const loadDataTable = async (params) => {
    const { data } = await getSystemLogListApi({ ...getFieldsValue(), ...params });

    return data;
  };
  function reloadTable() {
    actionRef.value!.reload();
  }
  async function getSystemUserList() {
    try {
      const { data } = await getSystemUserListApi({
        pageNumber: 1,
        pageSize: 9999,
      });

      adminList.value = data.records;
    } catch (err) {
      console.log(err);
    }
  }
  function handleExport() {
    try {
      exportLoading.value = true;
      window.open(
        exportSystemLogApi({
          ...getFieldsValue(),
          pageNumber: 1,
          pageSize: 10000,
          token: userStore.getToken,
        })
      );
    } finally {
      exportLoading.value = false;
    }
  }

  onMounted(() => {
    getSystemUserList();

    onActivated(() => {
      reloadTable();
    });
  });
</script>
