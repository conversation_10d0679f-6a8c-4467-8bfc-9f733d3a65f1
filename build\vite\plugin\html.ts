/**
 * Plugin to minimize and use ejs template syntax in index.html.
 * https://github.com/anncwb/vite-plugin-html
 */
import type { PluginOption } from 'vite';

import { createHtmlPlugin } from 'vite-plugin-html';

import pkg from '../../../package.json';
import { GLOB_CONFIG_FILE_NAME } from '../../constant';

export function configHtmlPlugin(env: ViteEnv, isBuild: boolean) {
  const { VITE_GLOB_APP_TITLE, VITE_PUBLIC_PATH } = env;

  const path = VITE_PUBLIC_PATH.endsWith('/') ? VITE_PUBLIC_PATH : `${VITE_PUBLIC_PATH}/`;

  const getAppConfigSrc = () => {
    return `${path || '/'}${GLOB_CONFIG_FILE_NAME}?v=${pkg.version}-${new Date().getTime()}`;
  };

  // 生成版本时间戳
  const buildTimestamp = new Date().getTime().toString();

  const htmlPlugin: PluginOption[] = createHtmlPlugin({
    minify: isBuild,
    inject: {
      // Inject data into ejs template
      data: {
        title: VITE_GLOB_APP_TITLE,
      },
      // Embed the generated app.config.js file and version meta tag
      tags: [
        // 添加版本时间戳meta标签
        {
          tag: 'meta',
          attrs: {
            name: 'app-version',
            content: buildTimestamp,
          },
        },
        // 原有的script标签（仅在构建时添加）
        ...(isBuild
          ? [
              {
                tag: 'script',
                attrs: {
                  src: getAppConfigSrc(),
                },
              },
            ]
          : []),
      ],
    },
  });
  return htmlPlugin;
}
