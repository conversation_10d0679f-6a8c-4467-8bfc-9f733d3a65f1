<template>
  <n-card :bordered="false">
    <BasicForm :enable-cache="true" @register="register" @submit="reloadTable" @reset="reloadTable">
      <template #roleId="{ model, field }">
        <RoleSelect v-model="model[field]" multiple placeholder="请选择关联角色" />
      </template>
      <template #sourceId="{ model, field }">
        <SourceMediaSelect v-model="model[field]" type="source" placeholder="请选择线索来源" />
      </template>
      <template #mediaPlatformSource="{ model, field }">
        <SourceMediaSelect v-model="model[field]" type="media" placeholder="请选择来源媒体" />
      </template>
      <template #platformId="{ model, field }">
        <AdvflowMediaSelect v-model="model[field]" multiple placeholder="请选择投放平台" />
      </template>
    </BasicForm>
  </n-card>

  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="columns"
      :request="loadDataTable"
      :row-key="(row: TrafficRule) => row.configId"
      :actionColumn="actionColumn"
      :scroll-x="1800"
      ref="actionRef"
      :striped="true"
    >
      <template #tableTitle>
        <n-button type="primary" v-permission="{ action: 'add' }" @click="handleAdd">
          <template #icon>
            <n-icon>
              <PlusOutlined />
            </n-icon>
          </template>
          新增规则
        </n-button>
      </template>
    </BasicTable>
  </n-card>

  <!-- 新增/编辑抽屉 -->
  <TrafficRuleDrawer ref="drawerRef" @success="saveSuccess" />
</template>

<script setup lang="tsx">
  import { reactive, ref, onMounted, onUnmounted, computed } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import { PlusOutlined, EditOutlined, DeleteOutlined } from '@vicons/antd';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import TrafficRuleDrawer from './components/TrafficRuleDrawer.vue';
  import {
    getTrafficRuleListApi,
    deleteTrafficRuleApi,
    updateTrafficRuleStatusApi,
  } from '@/api/system/trafficRule';
  import { EnableDisableOptions } from '@/views/client/enum';
  import { FormStatus } from '@/enums';
  import SourceMediaSelect from '@/components/SourceMediaSelect/index.vue';
  import { useSourceMediaOptions } from '@/components/SourceMediaSelect/useSourceMediaOptions';
  import { h, withDirectives, resolveDirective } from 'vue';
  import { NSwitch, NTag } from 'naive-ui';
  import type { TrafficRule } from './types';
  import RoleSelect from '@/components/RoleSelect/index.vue';
  import { useRoleOptions } from '@/components/RoleSelect/useRoleOptions';
  import { isShowErrorMessage } from '@/utils/http/axios/request';
  import dayjs from 'dayjs';
  import AdvflowMediaSelect from '@/components/AdvflowMediaSelect/index.vue';

  defineOptions({
    name: 'TrafficRule',
  });

  const dialog = useDialog();
  const message = useMessage();
  const actionRef = ref();
  const drawerRef = ref();

  // 选项数据
  const { sourceOptions, mediaOptions, platformOptions } = useSourceMediaOptions();
  const { reloadOptions, roleOptions } = useRoleOptions();

  // 搜索表单配置
  const schemas = computed<FormSchema[]>(() => [
    {
      field: 'ruleName',
      component: 'NInput',
      label: '规则名称',
      componentProps: {
        placeholder: '请输入规则名称',
      },
    },
    {
      field: 'sourceId',
      label: '线索来源',
      slot: 'sourceId',
    },
    {
      field: 'mediaPlatformSource',
      label: '来源媒体',
      slot: 'mediaPlatformSource',
    },
    {
      field: 'roleId',
      label: '关联角色',
      slot: 'roleId',
    },
    {
      field: 'addTimeRange',
      component: 'NDatePicker',
      label: '添加时间',
      componentProps: {
        type: 'daterange',
        'value-format': 'yyyy-MM-dd',
        format: 'yyyy-MM-dd',
        'is-date-disabled': (ts: number) => {
          const today = dayjs().endOf('day');
          return dayjs(ts).isAfter(today);
        },
      },
    },
    {
      field: 'status',
      component: 'NSelect',
      label: '状态',
      componentProps: {
        placeholder: '请选择状态',
        options: EnableDisableOptions,
      },
    },
    {
      field: 'platformId',
      label: '投放平台',
      slot: 'platformId',
    },
  ]);

  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });

  // 操作列配置
  const actionColumn = reactive({
    width: 160,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record: TrafficRule) {
      return (
        <n-space justify="center">
          <n-button
            type="primary"
            v-permission={{ action: 'edit' }}
            text
            onClick={() => handleEdit(record)}
          >
            <n-icon>
              <EditOutlined />
            </n-icon>
            编辑
          </n-button>
          {record.isDefault !== 1 && (
            <n-button
              type="error"
              v-permission={{ action: 'delete' }}
              text
              onClick={() => handleDelete(record)}
            >
              <n-icon>
                <DeleteOutlined />
              </n-icon>
              删除
            </n-button>
          )}
        </n-space>
      );
    },
  });

  // 加载表格数据
  const loadDataTable = async (params: any) => {
    const searchParams = getFieldsValue();

    // 处理时间区间，将 addTimeRange 拆分为 startTime 和 endTime
    const processedParams = { ...searchParams };
    if (searchParams.addTimeRange && Array.isArray(searchParams.addTimeRange)) {
      processedParams.startTime = searchParams.addTimeRange[0];
      processedParams.endTime = searchParams.addTimeRange[1];
      delete processedParams.addTimeRange;
    }

    const { data } = await getTrafficRuleListApi({
      ...processedParams,
      ...params,
    });
    return data;
  };

  // 重新加载表格
  function reloadTable() {
    actionRef.value?.reload();
  }

  // 新增
  function handleAdd() {
    drawerRef.value?.open();
  }

  // 编辑
  function handleEdit(record: TrafficRule) {
    drawerRef.value?.open(record);
  }

  // 删除
  function handleDelete(record: TrafficRule) {
    dialog.warning({
      title: '温馨提示',
      content: '请确认是否删除当前规则？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteTrafficRuleApi(record.configId);
          message.success('删除成功');
          reloadTable();
        } catch (error: any) {
          if (error.msg) {
            dialog.info({ title: '温馨提示', content: error.msg, positiveText: '好的' });
          }
        }
      },
    });
  }

  // 创建表格列配置
  const buildSourceLabelMap = computed(() => {
    const map = new Map<string, string>();
    [...sourceOptions.value, ...mediaOptions.value, ...platformOptions.value].forEach((item) =>
      map.set(String(item.value), item.label)
    );
    return map;
  });

  // 分配媒体（支持第三级：投放平台）
  const createMediaRender = (getLabelByValue: (value: string) => string) => (row: TrafficRule) => {
    if (!Array.isArray(row.rules) || row.rules.length === 0) return '';

    return row.rules
      .map((rule) => {
        const source = getLabelByValue(String(rule.sourceId ?? ''));
        const media = getLabelByValue(String(rule.mediaPlatformSource ?? ''));
        const platform = getLabelByValue(String((rule as any).platformId ?? ''));
        const parts = [source, media, platform].filter((p) => !!p);
        return parts.join('/');
      })
      .filter(Boolean)
      .join('、');
  };

  const columns = computed(() => {
    const getLabelByValue = (value: string) => buildSourceLabelMap.value.get(value) || value;

    return [
      {
        title: 'ID',
        key: 'configId',
      },
      {
        title: '规则名称',
        key: 'ruleName',
        width: 200,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '规则备注',
        key: 'remark',
        width: 200,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '分配媒体',
        key: 'mediaPlatformSource',
        width: 280,
        ellipsis: {
          tooltip: true,
        },
        render: createMediaRender(getLabelByValue),
      },
      {
        title: '角色',
        key: 'roleIds',
        width: 200,
        render(row: TrafficRule) {
          if (!Array.isArray(row.roleIds) || row.roleIds.length === 0) return '-';

          const roleMap = new Map<number, string>(
            roleOptions.value.map((r: any) => [r.id, r.name])
          );

          const roleLabels = row.roleIds
            .map((roleId) => roleMap.get(roleId) ?? String(roleId))
            .filter(Boolean);

          return roleLabels.length > 0 ? roleLabels.join('、') : '-';
        },
      },
      {
        title: '添加人',
        key: 'createBy',
        width: 120,
      },
      {
        title: '添加时间',
        key: 'createTime',
        // sorter: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
        width: 180,
      },
      {
        title: '更新人',
        key: 'updateBy',
        width: 120,
      },
      {
        title: '更新时间',
        key: 'updateTime',
        // sorter: (a, b) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime(),
        width: 180,
      },
      {
        title: '状态',
        key: 'status',
        width: 100,
        align: 'center',
        render(row: TrafficRule) {
          if (row.isDefault === 1) {
            return h(NTag, { type: 'success' }, { default: () => '默认启用' });
          }
          const permission = resolveDirective('permission');
          return withDirectives(
            h(NSwitch, {
              value: row.status === FormStatus.Enable,
              disabled: row.isDefault === FormStatus.Enable,
              onUpdateValue: (value: boolean) => {
                handleUpdateStatus(row.configId, value ? FormStatus.Enable : FormStatus.Disable);
              },
            }),
            [[permission, { action: 'status' }]]
          );
        },
      },
    ];
  });

  // 状态更新
  const handleUpdateStatus = async (configId: number, status: number) => {
    try {
      await updateTrafficRuleStatusApi(status, configId);
      message.success('状态更新成功');
      reloadTable();
    } catch (error: any) {
      if (error.msg) {
        dialog.info({ title: '温馨提示', content: error.msg, positiveText: '好的' });
      }
    }
  };

  const saveSuccess = () => {
    reloadTable();
    reloadOptions();
  };

  onMounted(() => {
    reloadOptions();
    isShowErrorMessage.value = false;
  });
  onUnmounted(() => {
    isShowErrorMessage.value = true;
  });
</script>
