# ZIP下载工具优化说明

## 🎯 优化内容

### 主要改进：单文件直接下载
当文件列表中只有一个文件时，现在会**直接下载文件**而不是打包成ZIP，提升用户体验。

## 📋 功能特性

### 1. 智能下载模式
- **单文件**：直接下载，保持原文件名
- **多文件**：打包成ZIP下载

### 2. 支持的文件类型
- 普通URL文件（直接通过链接下载）
- 带参数的API文件（通过fetch获取后下载）

### 3. 完整的错误处理
- 网络超时处理
- 下载失败重试机制
- 详细的错误信息记录

## 🚀 使用方法

### 基础用法

```typescript
import { downloadFilesAsZip } from './zipDownload';

// 单文件 - 直接下载
await downloadFilesAsZip(['https://example.com/document.pdf']);

// 多文件 - ZIP打包下载
await downloadFilesAsZip([
  'https://example.com/file1.pdf',
  'https://example.com/file2.pdf',
  'https://example.com/file3.pdf'
]);
```

### 高级用法

```typescript
// 带参数的单文件下载
await downloadFilesAsZip([{
  url: '/api/download/contract/123',
  filename: '合同文档.pdf',
  params: { token: 'your-token', format: 'pdf' }
}]);

// 自定义配置
await downloadFilesAsZip(files, {
  zipFilename: '自定义文件包.zip',
  showProgress: true,
  showResult: true,
  maxConcurrent: 3,
  timeout: 60000,
  generateFileName: (url, index) => `文档_${index + 1}.pdf`,
  onProgress: (current, total, filename) => {
    console.log(`下载进度: ${current}/${total} - ${filename}`);
  }
});
```

## 📊 返回结果

```typescript
interface ZipDownloadResult {
  success: number;    // 成功下载的文件数
  failed: number;     // 失败的文件数
  total: number;      // 总文件数
  zipSize: number;    // ZIP文件大小（单文件直接下载时为0）
  errors: Array<{     // 错误详情
    url: string;
    filename: string;
    error: any;
    index: number;
  }>;
}
```

## 🔄 行为变化对比

### 优化前
```
1个文件 → 生成ZIP包 → 下载ZIP文件
多个文件 → 生成ZIP包 → 下载ZIP文件
```

### 优化后
```
1个文件 → 直接下载文件（保持原名）
多个文件 → 生成ZIP包 → 下载ZIP文件
```

## 💡 优化优势

1. **用户体验提升**
   - 单文件下载更快速
   - 保持原始文件名和格式
   - 减少不必要的压缩步骤

2. **性能优化**
   - 单文件无需ZIP压缩处理
   - 减少内存占用
   - 提高下载速度

3. **向后兼容**
   - API接口保持不变
   - 现有代码无需修改
   - 自动识别下载模式

## 🧪 测试文件

### 测试代码
- `zipDownload.test.ts` - 单元测试
- `zipDownload.demo.html` - 浏览器演示页面

### 运行测试
```typescript
// 在浏览器控制台中
import { runAllTests } from './zipDownload.test';
runAllTests();
```

## 📝 使用示例

### 业务场景示例

```typescript
// 签约文档下载
async function downloadContractFiles(contractId: string) {
  const { data: fileList } = await getContractFiles(contractId);
  
  if (!fileList || fileList.length === 0) {
    window.$message?.error('暂无文件可下载');
    return;
  }
  
  // 自动判断：1个文件直接下载，多个文件ZIP打包
  const result = await downloadFilesAsZip(fileList, {
    zipFilename: `合同文档_${contractId}.zip`,
    showProgress: true,
    showResult: true
  });
  
  return result;
}
```

### Vue组件中使用

```vue
<template>
  <button @click="handleDownload" :loading="downloading">
    下载文件
  </button>
</template>

<script setup>
import { downloadFilesAsZip } from '@/utils/zipDownload';

const downloading = ref(false);

async function handleDownload() {
  downloading.value = true;
  
  try {
    const files = await getFileList();
    await downloadFilesAsZip(files);
  } catch (error) {
    console.error('下载失败:', error);
  } finally {
    downloading.value = false;
  }
}
</script>
```

## 🔧 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| zipFilename | string | 'files.zip' | ZIP文件名（仅多文件时使用） |
| showProgress | boolean | true | 是否显示进度提示 |
| showResult | boolean | true | 是否显示结果提示 |
| maxConcurrent | number | 5 | 最大并发下载数 |
| timeout | number | 30000 | 下载超时时间（毫秒） |
| generateFileName | function | - | 自定义文件名生成函数 |
| onProgress | function | - | 进度回调函数 |

## 🐛 注意事项

1. **单文件下载时**：
   - `zipSize` 返回值为 0
   - 不会触发ZIP相关的进度提示
   - 直接使用原始文件名或自定义文件名

2. **带参数的文件**：
   - 会通过fetch下载后用blob方式保存
   - 支持认证token等参数传递

3. **错误处理**：
   - 单文件下载失败时，`failed` 为 1，`success` 为 0
   - 错误信息会记录在 `errors` 数组中

## 🔄 版本兼容性

- ✅ 完全向后兼容
- ✅ 现有代码无需修改
- ✅ API接口保持不变
- ✅ 自动识别下载模式
