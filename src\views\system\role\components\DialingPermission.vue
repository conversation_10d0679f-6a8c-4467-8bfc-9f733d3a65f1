<template>
  <n-space vertical>
    <n-checkbox-group :value="checkedKeys" @update:value="handleUpdateValue">
      <n-space item-style="display: flex;" vertical>
        <n-checkbox v-for="item in items" :key="item.id" class="mb-[10px]" :value="item.id">
          <n-tooltip trigger="hover">
            <template #trigger>
              <div class="flex items-center">
                {{ item.name
                }}<n-icon size="15">
                  <HelpCircleOutline />
                </n-icon>
              </div>
            </template>
            {{ item.tips }}
          </n-tooltip>
        </n-checkbox>
      </n-space>
    </n-checkbox-group>
  </n-space>
</template>

<script setup lang="ts">
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { ref, watch } from 'vue';
  import { isEmpty } from '@/utils/is';
  import { updateRoleMdm } from '@/api/system/role';
  import { useUser } from '@/store/modules/user';

  defineExpose({
    sure,
  });
  const props = defineProps({
    roleId: {
      type: [Number, String],
      default: '',
    },
    roleDetail: {
      type: Object,
      default: () => ({}),
    },
  });
  const userStore = useUser();
  const checkedKeys = ref<any[]>([]);
  watch(
    () => props.roleDetail.id,
    () => {
      getData();
    },
    { immediate: true }
  );

  const items = [
    {
      id: 1,
      name: '启用',
      tips: '启用工作机拨打权限的角色对应用户可进行用户同步，用户同步成功后可进行设备激活',
    },
    {
      id: 0,
      name: '不启用',
      tips: '启用工作机拨打权限的角色对应用户可进行用户同步，用户同步成功后可进行设备激活',
    },
  ];

  function handleUpdateValue(_, meta) {
    checkedKeys.value = meta.actionType === 'check' ? [meta.value] : [];
  }

  function getData() {
    const { roleMdmPermission } = props.roleDetail;
    if (!isEmpty(roleMdmPermission)) {
      checkedKeys.value = [roleMdmPermission];
    } else {
      checkedKeys.value = [0];
    }
  }

  function sure() {
    const postData = {
      id: Number(props.roleId),
      roleMdmPermission:
        checkedKeys.value?.[0] !== undefined ? Number(checkedKeys.value?.[0]) : null,
    };

    return updateRoleMdm(postData).then((res) => {
      if (res.code == 200) {
        window.$message.success('配置成功');
        userStore.getInfo();
      }
    });
  }
</script>

<style lang="less" scoped></style>
