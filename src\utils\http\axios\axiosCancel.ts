import type { AxiosRequestConfig } from 'axios';

// 每个请求配置唯一 key（包括 method、url、params、data）
const pendingMap = new Map<string, AbortController>();

function getPendingKey(config: AxiosRequestConfig): string {
  const { method, url, params, data } = config;
  return [method, url, JSON.stringify(params), JSON.stringify(data)].join('&');
}

export function addPending(config: AxiosRequestConfig) {
  const key = getPendingKey(config);
  if (!pendingMap.has(key)) {
    const controller = new AbortController();
    config.signal = controller.signal;
    pendingMap.set(key, controller);
  } else {
    config.signal = pendingMap.get(key)!.signal;
  }
}

export function removePending(config: AxiosRequestConfig) {
  const key = getPendingKey(config);
  if (pendingMap.has(key)) {
    pendingMap.get(key)?.abort();
    pendingMap.delete(key);
  }
}

export function clearAllPending() {
  pendingMap.forEach((controller) => controller.abort());
  pendingMap.clear();
}
