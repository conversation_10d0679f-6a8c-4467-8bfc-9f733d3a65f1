import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { DashboardOutlined } from '@vicons/antd';
import { renderIcon } from '@/utils/index';

const routeName = 'message';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/message',
    name: routeName,
    redirect: '/message/detail',
    component: Layout,
    meta: {
      title: '消息中心',
      icon: renderIcon(DashboardOutlined),
      sort: 0,
    },
    children: [
      {
        path: 'detail',
        name: `${routeName}_detail`,
        meta: {
          title: '消息中心',
          keepAlive: true,
          buttons: [
            { key: 'message_detail_confirm', name: '批量确认' },
            { key: 'message_detail_delete', name: '批量删除' },
          ],
        },
        component: () => import('@/views/message/detail/index.vue'),
      },
    ],
  },
];

export default routes;
