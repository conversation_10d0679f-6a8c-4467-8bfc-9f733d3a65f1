import { defineAsyncComponent } from 'vue';
import { useMountComponent } from '@/composables/useMountComponent';
import type { DrawerOrderProps } from '@/components/DrawerOrder/types';

const DrawerOrder = defineAsyncComponent(() => import('@/components/DrawerOrder/index.vue'));

export function useDrawerOrder() {
  const { mountPromisify } = useMountComponent();

  const open = (
    props?: DrawerOrderProps
  ): Promise<{ action: 'ok' | 'close'; data: Recordable }> => {
    return mountPromisify({
      render: (ctx) => <DrawerOrder {...props} onClose={ctx.unmount} />,
    });
  };
  return {
    open,
  };
}
