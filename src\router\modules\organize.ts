import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { OptionsSharp } from '@vicons/ionicons5';
import { renderIcon } from '@/utils';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/organize',
    name: 'Organize',
    redirect: '/organize/user',
    component: Layout,
    meta: {
      title: '组织管理',
      icon: renderIcon(OptionsSharp),
      sort: 3,
    },
    children: [
      {
        path: 'user',
        name: 'organize_user',
        meta: {
          title: '用户管理',
          buttons: [
            { key: 'organize_user_add', name: '新增用户' },
            { key: 'organize_user_edit', name: '编辑用户' },
            { key: 'organize_user_status', name: '启用/禁用用户' },
            { key: 'organize_user_hand_over', name: '移交' },
            { key: 'organize_department_add', name: '添加部门' },
            { key: 'organize_department_edit', name: '修改部门' },
            { key: 'organize_department_delete', name: '删除部门' },
          ],
          keepAlive: true,
        },
        component: () => import('@/views/organize/user/index.vue'),
      },
      // {
      //   path: 'user/list',
      //   name: 'organize_user_list',
      //   redirect: '/organize/user',
      //   meta: {
      //     title: '用户列表',
      //     buttonsPreFix: 'organize_user_',
      //     buttons: [
      //       { key: 'add', name: '新增用户' },
      //       { key: 'edit', name: '编辑用户' },
      //       { key: 'status', name: '启用/禁用用户' },
      //       { key: 'hand_over', name: '移交' },
      //     ],
      //   },
      // },
      // {
      //   path: 'department/list',
      //   name: 'organize_department_list',
      //   redirect: '/organize/user',
      //   meta: {
      //     title: '部门管理',
      //     buttonsPreFix: 'organize_department_',
      //     buttons: [
      //       { key: 'add', name: '添加部门' },
      //       { key: 'edit', name: '修改部门' },
      //       { key: 'delete', name: '删除部门' },
      //     ],
      //   },
      // },
    ],
  },
];

export default routes;
