<template>
  <div class="awaiting-container">
    <div class="info-line" v-if="targetTime">
      请在
      <n-time :time="targetTime" format="M-dd HH:mm" />
      前完成【首次跟进】，倒计时
      <CountdownTimer :target-time="targetTime.getTime()" />
    </div>
    <div class="image-placeholder">
      <img src="@/assets/images/detail-drawer/node_time.png" alt="node_time" />
    </div>
    <n-button
      v-permission="{ action: 'detail_claim' }"
      type="primary"
      @click="$emit('start-follow-up')"
    >
      开始跟进
    </n-button>
  </div>
</template>

<script lang="ts" setup>
  import { computed, defineProps, withDefaults } from 'vue';
  import { NTime, NButton } from 'naive-ui';
  import CountdownTimer from './CountdownTimer.vue';
  const props = withDefaults(
    defineProps<{
      createTime?: string;
    }>(),
    {
      createTime: '',
    }
  );

  defineEmits(['start-follow-up']);

  const targetTime = computed(() => {
    if (!props.createTime) return null;
    const createDate = new Date(props.createTime);
    // 12小时后
    return new Date(createDate.getTime() + 12 * 60 * 60 * 1000);
  });
</script>

<style scoped>
  .awaiting-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fafafa;
  }
  .info-line {
    margin-bottom: 20px;
    font-size: 14px;
  }
  .image-placeholder {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
