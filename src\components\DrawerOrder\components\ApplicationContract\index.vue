<template>
  <n-space vertical>
    <n-card>
      <!-- 车辆资料上传 -->
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="车辆资料上传" :status="2" />
          </template>

          <div>
            <n-form
              ref="formRef"
              label-placement="left"
              size="medium"
              :model="formModel"
              :rules="formRules"
              label-width="160px"
            >
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <!-- 车辆登记证书 -->
                  <n-form-item label="车辆登记证书(2)" path="vehicleRegistration">
                    <UploadFile
                      v-model:fileList="formModel.vehicleRegistration"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="5"
                      :max-count="2"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <!-- 行驶证 -->
                  <n-form-item label="行驶证" path="drivingPermit">
                    <UploadFile
                      v-model:fileList="formModel.drivingPermit"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="5"
                      :max-count="2"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>

              <!-- 贷款信息 -->
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="贷款金额" path="loanAmount">
                    <n-input
                      v-model:value="formModel.loanAmount"
                      placeholder="请输入贷款金额"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="贷款期限" path="loanTerm">
                    <n-select
                      v-model:value="formModel.loanTerm"
                      placeholder="请选择贷款期限"
                      :options="loanTermOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="车辆售价" path="vehicleSalePrice">
                    <n-input
                      v-model:value="formModel.vehicleSalePrice"
                      placeholder="请输入车辆售价"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="里程公里" path="mileage">
                    <n-input v-model:value="formModel.mileage" placeholder="请输入里程" clearable />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="变速箱类型" path="transmissionType">
                    <n-select
                      v-model:value="formModel.transmissionType"
                      placeholder="请选择变速箱类型"
                      :options="transmissionTypeOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="抵押城市" path="mortgageCity">
                    <CitySelect
                      v-model="formModel.mortgageCity"
                      change-on-select
                      :multiple="false"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="申请人公牌类型" path="applicantPlateType">
                    <n-select
                      v-model:value="formModel.applicantPlateType"
                      placeholder="请选择公牌类型"
                      :options="plateTypeOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司电话">
                    <n-input
                      v-model:value="formModel.companyPhone"
                      placeholder="请输入公司电话"
                      clearable
                    />
                    <template #feedback>
                      <n-tooltip trigger="hover">
                        <template #trigger>
                          <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                            <QuestionCircleTwotone />
                          </n-icon>
                        </template>
                        请输入公司联系电话
                      </n-tooltip>
                    </template>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌组织机构代码">
                    <n-input
                      v-model:value="formModel.organizationCode"
                      placeholder="请输入组织机构代码"
                      clearable
                    />
                    <template #feedback>
                      <n-tooltip trigger="hover">
                        <template #trigger>
                          <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                            <QuestionCircleTwotone />
                          </n-icon>
                        </template>
                        请输入组织机构代码
                      </n-tooltip>
                    </template>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司名称">
                    <n-input
                      v-model:value="formModel.companyName"
                      placeholder="请输入公司名称"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司法人姓名">
                    <n-input
                      v-model:value="formModel.legalPersonName"
                      placeholder="请输入法人姓名"
                      clearable
                    />
                    <template #feedback>
                      <n-tooltip trigger="hover">
                        <template #trigger>
                          <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                            <QuestionCircleTwotone />
                          </n-icon>
                        </template>
                        请输入公司法定代表人的姓名
                      </n-tooltip>
                    </template>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司法人身份证">
                    <n-input
                      v-model:value="formModel.legalPersonIdCard"
                      placeholder="请输入法人身份证号"
                      maxlength="18"
                      clearable
                    />
                    <template #feedback>
                      <n-tooltip trigger="hover">
                        <template #trigger>
                          <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                            <QuestionCircleTwotone />
                          </n-icon>
                        </template>
                        请输入法人身份证号码
                      </n-tooltip>
                    </template>
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="公牌公司注册公司时间 yyyy-MM-dd">
                    <n-date-picker
                      v-model:value="formModel.companyRegistrationDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择注册时间"
                      clearable
                    />
                    <template #feedback>
                      <n-tooltip trigger="hover">
                        <template #trigger>
                          <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                            <QuestionCircleTwotone />
                          </n-icon>
                        </template>
                        请选择公司注册日期
                      </n-tooltip>
                    </template>
                  </n-form-item>
                </n-grid-item>
              </n-grid>

              <!-- 行驶证相关 -->
              <n-card>
                <n-flex align="center">
                  <div>
                    <SubTitle title="行驶证相关" />
                    <n-button type="primary" @click="handlePreviewDrivingPermit"> 预览 </n-button>
                  </div>
                  <div class="flex-1">
                    <n-grid :cols="GRID_COLS">
                      <n-grid-item>
                        <n-form-item label="排量/L" path="displacement">
                          <n-input
                            v-model:value="formModel.displacement"
                            placeholder="请输入排量"
                            clearable
                          />
                        </n-form-item>
                      </n-grid-item>
                      <n-grid-item>
                        <n-form-item label="燃料类型" path="fuelType">
                          <n-select
                            v-model:value="formModel.fuelType"
                            placeholder="请选择燃料类型"
                            :options="fuelTypeOptions"
                            clearable
                          />
                        </n-form-item>
                      </n-grid-item>
                      <n-grid-item>
                        <n-form-item label="车辆类别" path="vehicleCategory">
                          <n-select
                            v-model:value="formModel.vehicleCategory"
                            placeholder="请选择车辆类别"
                            :options="vehicleCategoryOptions"
                            clearable
                          />
                        </n-form-item>
                      </n-grid-item>
                      <n-grid-item>
                        <n-form-item label="车牌号" path="licensePlateNumber">
                          <n-input
                            v-model:value="formModel.licensePlateNumber"
                            placeholder="请输入车牌号"
                            clearable
                          />
                        </n-form-item>
                      </n-grid-item>
                      <n-grid-item>
                        <n-form-item label="车辆颜色" path="vehicleColor">
                          <n-select
                            v-model:value="formModel.vehicleColor"
                            placeholder="请选择车辆颜色"
                            :options="vehicleColorOptions"
                            clearable
                          />
                        </n-form-item>
                      </n-grid-item>
                      <n-grid-item>
                        <n-form-item label="发动机号" path="engineNumber">
                          <n-input
                            v-model:value="formModel.engineNumber"
                            placeholder="请输入发动机号"
                            clearable
                          />
                        </n-form-item>
                      </n-grid-item>
                    </n-grid>
                  </div>
                </n-flex>
              </n-card>

              <!-- 车辆登记证相关 -->
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <SubTitle title="车辆登记证相关" />
                </n-grid-item>
                <n-grid-item>
                  <n-flex justify="end">
                    <n-button type="primary" @click="handlePreviewVehicleRegistration">
                      预览
                    </n-button>
                  </n-flex>
                </n-grid-item>
              </n-grid>

              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="首次上牌时间 yyyy-MM-dd" path="firstRegistrationDate">
                    <n-date-picker
                      v-model:value="formModel.firstRegistrationDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择首次上牌时间"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="车辆VIN码" path="vinCode">
                    <n-input
                      v-model:value="formModel.vinCode"
                      placeholder="请输入VIN码"
                      maxlength="17"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="过户次数" path="transferCount">
                    <n-input
                      v-model:value="formModel.transferCount"
                      placeholder="请输入过户次数"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>

              <n-flex justify="center">
                <n-button type="primary" size="large" @click="handleSubmit"> 确认提交 </n-button>
              </n-flex>
            </n-form>

            <n-divider dashed />

            <SubTitle
              title="车辆资料上传"
              desc="前置要求：姓名、手机号、身份证号完整、身份证正反面"
            />

            <n-data-table :columns="columns" :data="data" />
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import CitySelect from '@/components/CitySelect/index.vue';
  import { QuestionCircleTwotone } from '@vicons/antd';

  import { ref, reactive } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';

  const formRef = ref<FormInst | null>(null);
  const formModel = reactive({
    // 文件上传
    vehicleRegistration: [],
    drivingPermit: [],
    // 贷款信息
    loanAmount: '',
    loanTerm: '',
    vehicleSalePrice: '',
    mileage: '',
    transmissionType: '',
    mortgageCity: '',
    // 公牌信息
    applicantPlateType: '一般公牌',
    companyPhone: '',
    organizationCode: '',
    companyName: '',
    legalPersonName: '',
    legalPersonIdCard: '',
    companyRegistrationDate: null,
    // 行驶证相关
    displacement: '',
    fuelType: '',
    vehicleCategory: '',
    licensePlateNumber: '',
    vehicleColor: '',
    engineNumber: '',
    // 车辆登记证相关
    firstRegistrationDate: null,
    vinCode: '',
    transferCount: '',
  });

  const formRules = {
    vehicleRegistration: [
      { required: true, message: '请上传车辆登记证书第一页', trigger: 'change' },
    ],
    drivingPermit: [{ required: true, message: '请上传行驶证', trigger: 'change' }],
    loanAmount: [{ required: true, message: '请输入贷款金额', trigger: 'blur' }],
    loanTerm: [{ required: true, message: '请选择贷款期限', trigger: 'change' }],
    vehicleSalePrice: [{ required: true, message: '请输入车辆售价', trigger: 'blur' }],
    mileage: [{ required: true, message: '请输入里程', trigger: 'blur' }],
    transmissionType: [{ required: true, message: '请选择变速箱类型', trigger: 'change' }],
    mortgageCity: [{ required: true, message: '请选择抵押城市', trigger: 'change' }],
    applicantPlateType: [{ required: true, message: '请选择公牌类型', trigger: 'change' }],
    displacement: [{ required: true, message: '请输入排量', trigger: 'blur' }],
    fuelType: [{ required: true, message: '请选择燃料类型', trigger: 'change' }],
    vehicleCategory: [{ required: true, message: '请选择车辆类别', trigger: 'change' }],
    licensePlateNumber: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
    vehicleColor: [{ required: true, message: '请选择车辆颜色', trigger: 'change' }],
    engineNumber: [{ required: true, message: '请输入发动机号', trigger: 'blur' }],
    firstRegistrationDate: [{ required: true, message: '请选择首次上牌时间', trigger: 'change' }],
    vinCode: [{ required: true, message: '请输入车辆VIN码', trigger: 'blur' }],
    transferCount: [{ required: true, message: '请输入过户次数', trigger: 'blur' }],
  };

  // 下拉选项
  const loanTermOptions = [
    { label: '12个月', value: '12' },
    { label: '24个月', value: '24' },
    { label: '36个月', value: '36' },
    { label: '48个月', value: '48' },
    { label: '60个月', value: '60' },
  ];

  const transmissionTypeOptions = [
    { label: '手动', value: 'manual' },
    { label: '自动', value: 'automatic' },
    { label: 'CVT', value: 'cvt' },
    { label: '双离合', value: 'dual-clutch' },
  ];

  const plateTypeOptions = [
    { label: '一般公牌', value: '一般公牌' },
    { label: '特殊公牌', value: '特殊公牌' },
  ];

  const fuelTypeOptions = [
    { label: '汽油', value: 'petrol' },
    { label: '柴油', value: 'diesel' },
    { label: '电动', value: 'electric' },
    { label: '混动', value: 'hybrid' },
  ];

  const vehicleCategoryOptions = [
    { label: '小型汽车', value: 'small_car' },
    { label: '中型汽车', value: 'medium_car' },
    { label: '大型汽车', value: 'large_car' },
    { label: 'SUV', value: 'suv' },
  ];

  const vehicleColorOptions = [
    { label: '白色', value: 'white' },
    { label: '黑色', value: 'black' },
    { label: '银色', value: 'silver' },
    { label: '红色', value: 'red' },
    { label: '蓝色', value: 'blue' },
    { label: '其他', value: 'other' },
  ];

  const productOptions = [
    { label: '产品A', value: 'product_a' },
    { label: '产品B', value: 'product_b' },
    { label: '产品C', value: 'product_c' },
  ];

  const columns = [
    {
      title: '提交状态',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  const data = [];

  const handleSubmit = async () => {
    await formRef.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交表单:', formModel);
      window.$message?.success('提交成功');
    });
  };

  const handlePreviewDrivingPermit = () => {
    if (formModel.drivingPermit && formModel.drivingPermit.length > 0) {
    } else {
      window.$message?.warning('请先上传行驶证');
    }
  };

  const handlePreviewVehicleRegistration = () => {
    if (formModel.vehicleRegistration && formModel.vehicleRegistration.length > 0) {
    } else {
      window.$message?.warning('请先上传车辆登记证书');
    }
  };

  // 产品选择相关
  const formRef2 = ref<FormInst | null>(null);
  const formModel2 = reactive({
    selectedProduct: '',
  });

  const formRules2 = {
    selectedProduct: [{ required: true, message: '请选择产品', trigger: 'change' }],
  };

  const handleConfirmProduct = async () => {
    await formRef2.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('确认产品:', formModel2);
      window.$message?.success('产品确认成功');
    });
  };
</script>

<style lang="less" scoped></style>
