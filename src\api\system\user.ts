import { get } from '@/utils/lib/axios.package';

/**
 * @description: 获取用户信息
 */
export function getUserInfo() {
  return get('/cms/admin/me');
}

export interface ILoginParams {
  mobileNo: string;
  smsCode: string;
  needDelay?: number;
}
/**
 * @description: 用户登录
 */
export function login(params: ILoginParams) {
  return get('/cms/admin/login', params);
}

/**
 * @description: 用户登出
 */
export function logout(params) {
  return get('/cms/admin/auth/logout', params);
}

interface ISendCaptchaParams {
  mobileNo: string;
}
/**
 * @description: 发送验证码
 */
export function sendCaptcha(params: ISendCaptchaParams) {
  return get('/cms/admin/send/sms', params);
}
