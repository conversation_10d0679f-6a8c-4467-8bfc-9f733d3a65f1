<template>
  <div class="flex items-center">
    <slot name="mobileNo">{{ mobileNo }}</slot>
    <n-button v-callPermission text class="p-[5px]" @click="submitFormDialog({ clubId: clubId })">
      <n-icon size="16">
        <CallOutline />
      </n-icon>
    </n-button>
  </div>
</template>
<script lang="ts" setup>
  import { submitFormDialog } from '@/views/client/myClients/CallForm';
  import { CallOutline } from '@vicons/ionicons5';
  import { onMounted } from 'vue';
  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
    clubId: {
      type: [Number, String],
      default: null,
    },
    mobileNo: {
      type: [Number, String],
      default: null,
    },
  });
  onMounted(() => {
    console.log(props.clubId, 'clubId');
  });
</script>
