<template>
  <FormDialog
    v-model="formDialogModel"
    ref="formDialog"
    :title="formId ? '编辑部门' : '新增部门'"
    :form-model="paramForm"
    :rules="rules"
    @submit="handleSubmit"
    @close="handleClose"
    @open="handleOpen"
  >
    <n-form-item label="部门名称：" path="branchName">
      <n-input
        v-model:value="paramForm.branchName"
        maxlength="15"
        placeholder="请输入部门名称"
        clearable
      />
    </n-form-item>
    <n-form-item label="上级部门：">
      <n-tree-select
        v-model:value="paramForm.parentBranch"
        label-field="branchName"
        key-field="id"
        children-field="childrenBranch"
        :options="getDeptTree"
        placeholder="请选择上级部门"
      />
    </n-form-item>
    <n-form-item label="部门主管：">
      <n-select
        v-model:value="paramForm.branchManager"
        label-field="username"
        value-field="id"
        clearable
        placeholder="请选择部门主管"
        :options="parentOptions"
      />
    </n-form-item>
  </FormDialog>
</template>

<script lang="ts" setup>
  import FormDialog from '@/components/FormDialog/index.vue';
  import { computed, nextTick, reactive, ref, useTemplateRef } from 'vue';
  import rules from './rules';
  import { saveSystemDeptApi, getSystemDeptDetailApi, getSystemDeptUserApi } from '@/api/system';
  import { cloneDeep } from 'lodash-es';
  import { isEmpty } from '@/utils/is';
  const props = defineProps({
    formId: {
      type: Number,
      default: null,
    },
    deptTree: {
      type: Array,
      default: () => [],
    },
  });

  const formDialogRef = useTemplateRef<InstanceType<typeof FormDialog>>('formDialog');
  const formState = {
    id: null,
    branchName: '',
    parentBranch: null,
    branchManager: null,
  };
  let paramForm = reactive({ ...formState });
  const formDialogModel = ref(false);
  // 直属上级
  const parentOptions = ref([]);

  const getDeptTree = computed(() => {
    // 编辑状态禁用同一节点
    let departmentList = cloneDeep(props.deptTree);
    const filterCurId = (item, id) => {
      if (item.id === id) {
        return {
          ...item,
          disabled: true,
          childrenBranch: null,
        };
      } else if (item.childrenBranch && item.childrenBranch.length > 0) {
        item.childrenBranch = item.childrenBranch.map((el) => {
          el = filterCurId(el, id);
          return el;
        });
      }
      return { ...item, disabled: false };
    };

    return isEmpty(props.formId)
      ? departmentList
      : departmentList?.reduce((pre, cur) => {
          const curd = filterCurId(cur, props.formId);
          curd && pre.push(curd);
          return pre;
        }, []) ?? [];
  });

  const emits = defineEmits(['close']);
  // 判断使用新增、编辑接口
  const useHttpInterface = async (params) => {
    if (params.id) {
      // 编辑
      return await saveSystemDeptApi({
        ...params,
        id: props.formId,
      });
    } else {
      // 新增
      return await saveSystemDeptApi(params);
    }
  };
  const handleOpen = () => {
    getSystemDeptUser();
    props.formId &&
      nextTick(() => {
        formDialogRef.value?.loadFormData(() =>
          getSystemDeptDetailApi({
            branchId: props.formId,
          })
        );
      });
  };
  const handleSubmit = async (params, done) => {
    try {
      const flag = props.deptTree && props.deptTree.length > 0 && isEmpty(params.parentBranch);
      if (flag) {
        window.$message.error('请选择上级部门');
        return;
      }
      await useHttpInterface(params);
      window.$message.success('保存成功');
      formDialogModel.value = false;
    } finally {
      done();
    }
  };
  const handleClose = () => {
    Object.assign(paramForm, formState);
    emits('close');
  };
  const getSystemDeptUser = async () => {
    if (!props.formId) {
      parentOptions.value = [];
      return;
    }
    try {
      const { data } = await getSystemDeptUserApi({ branchId: props.formId });
      parentOptions.value = data || [];
    } catch (err) {
      console.log(err);
    }
  };

  defineExpose({
    formDialogModel,
  });
</script>
