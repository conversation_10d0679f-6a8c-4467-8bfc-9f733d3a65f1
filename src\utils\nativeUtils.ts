/**
 * Dialog 关闭结果接口
 */
export interface DialogCloseResult {
  /** 关闭来源 */
  source: 'positive' | 'negative' | 'close' | 'mask' | 'esc';
  /** 是否确认 */
  confirmed: boolean;
  /** 传递的数据 */
  data?: any;
  /** 原始选项 */
  options?: any;
  /** 关闭时间戳 */
  timestamp: number;
  /** Dialog 类型 */
  type?: string;
}

/**
 * useDialog 对该组件函数调用promise化
 * 增强版本：返回详细的关闭信息
 */
export function promisifyDialog(dialog: any) {
  /**
   * 创建 Promise 化的 dialog 方法
   */
  const createDialogMethod = (dialogMethod: Function) => {
    return (options: any = {}) => {
      return new Promise<DialogCloseResult>((resolve, reject) => {
        // 保存原始回调
        const originalOnPositiveClick = options.onPositiveClick;
        const originalOnNegativeClick = options.onNegativeClick;
        const originalOnClose = options.onClose;
        const originalOnMaskClick = options.onMaskClick;
        const originalOnEsc = options.onEsc;

        // 创建结果对象的工厂函数
        const createResult = (
          source: DialogCloseResult['source'],
          confirmed: boolean,
          data?: any
        ): DialogCloseResult => ({
          source,
          confirmed,
          data,
          options,
          timestamp: Date.now(),
        });

        try {
          const dialogInstance = dialogMethod({
            ...options,
            onPositiveClick: (e?: Event) => {
              try {
                // 执行原始回调
                const result = originalOnPositiveClick?.(e);

                // 如果原始回调返回 false，阻止关闭
                if (result === false) {
                  return false;
                }

                // 解析 Promise 并传递结果
                resolve(createResult('positive', true, result));
                return result;
              } catch (error) {
                reject(error);
                return false;
              }
            },
            onNegativeClick: (e?: Event) => {
              try {
                // 执行原始回调
                const result = originalOnNegativeClick?.(e);

                // 如果原始回调返回 false，阻止关闭
                if (result === false) {
                  return false;
                }

                // 解析 Promise 并传递结果
                resolve(createResult('negative', false, result));
                return result;
              } catch (error) {
                reject(error);
                return false;
              }
            },
            onClose: () => {
              try {
                const result = originalOnClose?.();
                resolve(createResult('close', false, result));
                return result;
              } catch (error) {
                reject(error);
              }
            },
            onMaskClick: (e?: Event) => {
              try {
                const result = originalOnMaskClick?.(e);
                if (result !== false) {
                  resolve(createResult('mask', false, result));
                }
                return result;
              } catch (error) {
                reject(error);
                return false;
              }
            },
            onEsc: (e?: Event) => {
              try {
                const result = originalOnEsc?.(e);
                if (result !== false) {
                  resolve(createResult('esc', false, result));
                }
                return result;
              } catch (error) {
                reject(error);
                return false;
              }
            },
          });

          // 如果 dialog 创建失败
          if (!dialogInstance) {
            reject(new Error(`Failed to create dialog`));
          }
        } catch (error) {
          reject(error);
        }
      });
    };
  };

  return createDialogMethod(dialog);
}
