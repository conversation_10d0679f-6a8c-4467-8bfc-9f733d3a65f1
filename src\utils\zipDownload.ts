import JSZip from 'jszip';
import { downloadByBlob, downloadByUrl } from './downloadFile';
import { getFileName } from './getFileTypeByUrl';

/**
 * 文件项接口
 */
export interface FileItem {
  url: string;
  filename?: string;
  params?: Record<string, any>;
}

/**
 * ZIP下载配置接口
 */
export interface ZipDownloadConfig {
  /** ZIP文件名，默认为 'files.zip' */
  zipFilename?: string;
  /** 是否显示进度提示，默认true */
  showProgress?: boolean;
  /** 是否显示结果提示，默认true */
  showResult?: boolean;
  /** 最大并发下载数，默认5 */
  maxConcurrent?: number;
  /** 下载超时时间（毫秒），默认30秒 */
  timeout?: number;
  /** 自定义文件名生成函数 */
  generateFileName?: (url: string, index: number) => string;
  /** 进度回调函数 */
  onProgress?: (current: number, total: number, filename: string) => void;
}

/**
 * ZIP下载结果接口
 */
export interface ZipDownloadResult {
  success: number;
  failed: number;
  total: number;
  zipSize: number; // ZIP文件大小（字节）
  errors: Array<{
    url: string;
    filename: string;
    error: any;
    index: number;
  }>;
}

/**
 * 从URL获取文件数据
 */
async function fetchFileData(
  url: string,
  params?: Record<string, any>,
  timeout = 30000
): Promise<ArrayBuffer> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 构建完整URL
    let fullUrl = url;
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams(params);
      fullUrl += (url.includes('?') ? '&' : '?') + searchParams.toString();
    }

    const response = await fetch(fullUrl, {
      signal: controller.signal,
      headers: {
        Accept: '*/*',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.arrayBuffer();
  } finally {
    clearTimeout(timeoutId);
  }
}

/**
 * 下载单个文件（不打包成ZIP）
 */
async function downloadSingleFile(
  file: string | FileItem,
  config: ZipDownloadConfig,
  result: ZipDownloadResult
): Promise<ZipDownloadResult> {
  const { showProgress = true, showResult = true, timeout = 30000, generateFileName } = config;

  // 标准化文件信息
  const fileItem: FileItem =
    typeof file === 'string'
      ? {
          url: file,
          filename: generateFileName
            ? generateFileName(file, 0)
            : getFileName(file) || 'download_file',
        }
      : {
          ...file,
          filename:
            file.filename ||
            (generateFileName ? generateFileName(file.url, 0) : getFileName(file.url)) ||
            'download_file',
        };

  showProgress && window.$message?.info(`开始下载文件: ${fileItem.filename}`);

  try {
    // 如果文件有参数，需要通过fetch下载
    if (fileItem.params && Object.keys(fileItem.params).length > 0) {
      console.log(`📥 通过fetch下载文件（带参数）: ${fileItem.url}`);
      const arrayBuffer = await fetchFileData(fileItem.url, fileItem.params, timeout);
      const blob = new Blob([arrayBuffer]);
      downloadByBlob(blob, fileItem.filename!);
    } else {
      // 尝试直接通过URL下载，如果失败则通过fetch下载
      console.log(`📥 尝试直接下载文件: ${fileItem.url}`);
      try {
        const success = await downloadByUrl({
          url: fileItem.url,
          fileName: fileItem.filename,
        });

        if (!success) {
          throw new Error('downloadByUrl returned false');
        }
      } catch (urlError) {
        const errorMessage = urlError instanceof Error ? urlError.message : String(urlError);
        console.log(`📥 直接下载失败，尝试通过fetch下载: ${errorMessage}`);
        // 如果直接下载失败，尝试通过fetch下载
        const arrayBuffer = await fetchFileData(fileItem.url, undefined, timeout);
        const blob = new Blob([arrayBuffer]);
        downloadByBlob(blob, fileItem.filename!);
      }
    }

    result.success = 1;
    showResult && window.$message?.success(`文件下载完成: ${fileItem.filename}`);
    console.log(`✅ 文件下载成功: ${fileItem.filename}`);
  } catch (error) {
    result.failed = 1;
    result.errors.push({
      url: fileItem.url,
      filename: fileItem.filename!,
      error,
      index: 0,
    });
    showResult && window.$message?.error(`文件下载失败: ${fileItem.filename}`);
    console.error(`❌ 文件下载失败:`, fileItem.url, error);
  }

  return result;
}

/**
 * 批量下载文件并压缩为ZIP
 * 当只有一个文件时，直接下载文件而不打包成ZIP
 */
export async function downloadFilesAsZip(
  files: (string | FileItem)[],
  config: ZipDownloadConfig = {}
): Promise<ZipDownloadResult> {
  const {
    zipFilename = 'files.zip',
    showProgress = true,
    showResult = true,
    maxConcurrent = 5,
    timeout = 30000,
    generateFileName,
    onProgress,
  } = config;

  const result: ZipDownloadResult = {
    success: 0,
    failed: 0,
    total: files.length,
    zipSize: 0,
    errors: [],
  };

  if (files.length === 0) {
    showResult && window.$message?.warning('没有文件需要下载');
    return result;
  }

  // 如果只有一个文件，直接下载而不打包成ZIP
  if (files.length === 1) {
    return await downloadSingleFile(files[0], config, result);
  }

  showProgress && window.$message?.info(`开始下载 ${files.length} 个文件并压缩...`);

  // 标准化文件列表
  const fileItems: FileItem[] = files.map((file, index) => {
    if (typeof file === 'string') {
      return {
        url: file,
        filename: generateFileName
          ? generateFileName(file, index)
          : getFileName(file) || `file_${index + 1}`,
      };
    }
    return {
      ...file,
      filename:
        file.filename ||
        (generateFileName ? generateFileName(file.url, index) : getFileName(file.url)) ||
        `file_${index + 1}`,
    };
  });

  const zip = new JSZip();
  const downloadPromises: Promise<void>[] = [];

  // 分批处理下载
  for (let i = 0; i < fileItems.length; i += maxConcurrent) {
    const batch = fileItems.slice(i, i + maxConcurrent);

    const batchPromises = batch.map(async (item, batchIndex) => {
      const globalIndex = i + batchIndex;
      try {
        onProgress?.(globalIndex + 1, fileItems.length, item.filename!);

        const arrayBuffer = await fetchFileData(item.url, item.params, timeout);

        // 确保文件名唯一
        let finalFilename = item.filename!;
        let counter = 1;
        while (zip.file(finalFilename)) {
          const ext = finalFilename.split('.').pop();
          const nameWithoutExt = finalFilename.replace(`.${ext}`, '');
          finalFilename = `${nameWithoutExt}_${counter}.${ext}`;
          counter++;
        }

        zip.file(finalFilename, arrayBuffer);
        result.success++;

        console.log(`✅ 第 ${globalIndex + 1} 个文件下载成功: ${finalFilename}`);
      } catch (error) {
        result.failed++;
        result.errors.push({
          url: item.url,
          filename: item.filename!,
          error,
          index: globalIndex,
        });
        console.error(`❌ 第 ${globalIndex + 1} 个文件下载失败:`, item.url, error);
      }
    });

    downloadPromises.push(...batchPromises);

    // 等待当前批次完成再处理下一批
    await Promise.allSettled(batchPromises);
  }

  // 等待所有下载完成
  await Promise.allSettled(downloadPromises);

  // 生成ZIP文件
  if (result.success > 0) {
    try {
      showProgress && window.$message?.info('正在生成ZIP文件...');

      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6, // 压缩级别 1-9，6是平衡点
        },
      });

      result.zipSize = zipBlob.size;

      // 下载ZIP文件
      downloadByBlob(zipBlob, zipFilename);

      console.log(
        `✅ ZIP文件生成成功: ${zipFilename}, 大小: ${(zipBlob.size / 1024 / 1024).toFixed(2)}MB`
      );
    } catch (error) {
      console.error('❌ ZIP文件生成失败:', error);
      showResult && window.$message?.error('ZIP文件生成失败');
      return result;
    }
  }

  // 显示结果
  if (showResult) {
    if (result.failed === 0) {
      window.$message?.success(
        `ZIP文件下载完成！包含 ${result.success} 个文件，大小: ${(
          result.zipSize /
          1024 /
          1024
        ).toFixed(2)}MB`
      );
    } else if (result.success > 0) {
      window.$message?.warning(
        `ZIP文件下载完成！成功: ${result.success} 个，失败: ${result.failed} 个`
      );
    } else {
      window.$message?.error('所有文件下载失败，无法生成ZIP文件');
    }
  }

  return result;
}

/**
 * 快速下载多个PDF文件为ZIP（兼容原有代码）
 */
export async function downloadPdfsAsZip(
  urls: string[],
  config: Omit<ZipDownloadConfig, 'generateFileName'> & {
    generateFileName?: (url: string, index: number) => string;
  } = {}
): Promise<ZipDownloadResult> {
  return downloadFilesAsZip(urls, {
    ...config,
    zipFilename: config.zipFilename || 'documents.zip',
    generateFileName:
      config.generateFileName ||
      ((url, index) => {
        const fileName = getFileName(url);
        return fileName || `document_${index + 1}.pdf`;
      }),
  });
}

/**
 * 下载签约相关文档为ZIP（业务场景专用）
 */
export async function downloadContractFilesAsZip(
  urls: string[],
  contractId?: string | number
): Promise<ZipDownloadResult> {
  const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const zipFilename = contractId
    ? `合同文档_${contractId}_${timestamp}.zip`
    : `合同文档_${timestamp}.zip`;

  return downloadFilesAsZip(urls, {
    zipFilename,
    showProgress: true,
    showResult: true,
    maxConcurrent: 3, // 合同文档通常较大，降低并发数
    generateFileName: (url, index) => {
      const fileName = getFileName(url);
      if (fileName) return fileName;

      // 根据索引生成有意义的文件名
      const fileTypes = ['签约协议', '补充协议', '存证文件', '附件'];
      const fileType = fileTypes[index] || '文档';
      return `${fileType}_${index + 1}.pdf`;
    },
    onProgress: (current, total, filename) => {
      console.log(`正在下载第 ${current}/${total} 个文件: ${filename}`);
    },
  });
}
