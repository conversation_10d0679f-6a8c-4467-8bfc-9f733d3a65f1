# ZIP压缩下载功能使用指南

本项目提供了完整的多文件ZIP压缩下载解决方案，将多个文件打包成一个ZIP文件后下载，提供更好的用户体验。

## 功能特性

- ✅ **ZIP压缩**: 将多个文件压缩成一个ZIP包下载
- ✅ **并发下载**: 支持并发获取文件，提高下载效率
- ✅ **智能超时**: 可配置下载超时时间，避免长时间等待
- ✅ **进度提示**: 支持下载进度显示和结果反馈
- ✅ **错误处理**: 完善的错误处理，部分失败不影响整体
- ✅ **文件名管理**: 自动处理重名文件，支持自定义命名
- ✅ **类型安全**: 完整的 TypeScript 类型支持
- ✅ **业务集成**: 专门为签约文档等业务场景优化

## 快速开始

### 1. 基础用法

```typescript
import { downloadFilesAsZip } from '@/utils/zipDownload';

// 简单下载多个文件为ZIP
const urls = [
  'https://example.com/file1.pdf',
  'https://example.com/file2.pdf',
  'https://example.com/file3.pdf'
];

const result = await downloadFilesAsZip(urls);
console.log('ZIP下载结果:', result);
```

### 2. 自定义配置

```typescript
import { downloadFilesAsZip, type ZipDownloadConfig } from '@/utils/zipDownload';

const config: ZipDownloadConfig = {
  zipFilename: '合同文档.zip',    // 自定义ZIP文件名
  showProgress: true,           // 显示进度提示
  showResult: true,            // 显示结果提示
  maxConcurrent: 3,            // 最大并发下载数
  timeout: 30000,              // 超时时间30秒
  generateFileName: (url, index) => `文档_${index + 1}.pdf`,
  onProgress: (current, total, filename) => {
    console.log(`下载进度: ${current}/${total} - ${filename}`);
  }
};

const result = await downloadFilesAsZip(urls, config);
```

### 3. 在您的业务场景中使用

```typescript
// 在 LoanRecord.vue 中的实际应用
async function handleDownload(row) {
  const { data: protocolList } = await getESignProtocol(row.id);
  
  if (!protocolList || protocolList.length === 0) {
    window.$message.error('暂无存证下载');
    return;
  }

  // 使用专门的合同文档下载函数
  const result = await downloadContractFilesAsZip(protocolList, row.id);
  console.log('下载结果:', result);
}
```

## API 参考

### downloadFilesAsZip

通用的多文件ZIP下载函数。

```typescript
function downloadFilesAsZip(
  files: (string | FileItem)[],
  config?: ZipDownloadConfig
): Promise<ZipDownloadResult>
```

### downloadContractFilesAsZip

专门用于下载合同文档的函数，针对业务场景优化。

```typescript
function downloadContractFilesAsZip(
  urls: string[],
  contractId?: string | number
): Promise<ZipDownloadResult>
```

### downloadPdfsAsZip

专门用于下载PDF文件的函数。

```typescript
function downloadPdfsAsZip(
  urls: string[],
  config?: ZipDownloadConfig
): Promise<ZipDownloadResult>
```

### 配置选项 (ZipDownloadConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `zipFilename` | `string` | `'files.zip'` | ZIP文件名 |
| `showProgress` | `boolean` | `true` | 是否显示进度提示 |
| `showResult` | `boolean` | `true` | 是否显示结果提示 |
| `maxConcurrent` | `number` | `5` | 最大并发下载数 |
| `timeout` | `number` | `30000` | 下载超时时间（毫秒） |
| `generateFileName` | `function` | - | 自定义文件名生成函数 |
| `onProgress` | `function` | - | 进度回调函数 |

### 文件项接口 (FileItem)

```typescript
interface FileItem {
  url: string;              // 文件URL
  filename?: string;        // 自定义文件名
  params?: Record<string, any>; // URL参数
}
```

### 返回结果 (ZipDownloadResult)

```typescript
interface ZipDownloadResult {
  success: number;    // 成功下载的文件数
  failed: number;     // 失败的文件数
  total: number;      // 总文件数
  zipSize: number;    // ZIP文件大小（字节）
  errors: Array<{     // 错误详情
    url: string;
    filename: string;
    error: any;
    index: number;
  }>;
}
```

## 实际应用场景

### 1. 签约文档下载（当前业务）

```typescript
// 替换原来的多文件下载
async function handleDownload(row) {
  const { data: protocolList } = await getESignProtocol(row.id);
  
  if (!protocolList?.length) {
    window.$message.error('暂无存证下载');
    return;
  }

  // 一键打包下载所有文档
  await downloadContractFilesAsZip(protocolList, row.id);
}
```

### 2. 用户资料批量下载

```typescript
const userFiles: FileItem[] = [
  {
    url: '/api/download/idcard-front',
    filename: '身份证正面.jpg',
    params: { userId: '123', token: userToken }
  },
  {
    url: '/api/download/idcard-back', 
    filename: '身份证反面.jpg',
    params: { userId: '123', token: userToken }
  }
];

await downloadFilesAsZip(userFiles, {
  zipFilename: '用户资料.zip'
});
```

### 3. 带进度显示的下载

```typescript
const result = await downloadFilesAsZip(urls, {
  zipFilename: '项目文档.zip',
  onProgress: (current, total, filename) => {
    const percentage = Math.round((current / total) * 100);
    // 更新进度条UI
    updateProgressBar(percentage, filename);
  }
});
```

## 优势对比

### 🆚 原来的多文件下载 vs 新的ZIP压缩下载

| 特性 | 原多文件下载 | ZIP压缩下载 |
|------|-------------|------------|
| **用户体验** | 多个文件分别下载，容易丢失 | 一个ZIP包，整齐有序 |
| **浏览器兼容** | 容易被浏览器拦截 | 单文件下载，无拦截风险 |
| **文件管理** | 文件散乱，难以管理 | 统一打包，便于管理 |
| **下载速度** | 受浏览器并发限制 | 可控制并发，更高效 |
| **错误处理** | 部分失败影响体验 | 部分失败仍可获得其他文件 |
| **存储空间** | 原始文件大小 | 压缩后更小 |

## 最佳实践

### 1. 合理设置并发数

```typescript
// 小文件（<1MB）可以设置较高并发
const smallFilesConfig = {
  maxConcurrent: 8,
  timeout: 15000
};

// 大文件（>10MB）建议降低并发
const largeFilesConfig = {
  maxConcurrent: 2,
  timeout: 60000
};
```

### 2. 错误处理策略

```typescript
const result = await downloadFilesAsZip(urls, {
  showResult: false // 自定义结果处理
});

if (result.failed > 0) {
  if (result.success > 0) {
    window.$message.warning(
      `部分文件下载成功，ZIP包含 ${result.success} 个文件`
    );
  } else {
    window.$message.error('所有文件下载失败');
  }
}
```

### 3. 文件名优化

```typescript
const config = {
  generateFileName: (url, index) => {
    // 提取原始文件名
    const originalName = getFileName(url);
    
    // 业务相关的文件名映射
    const businessNames = {
      'protocol': '签约协议',
      'supplement': '补充协议', 
      'evidence': '存证文件'
    };
    
    // 根据URL特征生成有意义的文件名
    for (const [key, name] of Object.entries(businessNames)) {
      if (url.includes(key)) {
        return `${name}.pdf`;
      }
    }
    
    return originalName || `文档_${index + 1}.pdf`;
  }
};
```

## 注意事项

1. **文件大小限制**: 浏览器内存限制，建议单个ZIP包不超过500MB
2. **网络超时**: 根据文件大小和网络情况合理设置超时时间
3. **并发控制**: 过高的并发可能导致服务器压力，建议不超过10
4. **文件名处理**: 自动处理重名文件，确保ZIP包内文件名唯一
5. **错误恢复**: 部分文件下载失败不影响其他文件的打包

## 故障排除

### 常见问题

1. **ZIP文件生成失败**: 检查是否有文件下载成功，空ZIP无法生成
2. **下载超时**: 增加 `timeout` 配置或降低 `maxConcurrent`
3. **文件名乱码**: 确保 `generateFileName` 返回正确编码的文件名
4. **内存不足**: 减少并发数或分批处理大量文件

### 调试技巧

```typescript
// 开启详细日志
const result = await downloadFilesAsZip(urls, {
  onProgress: (current, total, filename) => {
    console.log(`[${current}/${total}] 正在下载: ${filename}`);
  }
});

// 检查错误详情
result.errors.forEach(error => {
  console.error('下载失败:', error.filename, error.error);
});

// 检查ZIP文件信息
console.log(`ZIP文件大小: ${(result.zipSize / 1024 / 1024).toFixed(2)}MB`);
```

## 迁移指南

如果您之前使用的是多文件下载，迁移到ZIP下载非常简单：

```typescript
// 原来的代码
await downloadMultipleFiles(urls);

// 新的代码
await downloadFilesAsZip(urls);
```

主要变化：
- 下载结果从多个文件变为一个ZIP文件
- 配置选项略有不同，但大部分兼容
- 错误处理逻辑保持一致
