<template>
  <n-space vertical>
    <n-card>
      <!-- 发起进件 -->
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="发起进件" :status="1" />
          </template>

          <div>
            <n-form
              ref="formRef"
              label-placement="left"
              size="medium"
              :model="formModel"
              :rules="formRules"
              label-width="120px"
            >
              <!-- 身份证上传区域 -->
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="身份证国徽面" path="emblemImage">
                    <UploadFile
                      v-model:fileList="formModel.emblemImage"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="5"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证人像面" path="portraitImage">
                    <UploadFile
                      v-model:fileList="formModel.portraitImage"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="5"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="姓名" path="name">
                    <n-input v-model:value="formModel.name" placeholder="请输入姓名" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="手机号" path="phone">
                    <n-input
                      v-model:value="formModel.phone"
                      placeholder="请输入手机号"
                      maxlength="11"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证号" path="idCard">
                    <n-input
                      v-model:value="formModel.idCard"
                      placeholder="请输入身份证号"
                      maxlength="18"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-flex justify="center">
                <n-button type="info" size="large" @click="handleSubmit"> 确认提交 </n-button>
              </n-flex>
            </n-form>

            <n-divider dashed />

            <SubTitle title="发起进件" desc="前置要求：姓名、手机号、身份证号完整" />

            <n-data-table :columns="columns" :data="data" />
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <n-card>
      <!-- 授权书签署 -->
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="授权书签署" :status="2" />
          </template>

          <SubTitle title="授权书签署" />
          <n-data-table :columns="columns2" :data="data2" />
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <n-card>
      <!-- 预审阶段 -->
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="预审阶段" :status="2" />
          </template>

          <n-form
            ref="formRef2"
            label-placement="left"
            size="medium"
            :model="formModel2"
            :rules="formRules2"
            label-width="120px"
          >
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="申请城市" path="area">
                  <CitySelect v-model="formModel2.area" change-on-select :multiple="false" />
                </n-form-item>
              </n-grid-item>
            </n-grid>
            <n-flex justify="center">
              <n-button type="info" size="large" @click="handleSubmit2"> 确认提交 </n-button>
            </n-flex>
          </n-form>
          <n-divider dashed />
          <SubTitle
            title="授权书签署"
            desc="前置要求：姓名、手机号、申请城市、身份证号完整、身份证正反面"
          />
          <n-data-table :columns="columns2" :data="data2" />
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import CitySelect from '@/components/CitySelect/index.vue';

  import { ref, reactive } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';

  const formRef = ref<FormInst | null>(null);
  const formModel = reactive({
    name: '',
    phone: '',
    idCard: '',
    emblemImage: '',
    portraitImage: '',
  });
  const formRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    idCard: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      {
        pattern:
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入正确的身份证号',
        trigger: 'blur',
      },
    ],
    emblemImage: [{ required: true, message: '请上传身份证国徽面', trigger: 'change' }],
    portraitImage: [{ required: true, message: '请上传身份证人像面', trigger: 'change' }],
  };

  const columns = [
    {
      title: '进件状态',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];
  const data = [
    {
      key: '1',
      status: '待审核',
    },
  ];

  const handleSubmit = async () => {
    await formRef.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交表单:', formModel);
      window.$message?.success('提交成功');
    });
  };

  const formRef2 = ref<FormInst | null>(null);
  const formModel2 = reactive({
    area: '',
  });
  const formRules2 = {
    area: [{ required: true, message: '请选择申请城市', trigger: 'blur' }],
  };

  const handleSubmit2 = async () => {
    await formRef2.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交表单:', formModel2);
      window.$message?.success('提交成功');
    });
  };

  const columns2 = [
    {
      title: '授权书签署地址',
      key: 'address',
      align: 'center',
    },
    {
      title: '签署状态',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];
  const data2 = [];
</script>

<style lang="less" scoped></style>
