import { mountComponent } from '@/utils/mountComponent';
import type { MountOptions, RenderCtx, UnmountFn } from '@/utils/mountComponent';
import { getCurrentInstance, VNode } from 'vue';

export type UnmountPromisifyFn<T> = (arg?: T) => void;
export type RenderInputPromisifyFn<T> = (
  ctx: RenderCtx & { unmount: UnmountPromisifyFn<T> }
) => VNode;

export function useMountComponent() {
  const appContext = getCurrentInstance()!.appContext;

  function mount(param: MountOptions): UnmountFn {
    return mountComponent(Object.assign({ appContext }, param) as MountOptions);
  }

  function mountPromisify<T>(
    param: Omit<MountOptions, 'render'> & { render: RenderInputPromisifyFn<T> }
  ): Promise<T> {
    return new Promise<T>((resolve) => {
      mountComponent({
        appContext,
        ...(param as Omit<MountOptions, 'render'>),
        render: (ctx: RenderCtx) =>
          (param.render as RenderInputPromisifyFn<T>)({
            ...(ctx as RenderCtx),
            unmount: (arg?: T) => {
              resolve(arg as T);
              ctx.unmount();
            },
          }),
      } as MountOptions);
    });
  }

  return {
    mount,
    mountPromisify,
  };
}
