<template>
  <n-card :bordered="false">
    <BasicForm
      :enable-cache="true"
      :showSlotConfig="true"
      @register="register"
      @submit="reloadTable"
      @reset="reloadTable"
    >
      <template #actionButton>
        <n-button
          v-permission="{ action: 'export' }"
          :loading="exportLoading"
          type="warning"
          @click="handleExport"
        >
          导出
        </n-button>
      </template>
    </BasicForm>
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      ref="action"
      :actionColumn="actionColumn"
      :scroll-x="2490"
      :striped="true"
    />
  </n-card>
</template>

<script setup lang="tsx">
  import { columns as baseColumns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { onActivated, reactive, useTemplateRef } from 'vue';
  import { getSystemConfigListApi } from '@/api/system';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import dayjs from 'dayjs';
  import { exportFollowUpListApi } from '@/api/dashboard/workplace';
  import { useUser } from '@/store/modules/user';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';

  defineOptions({
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'statistics_seller_data_monitor',
  });

  const router = useRouter();

  const exportLoading = ref(false);
  const userStore = useUser();

  const schemas: FormSchema[] = [
    {
      field: 'date',
      component: 'NDatePicker',
      label: '日期',
      childKey: ['triageStartTime', 'triageEndTime'],
      defaultValue: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      componentProps: {
        type: 'daterange',
        'value-format': 'yyyy-MM-dd',
        format: 'yyyy-MM-dd',
        'is-date-disabled': createMonthRangeDisabledFn(3),
        clearable: false,
      },
    },
    // 渠道来源下拉选：下拉单选，下拉枚举为已添加的渠道来源名称
    {
      field: 'channelCode',
      component: 'NSelect',
      label: '渠道来源',
      componentProps: {
        placeholder: '请选择渠道来源',
        options: [],
      },
    },
    // 来源渠道输入框：支持输入任意字符，优先精确查询，精确查询无结果时模糊查询
    {
      field: 'mediaPlatformSource',
      component: 'NInput',
      label: '来源渠道',
      componentProps: {
        placeholder: '请输入来源渠道',
      },
    },
    // 跟进人下拉选：下拉单选，下拉枚举为存在工作机拨打权限的用户姓名；该筛选项仅针对分配客户数之后的数据生效
    {
      field: 'clueFollower',
      component: 'NSelect',
      label: '跟进人',
      componentProps: {
        placeholder: '请选择跟进人',
        options: [],
      },
    },
  ];
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });

  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');

  const columns = [
    {
      title: '日期',
      key: 'date',
      width: 80,
      align: 'center',
    },
    ...baseColumns,
  ];

  const actionColumn = reactive({
    width: 120,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(record) {
      return (
        <n-button
          type="primary"
          text
          on-click={() => {
            router.push({
              path: '/statistics/seller-data-monitor-detail',
              query: {
                ...getFieldsValue(),
                date: record.date,
              },
            });
          }}
        >
          渠道详情
        </n-button>
      );
    },
  });

  const loadDataTable = async (res) => {
    const { data } = await getSystemConfigListApi({ ...getFieldsValue(), ...res });

    return data;
  };

  function reloadTable() {
    actionRef.value!.reload();
  }

  function handleExport() {
    try {
      exportLoading.value = true;
      window.open(
        exportFollowUpListApi({
          ...getFieldsValue(),
          pageNumber: 1,
          pageSize: 10000,
          token: userStore.getToken,
        })
      );
    } finally {
      exportLoading.value = false;
    }
  }

  onActivated(() => {
    reloadTable();
  });
</script>
