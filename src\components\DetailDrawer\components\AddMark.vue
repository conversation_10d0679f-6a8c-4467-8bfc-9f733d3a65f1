<template>
  <n-form ref="formRef" :model="formModel" :rules="rules" label-placement="left">
    <!-- 跟进备注 -->
    <n-form-item path="remark" label="跟进备注:">
      <n-input
        v-model:value="formModel.remark"
        type="textarea"
        :autosize="{ minRows: 3 }"
        maxlength="200"
        show-count
      />
    </n-form-item>
  </n-form>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  interface formModelType {
    remark: string;
    id?: number | string;
    clueId?: number | string;
  }
  const formRef = ref();
  const formModel = defineModel<formModelType>('formModel', {
    default: () => ({ remark: '', id: null, clueId: null }),
  });
  const rules = {
    remark: [
      {
        required: true,
        message: '请输入备注',
        trigger: 'change',
      },
    ],
  };
  async function validateForm() {
    return formRef.value?.validate();
  }
  defineExpose({ validateForm });
</script>
