/**
 * 通过 URL 判断文件类型
 * @param url 文件链接字符串
 * @returns 返回类型字符串：'image' | 'video' | 'audio' | 'pdf' | 'doc' | 'xls' | 'zip' | 'unknown'
 */
export function getFileTypeByUrl(url: string): string {
  if (!url) return 'unknown';

  const cleanUrl = url.split('?')[0].split('#')[0].toLowerCase();

  // 提取后缀名
  const match = cleanUrl.match(/\.(\w+)$/);
  if (!match) return 'unknown';

  const ext = match[1];

  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const videoExts = ['mp4', 'avi', 'mkv', 'mov', 'webm'];
  const audioExts = ['mp3', 'wav', 'ogg', 'flac'];
  const pdfExts = ['pdf'];
  const docExts = ['doc', 'docx'];
  const xlsExts = ['xls', 'xlsx'];
  const zipExts = ['zip', 'rar', '7z'];

  if (imageExts.includes(ext)) return 'image';
  if (videoExts.includes(ext)) return 'video';
  if (audioExts.includes(ext)) return 'audio';
  if (pdfExts.includes(ext)) return 'pdf';
  if (docExts.includes(ext)) return 'doc';
  if (xlsExts.includes(ext)) return 'xls';
  if (zipExts.includes(ext)) return 'zip';

  return 'unknown';
}

/**
 * 获取文件名（带扩展名）
 * @param fileOrUrl File 对象 或 URL 字符串
 * @returns 文件名字符串（如 "example.jpg"）
 */
export function getFileName(fileOrUrl: File | string): string {
  if (!fileOrUrl) return '';

  // 如果是 File 对象，直接返回 name 属性
  if (fileOrUrl instanceof File) {
    return fileOrUrl.name;
  }

  // 如果是字符串，提取路径中的文件名部分
  try {
    const url = new URL(fileOrUrl, 'http://dummy-base'); // 兼容相对路径
    const pathname = url.pathname;
    return decodeURIComponent(pathname.substring(pathname.lastIndexOf('/') + 1));
  } catch (e) {
    // 如果是相对路径或无效 URL，使用正则兜底
    const parts = fileOrUrl.split(/[\/\\]/);
    return decodeURIComponent(parts[parts.length - 1] || '');
  }
}
