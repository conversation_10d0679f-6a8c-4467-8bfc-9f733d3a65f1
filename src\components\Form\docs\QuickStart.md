# 表单缓存功能快速开始

## 5分钟上手表单缓存

### 1. 基础启用

最简单的方式，只需要添加 `enable-cache` 属性：

```vue
<template>
  <BasicForm
    :schemas="schemas"
    :enable-cache="true"
    @submit="handleSubmit"
  />
</template>
```

这样表单就会：
- ✅ 自动保存用户输入的数据
- ✅ 自动保存表单的展开收起状态
- ✅ 页面刷新后自动恢复数据和状态
- ✅ 使用当前路由路径作为缓存key
- ✅ 缓存到当天24点自动过期

### 2. 同一页面多个表单

如果一个页面有多个表单，需要设置不同的缓存key：

```vue
<template>
  <div>
    <!-- 用户信息表单 -->
    <BasicForm
      :schemas="userSchemas"
      :enable-cache="true"
      cache-key="user-info"
    />
    
    <!-- 联系方式表单 -->
    <BasicForm
      :schemas="contactSchemas"
      :enable-cache="true"
      cache-key="contact-info"
    />
  </div>
</template>
```

### 3. 手动控制缓存

如果需要手动控制缓存操作：

```vue
<template>
  <div>
    <n-space>
      <n-button @click="saveCache">保存缓存</n-button>
      <n-button @click="restoreCache">恢复缓存</n-button>
      <n-button @click="clearCache">清除缓存</n-button>
    </n-space>
    
    <BasicForm
      ref="formRef"
      :schemas="schemas"
      :enable-cache="true"
      :auto-restore-cache="false"
    />
  </div>
</template>

<script setup>
const formRef = ref();

function saveCache() {
  formRef.value?.saveFormCache?.();
}

function restoreCache() {
  const success = formRef.value?.restoreFormCache?.();
  if (success) {
    window.$message?.success('恢复成功');
  }
}

function clearCache() {
  formRef.value?.clearFormCache?.();
  window.$message?.success('缓存已清除');
}
</script>
```

### 4. 常用配置

```vue
<template>
  <BasicForm
    :schemas="schemas"
    :enable-cache="true"
    cache-key="my-form"
    :cache-timeout="60 * 60 * 24 * 7"
    :auto-restore-cache="true"
  />
</template>
```

配置说明：
- `cache-key="my-form"`: 自定义缓存标识
- `:cache-timeout="60 * 60 * 24 * 7"`: 缓存7天后过期（不设置则缓存到当天24点）
- `:auto-restore-cache="true"`: 自动恢复缓存（默认开启）

## 常见问题

### Q: 什么时候会自动保存缓存？
A: 当表单字段值发生变化或展开收起状态改变时会自动保存，使用了防抖机制避免频繁操作。

### Q: 哪些数据会被缓存？
A: 只有非空、非null、非空字符串的字段值会被缓存，同时会缓存表单的展开收起状态，避免存储无意义的数据。

### Q: 缓存存储在哪里？
A: 使用浏览器的 localStorage，通过项目的 Storage 工具类管理，支持过期时间。默认缓存到当天24点。

### Q: 如何处理敏感数据？
A: 对于包含敏感信息的表单，建议不启用缓存功能，或者设置较短的过期时间。

### Q: 缓存会影响性能吗？
A: 缓存操作经过优化，使用防抖机制和异步处理，对性能影响很小。

## 最佳实践

1. **搜索表单**：建议启用缓存，保留用户搜索条件
2. **长表单**：建议启用缓存，避免意外丢失数据
3. **敏感表单**：不建议启用缓存，或设置短过期时间
4. **多表单页面**：必须设置不同的 cache-key
5. **临时表单**：可以设置较短的过期时间

## 完整示例

```vue
<template>
  <n-card title="搜索表单（支持展开收起缓存）">
    <template #header-extra>
      <n-space>
        <n-tag :type="hasCache ? 'success' : 'default'">
          {{ hasCache ? '有缓存' : '无缓存' }}
        </n-tag>
        <n-button size="small" @click="clearCache">
          清除缓存
        </n-button>
      </n-space>
    </template>

    <BasicForm
      ref="formRef"
      :schemas="schemas"
      :enable-cache="true"
      cache-key="search-form"
      layout="inline"
      :show-advanced-button="true"
      :collapsed-rows="1"
      @submit="handleSubmit"
      @reset="handleReset"
    />
  </n-card>
</template>

<script setup>
import { ref, computed } from 'vue';

const formRef = ref();

const schemas = [
  {
    field: 'keyword',
    label: '关键词',
    component: 'NInput',
    componentProps: {
      placeholder: '请输入关键词',
    },
  },
  {
    field: 'category',
    label: '分类',
    component: 'NSelect',
    componentProps: {
      placeholder: '请选择分类',
      options: [
        { label: '全部', value: '' },
        { label: '产品', value: 'product' },
        { label: '服务', value: 'service' },
      ],
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'NSelect',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' },
      ],
    },
  },
  {
    field: 'dateRange',
    label: '日期范围',
    component: 'NDatePicker',
    componentProps: {
      type: 'daterange',
      format: 'yyyy-MM-dd',
    },
  },
];

const hasCache = computed(() => {
  return formRef.value?.hasCacheData?.() || false;
});

function handleSubmit(values) {
  console.log('搜索:', values);
}

function handleReset() {
  console.log('重置');
}

function clearCache() {
  formRef.value?.clearFormCache?.();
  window.$message?.success('缓存已清除');
}
</script>
```

现在你已经掌握了表单缓存功能的完整用法！🎉

**特别提示**：
- 🔄 展开收起状态会自动缓存和恢复
- 📱 适合搜索表单、筛选表单等场景
- 🎯 每天24点自动清除，保持数据新鲜
