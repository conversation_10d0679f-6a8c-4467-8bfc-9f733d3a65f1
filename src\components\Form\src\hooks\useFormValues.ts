import { isArray, isFunction, isObject, isString, isNullOrUnDef } from '@/utils/is';
import { unref } from 'vue';
import type { Ref, ComputedRef } from 'vue';
import type { FormSchema } from '../types/form';
import { set } from 'lodash-es';

interface UseFormValuesContext {
  defaultFormModel: Ref<any>;
  getSchema: ComputedRef<FormSchema[]>;
  formModel: Recordable;
  getActiveColumns: () => string[];
  restoreFormCache?: () => boolean; // 恢复缓存的函数
  enableCache?: boolean; // 是否启用缓存
}
export function useFormValues({
  defaultFormModel,
  getSchema,
  formModel,
  getActiveColumns,
  restoreFormCache,
  enableCache,
}: UseFormValuesContext) {
  // 加工 form values
  function handleFormValues(values: Recordable) {
    if (!isObject(values)) {
      return {};
    }
    const res: Recordable = {};
    // 激活列
    const activeColumns = getActiveColumns();
    for (const item of Object.entries(values)) {
      let [, value] = item;
      const [key] = item;
      if (
        !key ||
        (isArray(value) && value.length === 0) ||
        isFunction(value) ||
        isNullOrUnDef(value) ||
        !activeColumns.includes(key)
      ) {
        continue;
      }
      // 删除空格
      if (isString(value)) {
        value = value.trim();
      }
      set(res, key, value);
    }
    return res;
  }

  //初始化默认值
  function initDefault() {
    const schemas = unref(getSchema);
    const obj: Recordable = {};

    // 1. 首先收集所有schema中的默认值，用于重置功能
    schemas.forEach((item) => {
      const { defaultValue } = item;
      if (!isNullOrUnDef(defaultValue)) {
        obj[item.field] = defaultValue;
      }
    });

    // 2. 保存默认值模型（用于重置）
    defaultFormModel.value = obj;

    // 3. 优先级：缓存数据 > schema默认值 > 空值
    let cacheRestored = false;

    // 4. 如果启用缓存，先尝试恢复缓存数据
    if (enableCache && restoreFormCache) {
      cacheRestored = restoreFormCache();
    }

    // 5. 如果没有恢复缓存数据，则使用schema中的默认值
    if (!cacheRestored) {
      schemas.forEach((item) => {
        const { defaultValue } = item;
        if (!isNullOrUnDef(defaultValue)) {
          formModel[item.field] = defaultValue;
        }
      });
    }

    // 6. 如果恢复了缓存数据，返回true表示需要自动查询
    return cacheRestored;
  }

  return { handleFormValues, initDefault };
}
