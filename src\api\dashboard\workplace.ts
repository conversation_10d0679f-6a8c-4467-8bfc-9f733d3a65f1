import { get, post } from '@/utils/lib/axios.package';
import qs from 'qs';

/**
 * @description: 跟进页面
 */
export function getFollowUpListApi(params) {
  return get('/cms/workspace/follow-up/page', params);
}

/**
 * @description: 跟进页面导出
 */
export function exportFollowUpListApi(params) {
  return `${import.meta.env.VITE_GLOB_API_URL}/cms/workspace/export/follow-up/page?${qs.stringify(
    params
  )}`;
}

interface IClueLikeParams {
  clueId: number;
  intent?: number;
  tags?: string;
  likeStatus?: number;
  clueType?: number;
}
/**
 * @description: 关注线索
 */
export function clueLikeApi(params: IClueLikeParams) {
  return post(`/cms/workspace/like`, params);
}

/**
 * 待领取线索条数
 */
export function clueCountApi() {
  return get(`/cms/public-clue/unReceive/count`);
}
