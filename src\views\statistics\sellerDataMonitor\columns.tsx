import { BasicColumn } from '@/components/Table';
import { createTitleWithTooltip } from '@/utils/tableUtils';

export interface ListData {
  id: number;
  paramName: string;
}

export const columns: BasicColumn<ListData>[] = [
  {
    title: createTitleWithTooltip({
      title: '撞库请求数',
      tooltip: '三方撞库请求线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '撞库成功数',
      tooltip: '三方撞库请求成功线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '进件请求数',
      tooltip: '三方进件请求线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '进件成功数',
      tooltip: '三方进件请求成功线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '分配客户数',
      tooltip: '已分配的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '待跟进客户数',
      tooltip: '已分配并且待跟进的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '跟进客户数',
      tooltip: '已分配并且已跟进的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '跟进完成客户数',
      tooltip: '已分配并且已跟进结束的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '预审生成数',
      tooltip: '预审生成的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '预审审批数',
      tooltip: '预审审批通过的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '客服电核数',
      tooltip: '客服电核通过的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '授信通过数',
      tooltip: '授信通过的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '放款数',
      tooltip: '已放款的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '无效线索数',
      tooltip: '无线线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
  {
    title: createTitleWithTooltip({
      title: '回调无效线索数',
      tooltip: '无线线索并且已回调三方的线索数，线索ID去重',
    }),
    key: 'paramName',
    align: 'center',
  },
];
