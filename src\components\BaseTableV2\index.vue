<template>
  <!-- <n-data-table
      v-bind="{...$attrs,}"
    /> -->
  <component
    :is="
      h(
        NDataTable,
        {
          ...$attrs,
          ...($attrs.scrollX !== undefined ? { scrollX: 0 } : {}),
          ref: changeRef,
          columns: showColumns,
        },
        $slots
      )
    "
  />
</template>
<script lang="ts" setup>
  import type { TableBaseColumn } from 'naive-ui/lib/data-table/src/interface';

  import { NDataTable } from 'naive-ui';
  import type { ComponentInstance } from 'vue';
  import { h, getCurrentInstance, defineProps } from 'vue';
  const props = defineProps({
    columns: {
      type: Array as PropType<TableBaseColumn<any>[]>,
      default: () => [],
    },
  });

  const showColumns = handleColumns(props.columns);
  function handleColumns(list) {
    return list?.map((item) => {
      return {
        ...(item.type === 'selection' || item.type === 'expand' ? {} : { width: 120 }),
        ...item,
        ...(item.children && Array.isArray(item.children)
          ? { children: handleColumns(item.children) }
          : {}),
      };
    });
  }
  const vm = getCurrentInstance();
  function changeRef(exposed) {
    vm && (vm.exposed = exposed);
  }
  defineExpose({} as ComponentInstance<typeof NDataTable>);
</script>
<style lang="less">
  .n-data-table .n-data-table-td,
  .n-data-table .n-data-table-th .n-data-table-th__title-wrapper .n-data-table-th__title {
    min-width: 200px;
  }
</style>
