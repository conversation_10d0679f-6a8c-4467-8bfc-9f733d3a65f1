<template>
  <div class="table-actions mb-10px">
    <n-button v-if="!isHighSeas" type="primary" @click="openAddModal">新增签约</n-button>
  </div>
  <n-data-table
    :columns="columns"
    :data="data"
    :render-cell="renderCell"
    :pagination="pagination"
    :scroll-x="0"
  />
  <BaseModal
    v-model:show="showModal"
    :title="signModelTitle"
    :width="600"
    :on-confirm="handleConfirm"
  >
    <n-form
      ref="formRef"
      :model="model"
      :rules="rules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="contractTypeId" label="签约类型">
        <n-select
          v-model:value="model.contractTypeId"
          :options="allSignType"
          label-field="contractType"
          value-field="id"
          :disabled="signModalType === 'edit'"
        />
      </n-form-item>
      <n-form-item path="contractTemplateId" label="签约模板">
        <n-select v-model:value="model.contractTemplateId" :options="signTemplateOptions" />
      </n-form-item>
      <n-form-item path="name" label="姓名">
        <n-input v-model:value="model.name" placeholder="请输入" :maxlength="30" show-count
      /></n-form-item>
      <n-form-item path="gender" label="性别">
        <n-select v-model:value="model.gender" :options="sexOptions" />
      </n-form-item>
      <n-form-item path="idCard" label="身份证">
        <n-input v-model:value="model.idCard" placeholder="请输入" :maxlength="18" show-count />
      </n-form-item>
      <n-form-item path="phone" label="联系电话">
        <n-input
          @input="(val) => (model.phone = val.replace(/\D/g, ''))"
          v-model:value="model.phone"
          placeholder="请输入"
          :maxlength="11"
          show-count
        />
      </n-form-item>
      <n-form-item path="address" label="联系地址">
        <CitySelect
          v-model="model.address"
          change-on-select
          :multiple="false"
          :value-field="'label'"
          :label-field="'label'"
          @update="handleCityUpdate"
          ref="citySelectRef"
        />
        <!-- <n-input v-model:value="model.address" placeholder="请输入" :maxlength="30" show-count /> -->
      </n-form-item>
      <n-form-item path="postalCode" label="邮编">
        <n-input v-model:value="model.postalCode" :disabled="postalCodeDisabled"
      /></n-form-item>
      <n-form-item path="productName" label="产品名称">
        <n-input v-model:value="model.productName" placeholder="请输入" :maxlength="30" show-count
      /></n-form-item>
      <n-form-item path="fundingEntity" label="资方主体">
        <n-input
          v-model:value="model.fundingEntity"
          placeholder="请输入"
          :maxlength="30"
          show-count
      /></n-form-item>
      <n-form-item path="contractAmount" label="签约金额">
        <n-input
          v-model:value="model.contractAmount"
          placeholder="请输入"
          @input="(val) => (model.contractAmount = val.replace(/\D/g, ''))"
        >
          <template #suffix> 元 </template>
        </n-input></n-form-item
      >
    </n-form>
  </BaseModal>
  <BaseModal
    v-model:show="showQrcodeModel"
    :title="qrCodeModelType === 'contractUrl' ? '签约二维码' : '支付地址二维码'"
    :width="400"
    positive-text="复制二维码"
    :on-confirm="copyQrcode"
  >
    <div class="form-flex-center--box">
      <n-qr-code id="qrcode" :value="signQrCode" />
      <p class="mt-20px"
        >({{ `通过${qrCodeModelType === 'contractUrl' ? '签约' : '支付'}地址生成，可扫码访问` }})</p
      >
    </div>
  </BaseModal>
  <BaseModal
    v-model:show="showPaymentModal"
    title="新建支付"
    :width="400"
    :on-confirm="handlePaymentConfirm"
  >
    <n-form
      ref="paymentFormRef"
      :model="paymentModel"
      :rules="paymentRules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="payTemplateId" label="支付模板">
        <n-select
          v-model:value="paymentModel.payTemplateId"
          :options="paymentOptions"
          :render-label="renderOption"
          :show-arrow="!paymentModel.payTemplateId"
          :show-checkmark="false"
        />
      </n-form-item>
      <n-form-item path="qrCode" label="支付二维码">
        <n-select v-model:value="paymentModel.qrCode" :options="qrCodeImgs" />
      </n-form-item>
      <!-- <n-form-item path="qrCode" label="支付二维码">
        <div class="form-flex-center--box">
          <UploadFile
            v-model:fileList="paymentModel.qrCode"
            accept=".jpg,.jpeg,.png"
            :max-size="2"
          />
          <p class="mt-10px">（支持png、jpg格式，需小于2M）</p>
        </div>
      </n-form-item> -->
    </n-form>
  </BaseModal>
  <BaseModal
    v-model:show="showPaymentEditModal"
    title="编辑支付状态"
    :width="400"
    :on-confirm="handlePaymentStatusConfirm"
  >
    <n-form
      ref="paymentStatusFormRef"
      :model="paymentStatusModel"
      :rules="paymentStatusRules"
      label-placement="left"
      :label-width="100"
      validate-trigger="blur"
    >
      <n-form-item path="paymentStatus" label="支付状态">
        <n-select
          v-model:value="paymentStatusModel.paymentStatus"
          :options="contractPaymentStatusOptions"
        />
      </n-form-item>
    </n-form>
  </BaseModal>
  <BaseModal v-model:show="showPreviewPaymentUrl" :title="previewPaymentName">
    <div style="max-height: 500px; overflow-y: auto">
      <iframe
        :src="previewPaymentUrl"
        frameborder="0"
        width="375px"
        height="667px"
        style="margin: 0 auto"
      ></iframe>
    </div>
  </BaseModal>
</template>

<script lang="tsx" setup>
  import { h, ref, reactive, onMounted, computed } from 'vue';
  import {
    NDataTable,
    NButton,
    NForm,
    NFormItem,
    NSelect,
    NTime,
    NSpace,
    NTooltip,
    type FormInst,
    type FormRules,
    type SelectOption,
  } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import CitySelect from '@/components/CitySelect/index.vue';
  import type { contractRecordRepose, CustomerDetail } from '@/api/detail';
  import {
    contractStatusMap,
    sexOptions,
    contractStatusValue,
    contractPaymentStatusMap,
    contractPaymentStatusOptions,
    contractPaymentStatus,
  } from '@/enums/detailEnum';
  import {
    addRecordContract,
    updateRecordContract,
    getContractRecord,
    getContractRecordTypes,
    addRecordContractPay,
    getPaymentTemplate,
    recordContractPayStatus,
    getSignTemplate,
    getCityPostalCode,
    getESignProtocol,
    renewRecordContract,
    getPayQrCodeImg,
    type addRecordPayParams,
  } from '@/api/detail';
  import type { addContractRecordParams } from '@/api/detail';
  // import { BasicColumn } from '@/components/Table';
  import type { TableBaseColumn } from 'naive-ui/lib/data-table/src/interface';
  import { validIdCard, validPhone } from '@/utils/formValidationRules';
  import { copyCanvasToClipboard } from '@/utils/copyCanvasToClipboard';
  import { useRoute } from 'vue-router';
  import { downloadContractFilesAsZip } from '@/utils/zipDownload';
  const route = useRoute();
  const isHighSeas = computed(() => route.name === 'HighSeas');
  const signModelTitle = computed(() => {
    return signModalType.value === 'renew'
      ? '重新签约'
      : signModalType.value === 'add'
      ? '新增签约'
      : '编辑签约';
  });
  const mode = import.meta.env.MODE;
  const props = withDefaults(
    defineProps<{
      clueId: number;
      clueInfoVo: CustomerDetail['clueInfoVo'];
    }>(),
    {}
  );
  const data = ref<contractRecordRepose[]>([]);
  const emit = defineEmits(['update-success']);
  const formRef = ref<FormInst | null>(null);
  const paymentFormRef = ref<FormInst | null>(null);
  const paymentStatusFormRef = ref<FormInst | null>(null);
  const allSignType = ref([]);
  const signTemplateOptions = ref([]);
  const showModal = ref(false);
  const signModalType = ref('');
  const showPaymentModal = ref(false);
  const showPaymentEditModal = ref(false);
  const citySelectRef = ref();
  const qrCodeImgs = ref([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 30],
    onChange: (page: number) => {
      pagination.page = page;
      getList();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      getList();
    },
  });
  const postalCodeDisabled = ref(false);
  // 初始表单状态
  const initialSignModel: addContractRecordParams = {
    contractRecordId: null,
    contractTypeId: '',
    contractTemplateId: '',
    clueId: props.clueId,
    name: props.clueInfoVo?.name,
    gender: props.clueInfoVo?.sex,
    idCard: props.clueInfoVo?.idCardNumber || '',
    phone: props.clueInfoVo?.mobileNo,
    address: props.clueInfoVo?.cityName || '',
    postalCode: '',
    productName: '',
    fundingEntity: '',
    contractAmount: '2000',
  };

  let model = reactive<addContractRecordParams>({ ...initialSignModel });
  const rules: FormRules = {
    contractTypeId: {
      required: true,
      message: '请选择签约类型',
      trigger: 'change',
      type: 'number',
    },
    name: { required: true, message: '请输入姓名', trigger: 'blur' },
    gender: { required: true, message: '请选择性别', trigger: 'change', type: 'number' },
    contractTemplateId: {
      required: true,
      message: '请选择签约模板',
      trigger: 'change',
      type: 'number',
    },
    idCard: [{ required: true, validator: validIdCard, trigger: 'blur' }],
    phone: [{ required: true, validator: validPhone, trigger: 'blur' }],
    address: [
      { required: true, message: '请选择联系地址', trigger: 'change' },
      {
        validator: (_rule, value: any) => {
          if (value) {
            const options = citySelectRef.value?.options ?? [];
            const parentIndex = options?.findIndex((item) => item.label === value);
            if (parentIndex !== -1 && options?.[parentIndex]?.children.length > 1) {
              return new Error('请选择市');
            }
          }
        },
        trigger: 'change',
      },
    ],
    postalCode: { required: true, message: '请输入邮编', trigger: 'blur' },
    productName: { required: true, message: '请输入产品名称', trigger: 'blur' },
    fundingEntity: { required: true, message: '请输入资方主体', trigger: 'blur' },
    contractAmount: {
      required: true,
      message: '请输入签约金额',
      trigger: 'blur',
      type: 'any',
    },
  };
  const paymentRules: FormRules = {
    payTemplateId: { required: true, message: '请选择支付模板', trigger: 'change', type: 'number' },
    qrCode: { required: true, message: '请选择支付二维码', trigger: 'change' },
  };
  const paymentStatusRules: FormRules = {
    paymentStatus: { required: true, message: '请选择支付状态', trigger: 'change', type: 'number' },
  };
  const signQrCode = ref(''); //签约二维码
  const qrCodeModelType = ref(''); //二维码地址弹窗类型
  const showQrcodeModel = ref(false);
  const initPaymentForm = {
    contractRecordId: '',
    payTemplateId: '',
    qrCode: '',
  };
  const initPaymentStatusForm = {
    contractRecordId: '',
    paymentStatus: '',
  };
  const paymentModel = ref<addRecordPayParams>(initPaymentForm);
  const paymentStatusModel = ref(initPaymentStatusForm);
  const paymentOptions = ref([]);
  const previewPaymentUrl = ref('');
  const previewPaymentName = ref('');
  const showPreviewPaymentUrl = ref(false);
  onMounted(async () => {
    getList();
    getSignType();
    getTemplateList();
    getSignTemplateList();
    getCodeImgs();
  });
  function renderOption(option: SelectOption) {
    return [
      h(
        'div',
        {
          style: {
            width: '100%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            paddingRight: '40px',
          },
        },
        { default: () => option.label as string }
      ),
      option.value &&
        h(
          NButton,
          {
            size: 'small',
            style: {
              position: 'absolute',
              right: '5px',
              top: '50%',
              transform: 'translate(0,-50%)',
            },
            onClick: (e) => {
              e.stopPropagation();
              previewContract(option.value);
            },
          },
          { default: () => '预览' }
        ),
    ];
  }
  function previewContract(templateId) {
    let curOptions = paymentOptions.value.find((item: any) => item.value === templateId);
    const { domain = '', route = '', name = '' } = curOptions || {};
    const url = domain + route;
    previewPaymentName.value = name;
    previewPaymentUrl.value = url;
    showPreviewPaymentUrl.value = true;
  }
  function getCodeImgs() {
    getPayQrCodeImg().then((res) => {
      let listJSON = res.data?.['PAY_QRCODE_PAGE_URL'] ?? '[]';
      qrCodeImgs.value =
        JSON.parse(listJSON)?.map((item) => ({
          label: item?.qrCodeName ?? '',
          value: item?.qrCodeUrl ?? '',
        })) || [];
    });
  }
  function getList() {
    return getContractRecord({
      clueId: props.clueId,
      pageNumber: pagination.page,
      pageSize: pagination.pageSize,
    }).then((res) => {
      data.value = res.data?.records ?? [];
      return data.value;
    });
  }
  function getSignType() {
    getContractRecordTypes().then((res) => {
      if (res?.data) {
        allSignType.value = res.data;
      }
    });
  }
  async function getTemplateList() {
    let { data } = await getPaymentTemplate();
    paymentOptions.value = data?.map((item) => ({
      ...item,
      label: item.name,
      value: item.id,
    }));
    return data;
  }
  async function getSignTemplateList() {
    let { data } = await getSignTemplate();
    signTemplateOptions.value = data?.map((item) => ({
      id: item.id,
      label: item.originTemplateFileName,
      value: item.id,
      ...item,
    }));
    return data;
  }
  async function openAddModal() {
    // 重置表单
    Object.assign(model, JSON.parse(JSON.stringify(initialSignModel)));
    model.postalCode = await getPostalCode(model.address);
    postalCodeDisabled.value = !!model.postalCode;
    signModalType.value = 'add';
    showModal.value = true;
  }
  async function openSignModel(row: contractRecordRepose) {
    let { extension } = row;
    let { id, createTime, updateTime, contractAmount, ...rest } = extension || {};

    Object.assign(model, JSON.parse(JSON.stringify(initialSignModel)), {
      ...rest,
      contractTypeId: row.contractTypeId,
      contractAmount:
        contractAmount !== null && contractAmount !== undefined ? contractAmount.toString() : '',
    });
    !model.postalCode && (model.postalCode = await getPostalCode(model.address));
    postalCodeDisabled.value = !!model.postalCode;
    signModalType.value =
      row.contractStatus === contractStatusValue.REJECTED_STATUS ? 'renew' : 'edit';
    showModal.value = true;
  }
  async function handleConfirm() {
    try {
      await formRef.value?.validate();
      let api = model.contractRecordId
        ? signModalType.value === 'renew'
          ? renewRecordContract
          : updateRecordContract
        : addRecordContract;
      let address = getProvinceAndCity();
      await api({ ...model, address });
      window.$message.success(`${signModelTitle.value}成功`);
      emit('update-success');
      getList();
      showModal.value = false;
    } catch (errors) {
      window.$message.error('请检查表单输入');
      return false;
    }
  }
  function openQrCodeModel(modelOptions) {
    let { row, key } = modelOptions;
    row[key] && (signQrCode.value = row[key]);
    qrCodeModelType.value = key;
    showQrcodeModel.value = true;
  }
  //复制签约二维码
  async function copyQrcode() {
    const canvas = document.getElementById('qrcode')?.querySelector('canvas');
    canvas && (await copyCanvasToClipboard(canvas));
    window.$message.success('复制成功');
  }
  // 使用ZIP压缩下载工具
  async function downloadFilesAsZip(urls: string[], contractId?: string | number) {
    try {
      const result = await downloadContractFilesAsZip(urls, 'GPS中介合同_签约id_' + contractId);
      console.log('ZIP下载结果:', result);
      return result;
    } catch (error) {
      console.error('ZIP下载失败:', error);
      window.$message.error('文件压缩下载失败');
    }
  }
  async function handleDownload(row) {
    //下载
    const { data: protocolList } = await getESignProtocol(row.id);
    if (!protocolList || protocolList.length === 0) {
      window.$message.error('暂无存证下载');
      return;
    } else if (Array.isArray(protocolList)) {
      // 使用ZIP压缩下载
      await downloadFilesAsZip(protocolList, row.id);
    }
  }
  function openPaymentModel(row) {
    showPaymentModal.value = true;
    let { paymentTemplate, paymentQrCode } = row;
    const payTemplateId =
      paymentTemplate !== null && paymentTemplate !== undefined
        ? Number(row.paymentTemplate)
        : null;
    Object.assign(paymentModel.value, JSON.parse(JSON.stringify(initPaymentForm)), {
      contractRecordId: row.id,
      payTemplateId,
      qrCode: paymentQrCode,
    });
  }
  function openPaymentStatusModel(row) {
    showPaymentEditModal.value = true;
    Object.assign(paymentStatusModel.value, JSON.parse(JSON.stringify(initPaymentStatusForm)), {
      contractRecordId: row.id,
    });
  }
  async function handlePaymentConfirm() {
    try {
      await paymentFormRef.value?.validate();
      await addRecordContractPay({
        contractRecordId: paymentModel.value.contractRecordId,
        payTemplateId: paymentModel.value.payTemplateId,
        qrCode: paymentModel.value.qrCode,
      });
      window.$message.success('新建支付地址成功');
      getList();
      emit('update-success');
      showPaymentModal.value = false;
    } catch (errors) {
      window.$message.error('请检查表单输入');
      return false;
    }
  }
  async function handlePaymentStatusConfirm() {
    try {
      await paymentStatusFormRef.value?.validate();
      await recordContractPayStatus({
        contractRecordId: paymentStatusModel.value.contractRecordId,
        paymentStatus: paymentStatusModel.value.paymentStatus,
      });
      window.$message.success('编辑支付状态成功');
      getList();
      emit('update-success');
      showPaymentEditModal.value = false;
    } catch (errors) {
      window.$message.error('请检查表单输入');
      return false;
    }
  }
  const columns: TableBaseColumn<contractRecordRepose>[] = [
    {
      title: '签约记录ID',
      key: 'id',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      align: 'center',
    },
    {
      title: '签约类型',
      key: 'contractType',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      align: 'center',
    },
    // { title: '签约手机号', key: 'contractMobile', width: 120, align: 'center' },
    {
      title: '签约地址',
      key: 'contractUrl',
      align: 'center',
      width: 240,
      render(row) {
        return row.contractUrl ? (
          <NSpace align="center" justify="center">
            {mode === 'production' && (
              <NTooltip placement="top" trigger="hover">
                {{
                  trigger: () => (
                    <NButton type="primary" text v-copy={row.contractUrl} class="show-one-text">
                      {row.contractUrl}
                    </NButton>
                  ),
                  default: () => row.contractUrl,
                }}
              </NTooltip>
            )}

            <NButton
              type="primary"
              text
              style={{ 'text-align': 'center' }}
              onClick={() => {
                openQrCodeModel({ row, key: 'contractUrl' });
              }}
            >
              生成二维码
            </NButton>
          </NSpace>
        ) : (
          '--'
        );
      },
    },
    {
      title: '支付地址',
      key: 'paymentUrl',
      align: 'center',
      width: 240,
      render(row) {
        return row.paymentUrl ? (
          <NSpace align="center" justify="center">
            {mode === 'production' && (
              <NTooltip placement="top" trigger="hover">
                {{
                  trigger: () => (
                    <NButton type="primary" text v-copy={row.paymentUrl} class="show-one-text">
                      {row.paymentUrl}
                    </NButton>
                  ),
                  default: () => row.paymentUrl,
                }}
              </NTooltip>
            )}

            <NButton
              type="primary"
              text
              style={{ 'text-align': 'center' }}
              onClick={() => {
                openQrCodeModel({ row, key: 'paymentUrl' });
              }}
            >
              生成二维码
            </NButton>
          </NSpace>
        ) : (
          '--'
        );
      },
    },
    {
      title: '签约状态',
      key: 'contractStatus',
      width: 100,
      align: 'center',
      render(row) {
        return h('p', {}, contractStatusMap?.[row.contractStatus] ?? '--');
      },
    },
    {
      title: '支付状态',
      key: 'paymentStatus',
      width: 100,
      align: 'center',
      render(row) {
        return h('p', {}, contractPaymentStatusMap?.[row.paymentStatus] ?? '--');
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 120,
      align: 'center',
      render(row) {
        return row.createTime ? h(NTime, { time: new Date(row.createTime) }) : '--';
      },
    },
    {
      title: '存证时间',
      key: 'evidenceTime',
      width: 120,
      align: 'center',
      render(row) {
        return row.evidenceTime ? h(NTime, { time: new Date(row.evidenceTime) }) : '--';
      },
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 160,
      align: 'center',
      render(row) {
        return (
          <NSpace justify="center" align="center">
            {row.contractStatus === contractStatusValue.WAIT_STATUS && (
              <NButton
                type="primary"
                text
                onClick={() => {
                  openSignModel(row);
                }}
              >
                编辑
              </NButton>
            )}
            {row.contractStatus === contractStatusValue.REJECTED_STATUS && (
              <NButton
                type="primary"
                text
                onClick={() => {
                  openSignModel(row);
                }}
              >
                重新签约
              </NButton>
            )}
            {row.paymentStatus === contractPaymentStatus.WAIT_STATUS && (
              <NButton
                type="primary"
                text
                onClick={() => {
                  openPaymentStatusModel(row);
                }}
                v-permission={{ action: 'detail_payment_status' }}
              >
                编辑
              </NButton>
            )}
            {row.contractStatus === contractStatusValue.SIGNED_STATUS && (
              <NButton
                type="primary"
                text
                onClick={() => {
                  handleDownload(row);
                }}
              >
                下载
              </NButton>
            )}
            {row.contractStatus === contractStatusValue.SIGNED_STATUS && (
              <NButton
                type="primary"
                text
                onClick={() => {
                  openPaymentModel(row);
                }}
              >
                支付
              </NButton>
            )}
          </NSpace>
        );
      },
    },
  ];

  const renderCell = (value: any) => {
    if (!value) return '--';
    return value;
  };
  function getProvinceAndCity() {
    const city = model.address;
    const province = citySelectRef.value?.options?.find((item) =>
      item.children.find((cityItem) => cityItem.label === city && cityItem.value !== item.value)
    )?.label;
    return (province || '') + model.address;
  }

  async function handleCityUpdate() {
    if (model.address) {
      model.postalCode = await getPostalCode(model.address);
      postalCodeDisabled.value = !!model.postalCode;
    }
  }
  async function getPostalCode(city: string) {
    return getCityPostalCode({ city }).then((res) => {
      return res?.data || '';
    });
  }
</script>

<style lang="less" scoped>
  .w-full {
    width: 100%;
  }
  .table-actions {
    margin: 0 0 10px;
  }
  .form-flex-center--box {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .mt-10px {
    margin-top: 10px;
  }
  .mt-20px {
    margin-top: 20px;
  }
</style>
<style lang="less">
  .show-one-text {
    width: 100% !important;
    > span {
      display: inline-block !important;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .render-option {
    width: 90%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    height: 34px;
    padding: 0 3px;
    &:hover {
      background-color: #ccc;
    }
  }
</style>
