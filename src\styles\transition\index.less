@import './base.less';
@import './fade.less';
@import './scale.less';
@import './slide.less';
@import './scroll.less';
@import './zoom.less';

.collapse-transition {
  transition: 0.2s height ease-in-out, 0.2s padding-top ease-in-out, 0.2s padding-bottom ease-in-out;
}
.form-title{
  font-size: 16px;
  position: relative;
  padding-left: 10px;
  &::before {
    content: "";
    height: 80%;
    width: 5px;
    background-color: var(--n-color);
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
