/**
 * 版本更新弹窗工具
 * 使用 Naive UI 创建更友好的版本更新提示
 */

import { h } from 'vue';

/**
 * 显示版本更新对话框
 */
export function showVersionUpdateDialog(
  newVersion: string,
  currentVersion: string
): Promise<string> {
  // 格式化时间戳为可读日期
  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(parseInt(timestamp));
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch {
      return timestamp;
    }
  };

  const currentVersionFormatted = formatTimestamp(currentVersion);
  const newVersionFormatted = formatTimestamp(newVersion);
  return new Promise((resolve) => {
    window.$dialog.info({
      title: '🎉 发现新版本',
      content: () =>
        h('div', { style: 'line-height: 1.6;' }, [
          h('p', { style: 'margin-bottom: 12px;' }, '检测到应用有新版本可用！'),
          h(
            'div',
            { style: 'background: #f6f8fa; padding: 12px; border-radius: 6px; margin: 12px 0;' },
            [
              h('div', { style: 'margin-bottom: 8px;' }, [
                h('strong', {}, '当前版本：'),
                h('br'),
                h(
                  'span',
                  { style: 'font-family: monospace; color: #666;' },
                  currentVersionFormatted
                ),
              ]),
              h('div', {}, [
                h('strong', {}, '最新版本：'),
                h('br'),
                h(
                  'span',
                  { style: 'font-family: monospace; color: #18a058;' },
                  newVersionFormatted
                ),
              ]),
            ]
          ),
          h('p', { style: 'color: #666; font-size: 14px;' }, '💡 建议立即更新以获得最新功能和修复'),
        ]),
      positiveText: '立即更新',
      negativeText: '稍后提醒',
      closable: false,
      closeOnEsc: false,
      maskClosable: false,
      onPositiveClick: () => {
        // 显示加载提示
        const loadingDialog = window.$dialog.info({
          title: '正在更新...',
          content: '页面即将刷新，请稍候...',
          closable: false,
          closeOnEsc: false,
          maskClosable: false,
        });

        // 延迟一下再刷新，让用户看到提示
        setTimeout(() => {
          loadingDialog.destroy();
          window.location.reload();
        }, 1000);
        resolve('reload');
      },
      onNegativeClick: () => {
        // 用户选择稍后提醒，可以在这里设置一个较短的检查间隔
        console.log('用户选择稍后更新');
        resolve('later');
      },
    });
  });
}

/**
 * 显示简单的版本更新确认框（备用方案）
 */
export function showSimpleVersionUpdateDialog(
  newVersion: string,
  currentVersion: string
): Promise<string> {
  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(parseInt(timestamp));
      return date.toLocaleString('zh-CN');
    } catch {
      return timestamp;
    }
  };

  const message = `检测到新版本！

当前版本: ${formatTimestamp(currentVersion)}
最新版本: ${formatTimestamp(newVersion)}

是否立即刷新页面更新？`;
  return new Promise((resolve) => {
    if (confirm(message)) {
      window.location.reload();
      resolve('reload');
    }
  });
}
