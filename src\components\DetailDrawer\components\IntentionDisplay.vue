<template>
  <div class="intention-container">
    <div class="intention-info">
      <div
        class="intention-level"
        :style="{ backgroundColor: intentionColor }"
        @mouseenter="showEditIcon = true"
        @mouseleave="showEditIcon = false"
        @click="openModal"
      >
        <template v-if="!showEditIcon">
          <template v-if="intentionShortText">
            <div class="level-text">
              {{ intentionShortText }}
            </div>
            意向度
          </template>
          <template v-else>
            <div class="level-text"> --- </div>
          </template>
        </template>
        <div v-else class="edit-overlay">
          <n-icon :size="24"><Pencil /></n-icon>
          <div>修改</div>
        </div>
      </div>
    </div>
    <div class="intention-tags">
      <n-dynamic-tags
        v-model:value="safeTags"
        :max="5"
        type="primary"
        @update:value="handleTagsUpdate"
      />
    </div>
  </div>

  <BaseModal
    v-model:show="showModal"
    title="修改意向度"
    :width="600"
    positive-text="保存"
    :on-confirm="handlePositiveClick"
  >
    <n-form ref="formRef" :model="model" :rules="rules" label-placement="left" :label-width="100">
      <n-form-item path="intention" label="意向度">
        <RadioButtonPicker
          v-model="model.intention"
          :options="intentionOptions"
          name="intention-radio-group"
        />
      </n-form-item>
    </n-form>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { ref, computed, defineModel, defineProps } from 'vue';
  import type { FormInst, FormRules } from 'naive-ui';
  import { NIcon, NForm, NFormItem, NDynamicTags } from 'naive-ui';
  import { Pencil } from '@vicons/ionicons5';
  import { BaseModal } from '@/components/Modal';
  import { intentionEnum, intentionOptions } from '@/enums/detailEnum';
  import RadioButtonPicker from '@/components/Form/src/components/RadioButtonPicker.vue';
  import { updateClueTags, updateClueIntention } from '@/api/detail';

  const props = defineProps<{
    clueId: number;
  }>();

  type IntentionType = intentionEnum | null;
  const intention = defineModel<IntentionType>('intention', { required: true });
  const tags = defineModel<string[] | null>('tags', { required: true });

  const safeTags = computed(() => tags.value || ['示例标签']);

  const emit = defineEmits(['update-success']);

  const formRef = ref<FormInst | null>(null);
  const model = ref<{ intention: IntentionType }>({
    intention: null,
  });

  const rules: FormRules = {
    intention: {
      required: true,
      type: 'number',
      message: '请选择一个意向度',
      trigger: ['blur', 'change'],
    },
  };

  const showEditIcon = ref(false);
  const showModal = ref(false);

  function openModal() {
    model.value.intention = intention.value ?? null;
    showModal.value = true;
  }

  async function handlePositiveClick() {
    try {
      await formRef.value?.validate();
      await updateClueIntention(props.clueId, model.value.intention as intentionEnum);
      model.value.intention = null;
      emit('update-success');
    } catch (error) {
      window.$message.error('意向度更新失败');
      return false;
    }
  }

  // 处理标签更新
  async function handleTagsUpdate(newTags: string[]) {
    try {
      if (newTags.includes('示例标签')) {
        newTags.splice(newTags.indexOf('示例标签'), 1);
      }
      await updateClueTags(props.clueId, newTags.join(','));
      tags.value = newTags;
      emit('update-success');
    } catch (error) {
      window.$message.error('标签更新失败');
    }
  }

  // 根据意向度等级，显示简称（高、中、低、无）
  const intentionShortText = computed(() => {
    return intentionOptions.find((opt) => opt.value === intention.value)?.shortLabel || '';
  });

  // 根据意向度等级，返回不同的背景颜色
  const intentionColor = computed(() => {
    switch (intention.value) {
      case intentionEnum.HIGH:
        return '#dff0d8'; // 浅绿色
      case intentionEnum.MEDIUM:
        return '#fcf8e3'; // 浅黄色
      case intentionEnum.LOW:
        return '#f2dede'; // 浅红色
      case intentionEnum.NONE:
      default:
        return '#f0f0f0'; // 灰色
    }
  });
</script>

<style lang="less" scoped>
  .intention-container {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .intention-info {
    margin-right: 16px;
    .intention-level {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #666;
      border: 4px solid #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      position: relative;
      cursor: pointer;
      transition: background-color 0.3s ease;

      .level-text {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }

      .edit-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.4);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1;
      }
    }
  }

  .intention-tags {
    flex: 1;
  }

  .ml-1 {
    margin-left: 4px;
    vertical-align: middle;
  }
</style>
