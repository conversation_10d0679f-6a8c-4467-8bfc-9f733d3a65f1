<template>
  <n-select
    v-bind="$attrs"
    v-model:value="model"
    :options="options"
    :render-label="renderOption"
    :show-checkmark="false"
    ref="selectRef"
  />
</template>
<script setup lang="ts">
  import { h, ref, reactive, onMounted, computed } from 'vue';
  import { NSelect, type SelectOption } from 'naive-ui';
  const model = defineModel<string | number | undefined>('value', { required: true });
  const props = defineProps({
    options: {
      type: Array as PropType<SelectOption[]>,
      default: () => [],
    },
  });
  function renderOption(option: SelectOption) {
    return h(
      'div',
      {
        class: 'combine-select-option',
        onClick: () => {
          model.value = option.value;
        },
      },
      {
        default: () => `${option.label}${option.finish ? '✔️' : ''}`,
      }
    );
  }
  const selectRef = ref<InstanceType<typeof NSelect> | null>(null);
  defineExpose({
    selectRef,
  });
</script>
<style lang="less" scoped></style>
