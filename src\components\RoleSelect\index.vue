<template>
  <NSelect
    v-bind="$attrs"
    :value="modelValue"
    :options="options"
    :placeholder="placeholder"
    :multiple="multiple"
    :filterable="filterable"
    :clearable="clearable"
    @update:value="$emit('update:modelValue', $event)"
  />
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import { NSelect } from 'naive-ui';
  import { useRoleOptions } from '@/components/RoleSelect/useRoleOptions';

  interface Props {
    modelValue?: number | number[];
    placeholder?: string;
    multiple?: boolean;
    filterable?: boolean;
    clearable?: boolean;
  }

  withDefaults(defineProps<Props>(), {
    placeholder: '请选择角色',
    multiple: false,
    filterable: true,
    clearable: true,
  });

  defineEmits<{
    'update:modelValue': [value: number | number[] | undefined];
  }>();

  const { roleOptions, loadOptions } = useRoleOptions();

  const options = computed(() =>
    roleOptions.value.map((item) => ({
      label: item.name,
      value: item.id,
    }))
  );

  onMounted(() => {
    loadOptions();
  });
</script>
