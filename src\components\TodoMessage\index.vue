<template>
  <div>
    <n-badge :value="count" :max="99" @click="onGoTo">
      <n-tooltip placement="bottom">
        <template #trigger>
          <n-icon size="18">
            <BellOutlined />
          </n-icon>
        </template>
        <span>消息提醒</span>
      </n-tooltip>
    </n-badge>
  </div>
</template>

<script setup lang="ts">
  import { BellOutlined } from '@vicons/antd';
  import { useRouter } from 'vue-router';
  import { useTodoMessage } from '@/components/TodoMessage/composables/useTodoMessage';

  const router = useRouter();
  const { count } = useTodoMessage();

  const onGoTo = () => {
    router.push({
      path: '/message',
    });
  };
</script>

<style lang="less" scoped></style>
