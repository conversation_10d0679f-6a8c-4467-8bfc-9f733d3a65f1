# 表单缓存功能

表单组件现在支持本地缓存功能，可以自动保存和恢复表单数据，特别适用于需要在同一路由多次使用表单组件的场景。

## 功能特性

- ✅ 自动保存表单数据到本地存储
- ✅ 支持自动恢复缓存数据
- ✅ 支持自定义缓存key，解决同一路由多个表单的问题
- ✅ 支持设置缓存过期时间
- ✅ 只缓存有值的字段，避免存储空数据
- ✅ 自动缓存表单的展开收起状态
- ✅ 提供手动操作缓存的API

## 基本用法

### 启用缓存

```vue
<template>
  <BasicForm
    :schemas="schemas"
    :enable-cache="true"
    @submit="handleSubmit"
  />
</template>
```

### 自定义缓存配置

```vue
<template>
  <BasicForm
    :schemas="schemas"
    :enable-cache="true"
    :cache-key="'user-form'"
    :cache-timeout="60 * 60 * 2"
    :auto-restore-cache="false"
    @submit="handleSubmit"
  />
</template>
```

> **💡 提示**：如果不设置 `cacheTimeout`，缓存将默认保存到当天24点，这样每天都是一个新的开始，更符合日常使用习惯。

## 配置选项

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enableCache` | `boolean` | `false` | 是否启用缓存功能 |
| `cacheKey` | `string` | `''` | 自定义缓存key，不设置则使用路由路径 |
| `cacheTimeout` | `number` | `undefined` | 缓存过期时间（秒），不设置则缓存到当天24点 |
| `autoRestoreCache` | `boolean` | `true` | 是否自动恢复缓存数据 |

## API 方法

通过表单实例可以调用以下缓存相关方法：

```typescript
// 获取表单实例
const [register, formMethods] = useForm();

// 手动保存缓存
formMethods.saveFormCache();

// 手动恢复缓存
const success = formMethods.restoreFormCache();

// 清除缓存
formMethods.clearFormCache();

// 检查是否有缓存数据
const hasCache = formMethods.hasCacheData();
```

## 使用场景

### 1. 同一路由多个表单

当同一个路由页面有多个表单时，需要设置不同的 `cacheKey` 来区分：

```vue
<template>
  <div>
    <!-- 用户信息表单 -->
    <BasicForm
      :schemas="userSchemas"
      :enable-cache="true"
      cache-key="user-info-form"
    />
    
    <!-- 地址信息表单 -->
    <BasicForm
      :schemas="addressSchemas"
      :enable-cache="true"
      cache-key="address-info-form"
    />
  </div>
</template>
```

### 2. 搜索表单

对于搜索表单，通常希望保留用户的搜索条件：

```vue
<template>
  <BasicForm
    :schemas="searchSchemas"
    :enable-cache="true"
    cache-key="product-search"
    :cache-timeout="60 * 60 * 24 * 7"
    @submit="handleSearch"
  />
</template>
```

### 3. 长表单填写

对于复杂的长表单，可以避免用户意外刷新页面导致数据丢失：

```vue
<template>
  <BasicForm
    :schemas="longFormSchemas"
    :enable-cache="true"
    cache-key="long-form"
    :cache-timeout="60 * 60 * 24 * 3"
  />
</template>
```

## 注意事项

1. **缓存内容**：只有非空、非null、非空字符串的字段值会被缓存，同时缓存表单的展开收起状态
2. **缓存时机**：表单数据变化或展开收起状态变化时会自动保存到缓存
3. **恢复时机**：启用 `autoRestoreCache` 时，表单初始化完成后会自动恢复缓存
4. **缓存存储**：使用项目的 Storage 工具类，支持过期时间管理
5. **性能考虑**：缓存操作使用防抖机制，避免频繁写入

## 完整示例

```vue
<template>
  <div>
    <n-space>
      <n-button @click="handleRestore" :disabled="!hasCache">
        恢复缓存
      </n-button>
      <n-button @click="handleClear">
        清除缓存
      </n-button>
    </n-space>
    
    <BasicForm
      ref="formRef"
      :schemas="schemas"
      :enable-cache="true"
      cache-key="example-form"
      :cache-timeout="60 * 60 * 24"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { BasicForm } from '@/components/Form';

const formRef = ref();

const hasCache = computed(() => {
  return formRef.value?.hasCacheData?.() || false;
});

function handleRestore() {
  const success = formRef.value?.restoreFormCache?.();
  if (success) {
    window.$message?.success('缓存恢复成功');
  }
}

function handleClear() {
  formRef.value?.clearFormCache?.();
  window.$message?.success('缓存已清除');
}

function handleSubmit(values: any) {
  console.log('提交数据:', values);
}
</script>
```
