<template>
  <n-drawer v-model:show="visible" :width="960" placement="right" :mask-closable="false">
    <n-drawer-content :title="drawerTitle" closable>
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="top"
        label-width="auto"
      >
        <!-- 基础信息 -->
        <n-card title="基础信息" class="mb-4">
          <n-grid :cols="4" :x-gap="16" :y-gap="16">
            <n-gi :span="2">
              <n-form-item label="规则名称" path="ruleName">
                <n-input
                  v-model:value="formData.ruleName"
                  placeholder="请输入规则名称"
                  maxlength="30"
                  show-count
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="4">
              <n-form-item label="规则备注" path="remark">
                <n-input
                  v-model:value="formData.remark"
                  type="textarea"
                  placeholder="请输入规则备注"
                  maxlength="100"
                  show-count
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>

        <!-- 分配规则 -->
        <n-card title="分配规则" class="mb-4">
          <n-grid :cols="isDefaultMode ? 1 : 2" :x-gap="16">
            <n-gi>
              <n-form-item label="分配角色" path="roleIds">
                <RoleSelect
                  v-model:value="roleIdsValue"
                  :multiple="isDefaultMode"
                  placeholder="请选择分配角色"
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="!isDefaultMode">
              <n-form-item label="分配媒体" path="assignMedia">
                <CustomCascader
                  v-model:value="formData.assignMedia"
                  :options="mediaTreeOptions"
                  placeholder="请选择分配媒体"
                  multiple
                  @update:value="handleMediaChange"
                  @blocked-parent-click="() => message.warning('请勿同时配置不同层级媒体')"
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>

        <!-- 分配规则列表 (非默认模式) -->
        <n-card
          v-if="!isDefaultMode && formData.rules.length > 0"
          title="分配规则列表"
          class="mb-4"
        >
          <n-data-table
            :columns="assignRuleColumns"
            :data="formData.rules"
            :pagination="false"
            size="small"
          />

          <!-- 比例校验提示 -->
          <!-- <n-alert
            v-if="totalRatio !== 100 && formData.assignRules.length > 0"
            type="warning"
            class="mt-3"
          >
            当前总比例为 {{ totalRatio }}%，请确保总比例为 100%
          </n-alert> -->
        </n-card>

        <n-card title="状态">
          <n-grid :cols="4" :x-gap="16" :y-gap="16">
            <n-gi :span="2">
              <n-form-item label="状态" path="status">
                <n-switch
                  v-model:value="formData.status"
                  :checked-value="EnableDisable.Enable"
                  :unchecked-value="EnableDisable.Disable"
                  :disabled="isDefaultMode"
                >
                  <template #checked>启用</template>
                  <template #unchecked>禁用</template>
                </n-switch>
                <div v-if="isDefaultMode" style="font-size: 12px; color: #999; margin-left: 4px">
                  默认规则状态不可修改
                </div>
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>
      </n-form>

      <template #footer>
        <n-space justify="end">
          <n-button @click="handleCancel">取消</n-button>
          <n-button type="primary" @click="handleSave" :loading="saving"> 保存 </n-button>
        </n-space>
      </template>
    </n-drawer-content>
  </n-drawer>

  <!-- 复制比例选择器 -->
  <n-modal
    v-model:show="showCopyPopover"
    preset="card"
    title="选择复制目标"
    style="width: 400px"
    :z-index="10000"
    :mask-closable="false"
  >
    <CustomCascader
      v-model:value="copyMediaValue"
      :options="availableMediaOptions"
      placeholder="请选择分配媒体"
      multiple
      @update:value="handleCopyMediaUpdate"
      @blocked-parent-click="() => message.warning('请勿同时配置不同层级媒体')"
      :z-index="10001"
    />

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCopyCancel">取消</n-button>
        <n-button type="primary" @click="handleCopyConfirm" :disabled="copyMediaValue.length === 0">
          确认复制
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="tsx">
  import { ref, computed, reactive, nextTick } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import type { DataTableColumns, FormInst } from 'naive-ui';
  import { CopyOutlined, DeleteOutlined, QuestionCircleOutlined } from '@vicons/antd';
  import { cloneDeep } from 'lodash-es';
  import {
    getRoleListApi,
    addTrafficRuleApi,
    updateTrafficRuleApi,
    updateDefaultRuleApi,
    getSourcesApi,
  } from '@/api/system/trafficRule';
  import type { TrafficRuleForm, AssignRule, MediaTreeNode, RoleOption } from '../types';
  import CustomCascader from './CustomCascader.vue';
  import RoleSelect from '@/components/RoleSelect/index.vue';
  import { EnableDisable } from '@/views/client/enum';

  const emit = defineEmits(['success']);
  const dialog = useDialog();
  const message = useMessage();

  const visible = ref(false);
  const saving = ref(false);
  const formRef = ref<FormInst>();
  const editId = ref<number>();
  const isDefaultFlag = ref<number | null>(null);

  // 复制比例相关
  const showCopyPopover = ref(false);
  const copyMediaValue = ref<string[][]>([]);
  const currentCopyRule = ref<AssignRule>();

  // 选项数据
  const roleOptions = ref<RoleOption[]>([]);
  const mediaTreeOptions = ref<MediaTreeNode[]>([]);

  // 表单数据
  const formData = reactive<TrafficRuleForm>({
    ruleName: '',
    remark: '',
    status: 1,
    roleIds: [],
    assignMedia: [],
    rules: [],
  });

  // 是否为默认模式
  const isDefaultMode = computed(() => isDefaultFlag.value === 1);

  // 角色选择的计算属性（处理单选/多选转换）
  const roleIdsValue = computed({
    get: (): number[] | number | null => {
      if (isDefaultMode.value) {
        // 默认模式：多选，返回数组
        return formData.roleIds;
      } else {
        // 非默认模式：单选，返回第一个值或 null
        return formData.roleIds.length > 0 ? formData.roleIds[0] : null;
      }
    },
    set: (value: number[] | number | null) => {
      if (isDefaultMode.value) {
        // 默认模式：多选，直接赋值数组
        formData.roleIds = Array.isArray(value) ? value : value ? [value] : [];
      } else {
        // 非默认模式：单选，转换为数组
        formData.roleIds = value ? [value as number] : [];
      }
    },
  });

  // 抽屉标题
  const drawerTitle = computed(() => {
    if (isDefaultMode.value) {
      return '编辑默认分配规则';
    }
    return editId.value ? '编辑流量分配规则' : '新增流量分配规则';
  });

  // 总比例计算
  // const totalRatio = computed(() => {
  //   return formData.rules.reduce((sum, rule) => sum + (rule.distributeRatio || 0), 0);
  // });

  // 可用的媒体选项（排除已选择的）
  const availableMediaOptions = computed(() => {
    // 获取已选择的路径字符串数组，确保每个 path 都是数组
    const selectedPathStrings = (formData.assignMedia || [])
      .filter((path) => Array.isArray(path))
      .map((path) => path.join('/'));

    const filterOptions = (
      options: MediaTreeNode[],
      currentPath: string[] = []
    ): MediaTreeNode[] => {
      return options.map((option) => {
        const newOption = { ...option, disabled: false };
        const fullPath = [...currentPath, option.value];
        const pathString = fullPath.join('/');

        // 递归处理子级
        if (option.children && option.children.length > 0) {
          newOption.children = filterOptions(option.children, fullPath);
        }

        // 仅禁用已被选择的精确路径（不禁用父级，父级点击在更新时过滤）
        const isSelected = selectedPathStrings.includes(pathString);
        if (isSelected) newOption.disabled = true;

        return newOption;
      });
    };

    return filterOptions(mediaTreeOptions.value);
  });

  // 分配规则表格列配置
  const RATIO_OPTIONS = [
    { label: '10%', value: 10 },
    { label: '20%', value: 20 },
    { label: '30%', value: 30 },
    { label: '40%', value: 40 },
    { label: '50%', value: 50 },
    { label: '60%', value: 60 },
    { label: '70%', value: 70 },
    { label: '80%', value: 80 },
    { label: '90%', value: 90 },
    { label: '100%', value: 100 },
  ] as const;

  const assignRuleColumns: DataTableColumns<AssignRule> = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_, index) => index + 1,
    },
    {
      title: '线索来源',
      key: 'sourceId',
      width: 150,
      render: (row) =>
        getLabelByValue(String(row.sourceId || ''), mediaTreeOptions.value) || row.sourceId,
    },
    {
      title: '来源媒体',
      key: 'mediaPlatformSource',
      width: 120,
      render: (row) =>
        getLabelByValue(String(row.mediaPlatformSource || ''), mediaTreeOptions.value) ||
        row.mediaPlatformSource,
    },
    {
      title: '投放平台',
      key: 'platformId',
      width: 120,
      render: (row) =>
        getLabelByValue(String(row.platformId || ''), mediaTreeOptions.value) || row.platformId,
    },
    {
      title: () => (
        <n-space align="center" justify="center" size="small">
          <span>分配比例（%）</span>
          <div style="margin-top:4px; cursor: help;">
            <n-tooltip trigger="hover">
              {{
                trigger: () => (
                  <n-icon size="16" color="#2d8cf0">
                    <QuestionCircleOutlined />
                  </n-icon>
                ),
                default: () =>
                  '可分配的线索将按照比例分配至对应的角色中，同一媒体维度的分配比例之和最大为100%',
              }}
            </n-tooltip>
          </div>
        </n-space>
      ),
      key: 'distributeRatio',
      width: 180,
      render: (row) => (
        <n-select
          v-model:value={row.distributeRatio}
          options={RATIO_OPTIONS}
          placeholder="请选择比例"
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (row, index) => (
        <n-space>
          <n-button text type="primary" onClick={() => handleCopyRatio(row)}>
            <n-icon>
              <CopyOutlined />
            </n-icon>
            复制比例
          </n-button>
          <n-button text type="error" onClick={() => handleDeleteRule(index)}>
            <n-icon>
              <DeleteOutlined />
            </n-icon>
            删除
          </n-button>
        </n-space>
      ),
    },
  ];

  // 表单验证规则
  const rules = computed(() => ({
    ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
    roleIds: [
      {
        required: true,
        trigger: ['change', 'blur'],
        validator: (_rule: any, value: any) => {
          if (!Array.isArray(value) || value.length === 0) {
            return new Error('请选择分配角色');
          }

          const minRoles = isDefaultMode.value ? 2 : 1;
          const maxRoles = isDefaultMode.value ? Infinity : Infinity;

          if (value.length < minRoles || value.length > maxRoles) {
            return new Error(
              isDefaultMode.value ? '默认模式至少选择两个角色' : '非默认模式只能选择一个角色'
            );
          }

          return true;
        },
      },
    ],
    assignMedia: [
      {
        required: !isDefaultMode.value,
        type: 'array',
        min: 1,
        message: '请选择分配媒体',
        trigger: ['blur', 'change'],
      },
    ],
    status: [{ required: true }],
  }));

  // 根据单个值获取对应的标签（递归搜索树）
  function getLabelByValue(value: string, options: MediaTreeNode[]): string | undefined {
    for (const opt of options) {
      if (opt.value === value) return opt.label;
      if (opt.children && opt.children.length) {
        const found = getLabelByValue(value, opt.children);
        if (found) return found;
      }
    }
    return undefined;
  }

  // 处理分配媒体变化
  function handleMediaChange(values: string[][]) {
    // 过滤父级：若存在任一子级已选择，则父级不加入
    const filtered = filterParentsWhenChildrenSelected(values);
    formData.assignMedia = filtered;

    // 保留现有比例设置
    const existingRatios = new Map(
      (formData.rules || []).map((rule) => [
        `${rule.sourceId}/${rule.mediaPlatformSource || ''}/${rule.platformId || ''}`,
        rule.distributeRatio,
      ])
    );

    // 生成新规则
    formData.rules = filtered.map(
      (valuePath) =>
        ({
          sourceId: valuePath[0] || null,
          mediaPlatformSource: valuePath[1] || null,
          platformId: valuePath[2] || null,
          distributeRatio:
            existingRatios.get(
              `${valuePath[0] || ''}/${valuePath[1] || ''}/${valuePath[2] || ''}`
            ) ?? null,
        } as AssignRule)
    );

    // 触发验证
    nextTick(() => {
      formRef.value?.validate?.();
    });
  }
  // 复制比例
  function handleCopyRatio(rule: AssignRule) {
    if (!rule.distributeRatio) {
      message.error('请输入分配比例');
      return;
    }

    currentCopyRule.value = rule;
    copyMediaValue.value = [];
    showCopyPopover.value = true;
  }

  // Modal 内选择变化：父级若有子已选，则父级勾选无效（在此处过滤）
  function handleCopyMediaUpdate(value: string[][]) {
    // 去重
    const unique = Array.from(
      new Set((value || []).filter((p) => Array.isArray(p) && p.length > 0).map((p) => p.join('/')))
    );

    // 已存在于表单中的选择（用于判断父级是否有已选子级）
    const existingSet = new Set(
      (formData.assignMedia || [])
        .filter((p) => Array.isArray(p) && p.length > 0)
        .map((p) => p.join('/'))
    );

    // 如果同一前缀存在更长的路径（子级），则移除父级
    const filtered: string[] = [];
    const removedParents: string[] = [];
    for (const s of unique) {
      const prefix = s + '/';
      let hasChild = false;
      // 1) 在当前选择中查找子级
      for (const t of unique) {
        if (t !== s && t.startsWith(prefix)) {
          hasChild = true;
          break;
        }
      }
      // 2) 在已有的表单选择中查找子级
      if (!hasChild) {
        for (const ex of existingSet) {
          if (ex.startsWith(prefix)) {
            hasChild = true;
            break;
          }
        }
      }
      if (!hasChild) {
        filtered.push(s);
      } else {
        removedParents.push(s);
      }
    }

    copyMediaValue.value = filtered.map((s) => s.split('/')) as string[][];

    if (removedParents.length > 0) {
      message.warning('请勿同时配置不同层级媒体');
    }
  }

  // 确认复制
  function handleCopyConfirm() {
    if (!copyMediaValue.value?.length) {
      message.error('请选择要复制的媒体');
      return;
    }

    // 基于现有选择过滤父级（存在子级即不添加），并移除重复
    const newAssignMedia = filterAgainstExisting(copyMediaValue.value, formData.assignMedia || []);

    // 构造规则与媒体路径
    const newRules = newAssignMedia.map(
      (selectedPath) =>
        ({
          sourceId: selectedPath[0] || null,
          mediaPlatformSource: selectedPath[1] || null,
          platformId: selectedPath[2] || null,
          distributeRatio: currentCopyRule.value?.distributeRatio ?? null,
        } as AssignRule)
    );

    if (newRules.length === 0) {
      // 无可添加时，不提示、不关闭，点击无效
      return;
    }

    formData.rules.push(...newRules);
    formData.assignMedia.push(...newAssignMedia);

    showCopyPopover.value = false;
    copyMediaValue.value = [];
    message.success(`复制成功，共添加了 ${newRules.length} 条规则`);
  }

  // 工具：过滤父级（若存在任一子级被选中，则父级不保留）
  function filterParentsWhenChildrenSelected(paths: string[][]): string[][] {
    const strs = (paths || [])
      .filter((p) => Array.isArray(p) && p.length > 0)
      .map((p) => p.join('/'));
    const set = new Set(strs);
    const result: string[] = [];
    for (const s of set) {
      const prefix = s + '/';
      let hasChild = false;
      for (const other of set) {
        if (other !== s && other.startsWith(prefix)) {
          hasChild = true;
          break;
        }
      }
      if (!hasChild) result.push(s);
    }
    return result.map((s) => s.split('/')) as string[][];
  }

  // 工具：与已有选择对比过滤
  // 1) 去重（已存在的不再添加）
  // 2) 若选择中或已有中存在某路径的子级，则不添加该父路径
  function filterAgainstExisting(chosen: string[][], existing: string[][]): string[][] {
    const existingSet = new Set(
      (existing || []).filter((p) => Array.isArray(p) && p.length > 0).map((p) => p.join('/'))
    );

    const chosenList = (chosen || [])
      .filter((p) => Array.isArray(p) && p.length > 0)
      .map((p) => p.join('/'));
    const chosenUnique = Array.from(new Set(chosenList));

    const allSet = new Set<string>([...existingSet, ...chosenUnique]);

    const final: string[] = [];
    for (const s of chosenUnique) {
      if (existingSet.has(s)) continue; // 已存在，跳过
      const prefix = s + '/';
      let hasChild = false;
      for (const other of allSet) {
        if (other !== s && other.startsWith(prefix)) {
          hasChild = true;
          break;
        }
      }
      if (!hasChild) final.push(s);
    }

    return final.map((s) => s.split('/')) as string[][];
  }

  // 取消复制
  function handleCopyCancel() {
    showCopyPopover.value = false;
    copyMediaValue.value = [];
  }

  // 删除规则
  function handleDeleteRule(index: number) {
    // 删除对应的规则
    formData.rules.splice(index, 1);

    // 同时删除对应的分配媒体
    formData.assignMedia.splice(index, 1);
  }

  // 重置表单
  function resetForm() {
    formData.ruleName = '';
    formData.remark = '';
    formData.status = isDefaultMode.value ? 1 : 0; // 默认模式启用，非默认模式禁用
    formData.roleIds = [];
    formData.assignMedia = [];
    formData.rules = [];
    editId.value = undefined;
    isDefaultFlag.value = null;
  }

  // 打开抽屉
  async function open(row?: any) {
    loadOptions();
    resetForm();
    if (row) {
      editId.value = row.configId;
      isDefaultFlag.value = row.isDefault;
      formData.ruleName = row.ruleName ?? '';
      formData.remark = row.remark ?? '';
      formData.status = row.status ?? (isDefaultMode.value ? 1 : 0);
      formData.roleIds = cloneDeep(row.roleIds ?? []);
      formData.assignMedia = cloneDeep(row.assignMedia ?? []);
      formData.rules = cloneDeep(row.rules ?? []);
    }
    visible.value = true;
  }

  // 取消
  function handleCancel() {
    visible.value = false;
    resetForm();
  }

  // 保存
  async function handleSave() {
    try {
      await formRef.value?.validate();

      // 非默认模式需要校验分配比例
      if (!isDefaultMode.value && (formData.rules || []).length > 0) {
        const hasEmptyRatio = (formData.rules || []).some((rule) => !rule.distributeRatio);
        if (hasEmptyRatio) {
          message.error('请选择分配比例');
          return;
        }
      }

      saving.value = true;

      const submitData = { ...formData } as any;
      if (editId.value) {
        submitData.configId = editId.value;
      }

      if (editId.value) {
        if (isDefaultMode.value) {
          await updateDefaultRuleApi(submitData);
        } else {
          await updateTrafficRuleApi(submitData);
        }
      } else {
        await addTrafficRuleApi(submitData);
      }

      message.success('保存成功');
      visible.value = false;
      emit('success');
      resetForm();
    } catch (error: any) {
      if (error.msg) {
        dialog.info({ title: '温馨提示', content: error.msg, positiveText: '好的' });
      }
    } finally {
      saving.value = false;
    }
  }

  // 加载选项数据
  async function loadOptions() {
    try {
      const [roleRes, sourcesRes] = await Promise.all([getRoleListApi(), getSourcesApi()]);
      roleOptions.value = roleRes.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
      mediaTreeOptions.value = sourcesRes.data;
    } catch (error) {
      console.error('加载选项数据失败:', error);
    }
  }

  defineExpose({
    open,
  });
</script>
