import {
  intentionEnum,
  FollowUpNodeStatusEnum,
  followUpMethodEnum,
  communicationStatusEnum,
  clueStatusEnum,
  sexEnum,
  educationLevelEnum,
  maritalStatusEnum,
  houseTypeEnum,
  companyNatureEnum,
  contactRelationEnum,
  monthlyIncomeEnum,
  YesNoEnum,
  VehicleStatusEnum,
  CheckStatusEnum,
} from '@/enums/detailEnum';
import { get, post } from '@/utils/lib/axios.package';

/**
 * 获取线索详情
 * @param clueId 线索ID
 */
export function getClueDetail(clueId: number) {
  return get(`/cms/workspace/detail/${clueId}`);
}

/**
 * 获取线索详情-【公海】
 * @param clueId 线索ID
 */
export function getPublicClueDetail(clueId: number) {
  return get(`/cms/public-clue/detail/${clueId}`);
}

/**
 * 更新线索标签
 * @param clueId 线索ID
 * @param tags 标签数组
 */
export function updateClueTags(clueId: number, tags: string) {
  return post('/cms/workspace/add/tags', {
    clueId,
    tags,
  });
}

/**
 * 更新线索意向度
 * @param clueId 线索ID
 * @param intent 意向度
 */
export function updateClueIntention(clueId: number, intent: intentionEnum) {
  return post('/cms/workspace/update/intention', {
    clueId,
    intent,
  });
}

/**
 * 更新订单状态
 * @param params 请求参数
 */
export interface UpdateOrderStatusPayloadExt {
  signUrl?: string;
  productName?: string;
  creditLine?: string;
  productRate?: string;
  term?: string;
  loanAmount?: string;
  monthlyPayment?: string;
}

export interface UpdateOrderStatusParams {
  innerOrderNo: string;
  orderNode?: string | number;
  nodeStatus: number | null;
  remark: string;
  ext: UpdateOrderStatusPayloadExt;
  managementCode: string | number;
  businessOrderNode: string | number;
}

export function updateOrderStatus(params: UpdateOrderStatusParams) {
  return post('/cms/loan-order-record/update/order/orderNode', params);
}

export function getOrderNode(loanCode: string | number) {
  return post(`/cms/loan-order-record/query/orderNode/${loanCode}`);
}
/**
 * 添加跟进记录
 * @param params 请求参数
 */
export interface AddFollowUpRecordPayload {
  clueId: number;
  followUpType?: number;
  communicationStatus?: number | null;
  intent?: number | null;
  addWeChat?: number;
  isOwnCar?: number | null;
  isFullPaymentCar?: number | null;
  isOperatingCar?: number | null;
  isMortgageCar?: number | null;
  carYear?: number | null;
  mileage?: number | null;
  wechatType?: number | null;
  remark?: string;
  addAgent?: number;
  type?: number;
  agentTime?: number | null;
  agentRemark?: string;
  communicationDetail?: number | null;
}

export function addFollowUpRecord(params: AddFollowUpRecordPayload) {
  return post('/cms/workspace/add/follow-up/record', params);
}

// 取消待办记录
export function cancelFollowUpRecord(data) {
  return post('/cms/workspace/cancel/agent-record', data);
}

/**
 * 标记跟进状态
 * @param clueId 线索ID
 * @param clueType 状态
 */
export function updateClueStatus(params: {
  clueId: string | number;
  clueType: number;
  invalidType: number | null;
  invalidRemark?: string;
}) {
  return post('/cms/workspace/update/clue-status', params);
}

// 定义返回数据的类型
export interface CustomerDetail {
  // 线索核心信息
  clueInfoVo: {
    id: string;
    name: string; // 客户姓名
    headImg: string;
    createTime: string; // 创建时间
    triageTime: string; // 分发时间
    followStatus: clueStatusEnum;
    mobileNo: string;
    idCardNumber: string;
    sex: sexEnum;
    licensePlateNumber: string;
    provinceName: string;
    cityName: string;
    intent: intentionEnum;
    tagList: string[];
    mediaPlatformSource: string; // 线索来源
    communicationStatus: number | null; // 通讯状态
    addWeChat: YesNoEnum; // 是否加微
    followStatusIndex: FollowUpNodeStatusEnum;
    publicClueId: number | string;
  };

  // 贷款申请材料
  loanApplicationMaterials: {
    personalInfo: {
      email: string;
      educationLevel: educationLevelEnum;
      maritalStatus: maritalStatusEnum;
      childCount: number;
      residenceAddress: string;
      houseType: houseTypeEnum;
      company: string;
      companyAddress: string;
      companyPhone: string;
      companyNature: companyNatureEnum;
      monthlyIncome: monthlyIncomeEnum;
      contactName: string;
      contactPhone: string;
      contactRelation: contactRelationEnum;
      threeElementsStatus: CheckStatusEnum; // 三要素校验状态
    };
    vehicleInfo: {
      bodyColor: string;
      interiorColor: string;
      licenseCityName: string;
      factoryDate: string;
      transferCount: number;
      vehicleStatus: VehicleStatusEnum; // 车辆状态
      carOfPersonStatus: CheckStatusEnum; // 人车校验状态
    };
    certificateInfo: {
      idCardImgUrl: string;
      idCardImgBackUrl: string;
      drivingLicenseImgUrl: string;
      drivingLicenseImgBackUrl: string;
      paperDriverLicenseImgUrl: string;
      paperDriverLicenseImgBackUrl: string;
      bankCardImgUrl: string;
      bankCardBackUrl: string;
    };
    otherDocuments: {
      vehiclePhotos: string[];
      contactListMedias: string[];
      traffic12123Medias: string[];
      bankStatements: string[];
      bankAccountInfos: string[];
      insurances: string[];
    };
  };

  salesOpportunities: SalesOpportunity[];
  loanOrderRecords: LoanOrderRecord[];
  clueFollowUpRecords: ClueFollowUpRecord[];
  managementCodes?: string[];
  // followUpNodeStatus: FollowUpNodeStatusEnum;
  // nextFollowUpTime: number;
  clueAgentRecords: ClueAgentRecord[];

  currentTime: number | string;
}

export interface SalesOpportunity {
  saleOpportunityNo: string; // 销售机会ID
  managementCode: string; // 可进件资方
  productName: string; // 可进件产品
  productRate: string; // 产品利率
  loanAmount: number; // 可贷金额（万）
  matchTime: string; // 匹配时间
}

export interface LoanOrderRecord extends UpdateOrderStatusPayloadExt {
  innerOrderNo: string; // 订单ID
  loanOrderId: string; //贷款订单id
  managementCode: string; // 进件资方
  productName: string; // 进件产品
  productRate: string; // 产品利率
  orderNode: string; // 订单节点
  status: number; // 订单状态
  failMessage: string; // 未通过原因
  createTime: number; // 订创建时间
  updateTime: number; // 订单更新时间
  businessOrderNode: string | number; // 资方订单节点
  businessOrderNodeStr?: string; // 资方订单节点名称
}

/**
 * @description: 跟进记录
 */
export interface ClueFollowUpRecord {
  id: number;
  createByName: string; // 创建人
  createTime: number; // 跟进时间
  followUpType: followUpMethodEnum; // 跟进方式
  communicationStatus: communicationStatusEnum; // 沟通状态
  remark: string; // 备注
  communicationType?: number;
  communicationRecording?: string;
  duration?: number;
  outCallType?: number;
  communicationDetail?: number;
  clueId: number | string;
  communicationResult?: number | string;
}

export interface ClueAgentRecord {
  id: number;
  clueId: number;
  agentTime: number;
  remark: string;
  status: number;
  agentId: number;
  createTime: number;
  type: number;
  isCanceled: number;
}

export function updateFollowUpRecord(data) {
  return post('/cms/workspace/edit/follow-up/record', data);
}

interface contractSign {
  name: string;
  contractTemplateId: string;
  gender: number | null;
  idCard: string;
  phone: number | null | string;
  address: string;
  postalCode: string;
  productName: string;
  fundingEntity: string;
  contractAmount: string | number;
}
interface contractRecordReposeExtension extends contractSign {
  id: number;
  contractRecordId: number;
  createTime: string;
  updateTime: string;
}
export interface addContractRecordParams extends contractSign {
  contractRecordId?: number | null;
  clueId: number;
  contractTypeId: number | string;
}

//新增签约记录
export function addRecordContract(data: addContractRecordParams) {
  return post('/cms/contract/record/save', data);
}
//签约记录详情编辑
export function updateRecordContract(data: addContractRecordParams) {
  return post('/cms/contract/record/extension/update', data);
}
//重新签约
export function renewRecordContract(data: addContractRecordParams) {
  return post('/cms/contract/record/reSignContract', data);
}
export interface contractRecordRepose {
  id: number;
  contractTypeId: number;
  contractType: string; //签约类型名称
  contractMobile?: string; // 签约手机号
  contractUrl: string | null; //签约地址
  paymentUrl: string | null; //支付地址
  contractStatus: number; // 签约状态（1:待签约, 2:已签约）
  paymentStatus: number; //支付状态（1:待支付, 2:已支付）
  createTime: string | null; //创建时间
  evidenceTime: string | null; //存证时间
  extension?: contractRecordReposeExtension;
}
// 签约记录列表
export function getContractRecord({ clueId, ...params }) {
  return get(`/cms/contract/record/clue/${clueId}`, params);
}
// 查所有签约类型
export function getContractRecordTypes() {
  return get(`/cms/contract/record/types`);
}

export interface addRecordPayParams {
  contractRecordId: number | string;
  payTemplateId: number | string;
  qrCode: string;
}
//新建支付
export function addRecordContractPay(data: addRecordPayParams) {
  return post('/cms/contract/record/payment', data);
}
//支付状态编辑
export function recordContractPayStatus(data) {
  return post('/cms/contract/record/payment/status/update', data);
}
//下载电子存证
export function getESignUrl(contractMappingRecordId) {
  return get(`/cms/download/ESign/url/${contractMappingRecordId}`);
}

export interface fallUpParams {
  managementCode: string;
  innerOrderNo?: string;
  clueId?: number | string;
  sourceCode?: number;
}

//提交进件
export function addLoanApplyInfo(data: fallUpParams) {
  return post('/cms/loan-order-record/commit/loanApplyInfo', data);
}
//查询签约存证下载列表
// export function getESignProtocol(contractId) {
//   return get(`/cms/contract/record/mapping/${contractId}`);
// }

//获取支付模板列表
export function getPaymentTemplate() {
  return get(`/cms/contract/record/payment/templates`);
}

//获取签约模版列表
export function getSignTemplate() {
  return get(`/cms/contract/record/contract/templates`);
}
//获取邮编
export function getCityPostalCode(data) {
  return get(`/cms/city/postalCode`, data);
}
//查询签约存证下载列表
export function getESignProtocol(contractRecordId) {
  return get(`/cms/contract/record/template/download/${contractRecordId}`);
}
//查询支付二维码列表
export function getPayQrCodeImg() {
  return post(`/cms/systemParam/getByCodes`, ['PAY_QRCODE_PAGE_URL']);
}
