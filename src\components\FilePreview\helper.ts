import type { UploadType } from './types';

/**
 * 从 URL 中提取文件后缀名
 * @param url
 */
export function getFileExtension(url: string): string {
  if (!url) return '';
  try {
    const path = new URL(url).pathname;
    const parts = path.split('.');
    return parts.length > 1 ? parts.pop()!.toLowerCase() : '';
  } catch (e) {
    console.error('Invalid URL for getFileExtension:', url);
    return '';
  }
}

/**
 * 根据文件后缀名获取文档信息
 * @param extension
 */
export function getDocInfo(extension: string): { typeName: string; color: string } {
  const defaultInfo = { typeName: 'DOC', color: '#a0a0a0' };
  const map: Record<string, { typeName: string; color: string }> = {
    pdf: { typeName: 'PDF', color: '#e53935' },
    doc: { typeName: 'DOC', color: '#1e88e5' },
    docx: { typeName: 'DOCX', color: '#1e88e5' },
    xls: { typeName: 'XLS', color: '#43a047' },
    xlsx: { typeName: 'XLSX', color: '#43a047' },
    ppt: { typeName: 'PPT', color: '#f9a825' },
    pptx: { typeName: 'PPTX', color: '#f9a825' },
  };
  return map[extension] || defaultInfo;
}

/**
 * 根据 URL 推断文件类型
 * @param url
 */
export function getFileTypeFromUrl(url: string): UploadType {
  if (!url) return 'img';
  const extension = getFileExtension(url);

  const docExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  if (docExtensions.includes(extension)) {
    return 'doc';
  }

  const videoExtensions = ['mp4', 'webm', 'ogg'];
  if (videoExtensions.includes(extension)) {
    return 'video';
  }

  return 'img';
}
