<template>
  <BaseModal
    v-model:show="showModal"
    title="标记跟进状态"
    :width="500"
    positive-text="保存"
    :on-confirm="handleStatusSubmit"
  >
    <n-form
      ref="statusFormRef"
      :model="statusForm"
      :rules="statusRules"
      label-placement="left"
      :label-width="80"
    >
      <n-form-item path="clueType" label="状态">
        <RadioButtonPicker
          v-model="statusForm.clueType"
          :options="showClueTypeOptions"
          name="status-radio-group"
        />
      </n-form-item>
      <!-- 一、新增无效类型下拉选： 必填项  1、选择状态为无效时关联展示  2、下拉单选，下拉枚举见原型 -->
      <n-form-item
        v-if="statusForm.clueType === clueTypeEnum.INVALID"
        path="invalidType"
        label="无效类型"
      >
        <RadioButtonPicker
          v-model="statusForm.invalidType"
          :options="invalidTypeOptions"
          name="invalid-radio-group"
        />
      </n-form-item>
      <!-- 二、无效说明输入框：非必填项  1、限制输入100个字符 -->
      <n-form-item
        v-if="statusForm.clueType === clueTypeEnum.INVALID"
        path="invalidRemark"
        label="无效说明"
      >
        <n-input
          v-model:value="statusForm.invalidRemark"
          type="textarea"
          :autosize="{ minRows: 3 }"
          maxlength="100"
          show-count
        />
      </n-form-item>
    </n-form>
  </BaseModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import type { FormInst, FormRules } from 'naive-ui';
  import { NForm, NFormItem } from 'naive-ui';
  import { BaseModal } from '@/components/Modal';
  import RadioButtonPicker from '@/components/Form/src/components/RadioButtonPicker.vue';
  import { clueTypeOptions, clueTypeEnum } from '@/enums/detailEnum';
  import { updateClueStatus } from '@/api/detail';
  import { InvalidTypeOptions } from '@/views/client/enum';
  import { watch } from 'vue';

  const showModal = defineModel<boolean>('show', { default: false });
  const props = defineProps({
    clueId: {
      type: Number,
      required: true,
    },
    clueInfoVo: {
      type: Object,
      default: () => ({}),
    },
  });
  const emit = defineEmits(['submit-success']);

  const invalidTypeOptions = ref(InvalidTypeOptions.filter((item) => item.value !== 3)); // 过滤系统变更

  const statusFormRef = ref<FormInst | null>(null);
  const statusForm = ref<{
    clueType: clueTypeEnum | null;
    invalidType: number | null;
    invalidRemark: string;
  }>({
    clueType: null,
    invalidType: null,
    invalidRemark: '',
  });
  const showClueTypeOptions = computed(() => {
    return clueTypeOptions.map((item) => {
      return {
        ...item,
        disabled:
          props.clueInfoVo?.clueType === clueTypeEnum.VALID &&
          item.value === props.clueInfoVo.clueType,
      };
    });
  });
  const statusRules: FormRules = {
    clueType: {
      required: true,
      type: 'number',
      message: '请选择状态',
      trigger: ['blur', 'change'],
    },
    invalidType: {
      required: true,
      type: 'number',
      message: '请选择无效类型',
      trigger: ['blur', 'change'],
    },
  };

  watch(
    () => showModal.value,
    (show) => {
      if (!show) {
        resetForm();
      }
    }
  );

  const resetForm = () => {
    statusForm.value.clueType = null;
    statusForm.value.invalidType = null;
    statusForm.value.invalidRemark = '';
  };

  async function handleStatusSubmit() {
    try {
      await statusFormRef.value?.validate();

      await updateClueStatus({
        clueId: props.clueId,
        clueType: statusForm.value.clueType as clueTypeEnum,
        invalidType: statusForm.value.invalidType,
        invalidRemark: statusForm.value.invalidRemark,
      });
      window.$message.success('状态更新成功');
      emit('submit-success');

      showModal.value = false;
    } catch (error) {
      window.$message.error('状态更新失败，请稍后重试');
      return false;
    }
  }
</script>
