import CallFormDialog from './components/CallFormDialog.vue';
import { customerInfoCall, getFollowUpRecord } from '@/api/client';
import { ref } from 'vue';
import emitter from '@/utils/eventBus';

export async function submitFormDialog(options) {
  const { clubId } = options;
  if (!clubId && clubId !== 0) {
    return window.$message.error('未知线索');
  }
  const formRef = ref();
  const formModel = ref({ cardSlotNum: '' });
  const loading = ref(false);
  return window.$dialog.info({
    title: '工作机拨打确认',
    content: () => <CallFormDialog ref={formRef} v-model:form={formModel.value} />,
    positiveText: '确定',
    negativeText: '取消',
    maskClosable: false,
    showIcon: false,
    loading,
    actionStyle: { 'justify-content': 'center' },
    style: { width: '600px' },
    onPositiveClick: () => handleConfirm(),
  });
  async function handleConfirm() {
    await formRef.value?.formValid();
    loading.value = true;
    await customerInfoCall({ clubId, ...formModel.value })
      .then((res) => {
        if (res.code === 200) window.$message.success('电话拨打成功，请前往对应工作机查看');
        return res;
      })
      .finally(() => {
        loading.value = false;
      });
    emitter.emit('workCallFinish');
  }
}
export function getResult(id) {
  return getFollowUpRecord({ id });
}
