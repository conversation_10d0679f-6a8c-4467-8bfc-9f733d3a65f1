<template>
  <BaseDrawer v-model:show="visible" title="添加跟进记录" :width="600" :on-confirm="handleSubmit">
    <n-form
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-placement="left"
      label-width="120"
    >
      <!-- 跟进方式 -->
      <n-form-item path="followUpType" label="跟进方式:">
        <n-radio-group v-model:value="formModel.followUpType">
          <n-space>
            <n-radio
              v-for="option in followUpMethodOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 通讯状态 -->
      <n-form-item path="communicationStatus" label="通讯状态:">
        <n-radio-group v-model:value="formModel.communicationStatus">
          <n-space>
            <n-radio
              v-for="option in currentCommunicationStatusOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
              :on-update:value="(formModel.communicationDetail = null)"
              :disabled="
                formModel.followUpType === followUpMethodEnum.PHONE &&
                communicationStatus === communicationStatusEnum.CONNECTED &&
                option.value !== communicationStatusEnum.CONNECTED
              "
            />
          </n-space>
        </n-radio-group>
      </n-form-item>
      <!-- 通讯详情 -->
      <n-form-item
        v-if="phoneCommunicationDetailOptions.length > 0"
        path="communicationDetail"
        label="通讯详情:"
      >
        <n-radio-group v-model:value="formModel.communicationDetail">
          <n-space>
            <n-radio
              v-for="option in phoneCommunicationDetailOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>
      <!-- 意向度 -->
      <n-form-item path="intent" label="意向度:">
        <RadioButtonPicker v-model="formModel.intent" :options="intentionOptions" />
      </n-form-item>

      <!-- 是否本人车辆 -->
      <n-form-item path="isOwnCar" label="是否本人车辆:">
        <n-radio-group v-model:value="formModel.isOwnCar">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 是否全款车 -->
      <n-form-item path="isFullPaymentCar" label="是否全款车:">
        <n-radio-group v-model:value="formModel.isFullPaymentCar">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 是否营运车 -->
      <n-form-item path="isOperatingCar" label="是否营运车:">
        <n-radio-group v-model:value="formModel.isOperatingCar">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 是否抵押车 -->
      <n-form-item path="isMortgageCar" label="是否抵押车:">
        <n-radio-group v-model:value="formModel.isMortgageCar">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 车辆年份 -->
      <n-form-item path="carYear" label="车辆年份:">
        <n-select
          v-model:value="formModel.carYear"
          :options="carYearOptions"
          placeholder="请选择"
          style="width: 30%"
        />
      </n-form-item>

      <!-- 公里数 -->
      <n-form-item path="mileage" label="公里数:">
        <n-input-number
          v-model:value="formModel.mileage"
          :precision="2"
          :min="0"
          placeholder="请输入"
          style="width: 30%"
          :show-button="false"
        >
          <template #suffix>万</template>
        </n-input-number>
      </n-form-item>

      <!-- 是否加微 -->
      <n-form-item path="addWeChat" label="是否加微:">
        <n-radio-group v-model:value="formModel.addWeChat">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 加微类型 -->
      <n-form-item v-if="formModel.addWeChat === 1" path="wechatType" label="加微类型:">
        <n-radio-group v-model:value="formModel.wechatType">
          <n-space>
            <n-radio
              v-for="option in wechatTypeOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 跟进备注 -->
      <n-form-item path="remark" label="跟进备注:">
        <n-input
          v-model:value="formModel.remark"
          type="textarea"
          :autosize="{ minRows: 3 }"
          maxlength="200"
          show-count
        />
      </n-form-item>

      <!-- 是否创建待办 -->
      <n-form-item path="addAgent" label="是否创建待办:">
        <n-radio-group v-model:value="formModel.addAgent">
          <n-space>
            <n-radio
              v-for="option in yesNoOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </n-space>
        </n-radio-group>
      </n-form-item>

      <!-- 待办事项 -->
      <div v-if="formModel.addAgent === 1" class="todo-section">
        <n-form-item path="type" label="待办类型:">
          <n-select v-model:value="formModel.type" :options="todoTypeOptions" />
        </n-form-item>
        <n-form-item path="agentTime" label="待办处理时间:">
          <n-date-picker v-model:value="formModel.agentTime" type="datetime" style="width: 100%" />
        </n-form-item>
        <n-form-item path="agentRemark" label="待办说明:">
          <n-input
            v-model:value="formModel.agentRemark"
            type="textarea"
            :autosize="{ minRows: 3 }"
            maxlength="200"
            show-count
          />
        </n-form-item>
      </div>
    </n-form>
  </BaseDrawer>
</template>

<script lang="ts" setup>
  import { getClueDetail, getPublicClueDetail } from '@/api/detail';
  import emitter from '@/utils/eventBus';
  import { ref, reactive, watch, computed, watchEffect, type PropType } from 'vue';
  import {
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NDatePicker,
    NRadioGroup,
    NRadio,
    NSpace,
    type FormInst,
    type FormRules,
  } from 'naive-ui';
  import { BaseDrawer } from '@/components/Drawer';
  import { RadioButtonPicker } from '@/components/Form';
  import { addFollowUpRecord, type AddFollowUpRecordPayload } from '@/api/detail';
  import {
    intentionOptions,
    followUpMethodOptions,
    yesNoOptions,
    wechatTypeOptions,
    todoTypeOptions,
    communicationStatusEnum,
    followUpMethodEnum,
    wechatCommunicationStatusOptions,
    phoneCommunicationStatusOptions,
    phoneConnectedOptions,
    phoneNoConnectedOptions,
    communicationStatusEnumV2,
  } from '@/enums/detailEnum';

  const props = defineProps({
    communicationStatus: {
      type: Number as PropType<number | null>,
      default: null,
    },
    clueType: {
      type: String as PropType<'publicClue' | 'clue'>,
      default: 'clue',
    },
  });

  const visible = ref(false);
  const clueId = ref(0);
  const emit = defineEmits(['submit-success']);
  const formRef = ref<FormInst | null>(null);

  const initialFormState = {
    followUpType: 1,
    communicationStatus: null,
    communicationDetail: null,
    intent: null,
    isOwnCar: null,
    isFullPaymentCar: null,
    isOperatingCar: null,
    isMortgageCar: null,
    carYear: null,
    mileage: null,
    wechatType: null,
    addWeChat: 0,
    remark: '',
    addAgent: 0,
    type: 1,
    agentTime: null,
    agentRemark: '',
  };

  const formModel = reactive<Omit<AddFollowUpRecordPayload, 'clueId'>>({ ...initialFormState });

  const rules = computed<FormRules>(() => {
    const activeRules: FormRules = {
      followUpType: {
        required: true,
        type: 'number',
        message: '请选择跟进方式',
        trigger: 'change',
      },
      communicationStatus: {
        required: true,
        type: 'number',
        message: '请选择通讯状态',
        trigger: 'change',
      },
      intent: { required: true, type: 'number', message: '请选择意向度', trigger: 'change' },
      wechatType: { required: true, type: 'number', message: '请选择加微类型', trigger: 'change' },
    };

    if (formModel.addAgent === 1) {
      activeRules.type = {
        required: true,
        type: 'number',
        message: '请选择待办类型',
        trigger: 'change',
      };
      activeRules.agentTime = {
        required: true,
        type: 'number',
        message: '请选择待办处理时间',
        trigger: 'change',
      };
    }
    return activeRules;
  });

  const currentCommunicationStatusOptions = computed(() => {
    if (formModel.followUpType === followUpMethodEnum.PHONE) {
      return phoneCommunicationStatusOptions;
    }
    if (formModel.followUpType === followUpMethodEnum.WECHAT) {
      return wechatCommunicationStatusOptions;
    }
    return [];
  });

  // 动态生成车辆年份选项
  const carYearOptions = computed(() => {
    const currentYear = new Date().getFullYear();
    // 显式声明数组类型
    const options: { label: string; value: number }[] = [];

    for (let year = currentYear; year >= 1999; year--) {
      options.push({
        label: `${year}`,
        value: year,
      });
    }
    return options;
  });

  const phoneCommunicationDetailOptions = computed(() => {
    if (formModel.followUpType === followUpMethodEnum.PHONE) {
      if (formModel.communicationStatus === communicationStatusEnumV2.CONNECTED)
        return phoneConnectedOptions;
      if (formModel.communicationStatus === communicationStatusEnumV2.NOT_CONNECTED)
        return phoneNoConnectedOptions;
    }
    return [];
  });
  watch(
    () => formModel.followUpType,
    () => {
      formModel.communicationStatus = null;
    }
  );
  // 监听是否加微字段的变化
  watch(
    () => formModel.addWeChat,
    () => {
      if (formModel.addWeChat === 0) {
        formModel.wechatType = null;
      }
    }
  );

  watch(
    () => formModel.addAgent,
    (isCreatingTodo) => {
      if (!isCreatingTodo) {
        formRef.value?.restoreValidation();
      }
    }
  );

  watchEffect(() => {
    if (
      formModel.followUpType === followUpMethodEnum.PHONE &&
      props.communicationStatus === communicationStatusEnum.CONNECTED
    ) {
      formModel.communicationStatus = communicationStatusEnum.CONNECTED;
    }
  });
  watch(
    () => visible.value,
    (show) => {
      if (show) {
        fetchData();
      }
      if (!show) {
        // 重置表单数据
        Object.assign(formModel, { ...initialFormState });
      }
    }
  );
  async function handleSubmit() {
    try {
      await formRef.value?.validate();
      const payload: AddFollowUpRecordPayload = {
        clueId: clueId.value,
        ...formModel,
        ...(formModel.followUpType === followUpMethodEnum.WECHAT
          ? { communicationDetail: null }
          : {}),
      };
      console.log('payload', payload);
      console.log('formModel', formModel);
      await addFollowUpRecord(payload);

      window.$message.success('添加成功');
      emit('submit-success');
      emitter.emit('add-follow-up-success');
      // 重置表单数据
      Object.assign(formModel, { ...initialFormState });
    } catch (error) {
      window.$message.error('添加失败');
      return false;
    }
  }

  function openDrawer(id: number) {
    clueId.value = id;
    visible.value = true;
  }

  async function fetchData() {
    if (!clueId.value) return;

    try {
      const api = props.clueType === 'publicClue' ? getPublicClueDetail : getClueDetail;
      const { data } = await api(clueId.value);

      const keys = Object.keys(initialFormState);

      // 优先使用跟进记录中的表单数据，否则重置为初始状态
      const followUpRecord = data?.clueFollowUpRecords?.[0];
      keys.forEach((key) => {
        (formModel as AddFollowUpRecordPayload)[key] =
          followUpRecord && followUpRecord.hasOwnProperty(key)
            ? followUpRecord[key]
            : (initialFormState as AddFollowUpRecordPayload)[key];
      });
    } catch {}
  }

  defineExpose({
    openDrawer,
  });
</script>

<style scoped>
  .todo-section {
    border: 1px dashed #ccc;
    padding: 16px;
    border-radius: 4px;
    margin-top: -10px;
  }
</style>
