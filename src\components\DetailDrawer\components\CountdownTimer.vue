<template>
  <div class="countdown">
    <span class="time-box">{{ hours }}</span>
    <span class="colon">:</span>
    <span class="time-box">{{ minutes }}</span>
    <span class="colon">:</span>
    <span class="time-box">{{ seconds }}</span>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onUnmounted, watch } from 'vue';

  const props = defineProps<{
    targetTime: string | number | Date;
  }>();

  const hours = ref('00');
  const minutes = ref('00');
  const seconds = ref('00');
  let timer: ReturnType<typeof setInterval> | null = null;

  function updateCountdown() {
    const now = new Date().getTime();
    const target = new Date(props.targetTime).getTime();
    const distance = target - now;

    if (distance < 0) {
      hours.value = '00';
      minutes.value = '00';
      seconds.value = '00';
      if (timer) clearInterval(timer);
      return;
    }

    const h = Math.floor(distance / (1000 * 60 * 60));
    const m = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const s = Math.floor((distance % (1000 * 60)) / 1000);

    hours.value = h.toString().padStart(2, '0');
    minutes.value = m.toString().padStart(2, '0');
    seconds.value = s.toString().padStart(2, '0');
  }

  watch(
    () => props.targetTime,
    () => {
      if (timer) clearInterval(timer);
      updateCountdown();
      timer = setInterval(updateCountdown, 1000);
    },
    { immediate: true }
  );

  onUnmounted(() => {
    if (timer) clearInterval(timer);
  });
</script>

<style scoped>
  .countdown {
    display: inline-flex;
    align-items: center;
  }
  .time-box {
    display: inline-block;
    padding: 2px 4px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 2px;
    font-weight: bold;
    min-width: 24px;
    text-align: center;
  }
  .colon {
    margin: 0 2px;
  }
</style>
