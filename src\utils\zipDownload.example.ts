/**
 * ZIP压缩下载工具使用示例
 *
 * 这个文件展示了如何使用 zipDownload.ts 中的各种ZIP压缩下载功能
 */

import {
  downloadFilesAsZip,
  downloadPdfsAsZip,
  downloadContractFilesAsZip,
  type FileItem,
  type ZipDownloadConfig,
} from './zipDownload';

// ============= 基础使用示例 =============

/**
 * 示例1: 简单的多文件ZIP下载
 */
export async function example1_SimpleZipDownload() {
  const urls = [
    'https://example.com/file1.pdf',
    'https://example.com/file2.pdf',
    'https://example.com/file3.pdf',
  ];

  // 使用默认配置下载为ZIP
  const result = await downloadFilesAsZip(urls);
  console.log('ZIP下载结果:', result);
}

/**
 * 示例1.1: 单文件直接下载（不打包成ZIP）
 */
export async function example1_1_SingleFileDownload() {
  // 当只有一个文件时，会直接下载文件而不是打包成ZIP
  const singleFile = 'https://example.com/document.pdf';

  const result = await downloadFilesAsZip([singleFile], {
    showProgress: true,
    showResult: true,
  });

  console.log('单文件下载结果:', result);
  // 注意：result.zipSize 为 0，因为没有生成ZIP文件
}

/**
 * 示例2: 带自定义配置的ZIP下载
 */
export async function example2_CustomZipConfig() {
  const urls = [
    'https://example.com/contract1.pdf',
    'https://example.com/contract2.pdf',
    'https://example.com/invoice.pdf',
  ];

  const config: ZipDownloadConfig = {
    zipFilename: '合同文档包.zip',
    showProgress: true,
    showResult: true,
    maxConcurrent: 3,
    timeout: 30000,
    generateFileName: (url, index) => {
      const types = ['主合同', '补充协议', '发票'];
      return `${types[index] || '文档'}_${index + 1}.pdf`;
    },
    onProgress: (current, total, filename) => {
      console.log(`正在下载 ${current}/${total}: ${filename}`);
    },
  };

  const result = await downloadFilesAsZip(urls, config);
  console.log('ZIP下载结果:', result);
}

/**
 * 示例3: 使用FileItem数组（支持参数）
 */
export async function example3_FileItemsDownload() {
  const files: FileItem[] = [
    {
      url: '/api/download/contract/123',
      filename: '购房合同.pdf',
      params: { token: 'your-token', format: 'pdf' },
    },
    {
      url: '/api/download/invoice/456',
      filename: '发票.pdf',
      params: { token: 'your-token' },
    },
    {
      url: 'https://example.com/public-doc.pdf',
      filename: '公开文档.pdf',
      // 无需参数
    },
  ];

  const result = await downloadFilesAsZip(files, {
    zipFilename: '业务文档.zip',
    maxConcurrent: 2,
    timeout: 60000, // 1分钟超时
  });

  console.log('ZIP下载结果:', result);
}

/**
 * 示例3.1: 单文件下载（带参数）
 */
export async function example3_1_SingleFileWithParams() {
  // 单个文件，带API参数，会直接下载而不打包
  const singleFileWithParams: FileItem = {
    url: '/api/download/contract/123',
    filename: '重要合同.pdf',
    params: {
      token: 'your-auth-token',
      format: 'pdf',
      watermark: 'true'
    },
  };

  const result = await downloadFilesAsZip([singleFileWithParams], {
    showProgress: true,
    showResult: true,
    timeout: 60000,
  });

  console.log('单文件（带参数）下载结果:', result);
  // 由于有参数，会通过fetch下载然后用blob方式保存
}

// ============= 实际业务场景示例 =============

/**
 * 示例4: 下载签约相关文档（您的业务场景）
 */
export async function example4_ContractDocuments() {
  // 模拟从API获取的文档列表
  const protocolList = [
    'https://example.com/contract/protocol1.pdf',
    'https://example.com/contract/protocol2.pdf',
    'https://example.com/contract/evidence.pdf',
  ];

  if (!protocolList || protocolList.length === 0) {
    window.$message?.error('暂无存证下载');
    return;
  }

  // 使用专门的合同文档下载函数
  const result = await downloadContractFilesAsZip(protocolList, '12345');
  return result;
}

/**
 * 示例5: 在Vue组件中的使用
 */
export async function example5_VueComponentUsage() {
  // 这是在Vue组件中的使用示例

  // 模拟handleDownload函数
  async function handleDownload(row: any) {
    try {
      // 获取文件列表
      const { data: protocolList } = await getESignProtocol(row.id);

      if (!protocolList || protocolList.length === 0) {
        window.$message?.error('暂无存证下载');
        return;
      }

      // 下载为ZIP
      const result = await downloadContractFilesAsZip(protocolList, row.id);

      if (result.success > 0) {
        console.log(`成功下载 ${result.success} 个文件到ZIP包中`);
      }

      return result;
    } catch (error) {
      console.error('下载失败:', error);
      window.$message?.error('文件下载失败');
    }
  }

  // 模拟API调用
  async function getESignProtocol(contractId: string) {
    // 这里是您的API调用
    return {
      data: ['https://example.com/contract1.pdf', 'https://example.com/contract2.pdf'],
    };
  }

  return { handleDownload };
}

/**
 * 示例6: 批量下载用户上传的文件
 */
export async function example6_UserUploadedFiles() {
  // 模拟用户文件列表
  const userFiles = [
    { url: 'https://example.com/uploads/photo1.jpg', name: '身份证正面.jpg' },
    { url: 'https://example.com/uploads/photo2.jpg', name: '身份证反面.jpg' },
    { url: 'https://example.com/uploads/doc.pdf', name: '收入证明.pdf' },
    { url: 'https://example.com/uploads/bank.pdf', name: '银行流水.pdf' },
  ];

  const fileItems: FileItem[] = userFiles.map((file) => ({
    url: file.url,
    filename: file.name,
  }));

  const result = await downloadFilesAsZip(fileItems, {
    zipFilename: '用户资料.zip',
    maxConcurrent: 4,
    showProgress: true,
    onProgress: (current, total, filename) => {
      // 可以在这里更新UI进度条
      const percentage = Math.round((current / total) * 100);
      console.log(`下载进度: ${percentage}% - ${filename}`);
    },
  });

  return result;
}

/**
 * 示例7: 错误处理和重试机制
 */
export async function example7_ErrorHandlingAndRetry() {
  const urls = [
    'https://example.com/valid-file.pdf',
    'https://invalid-url/file.pdf', // 这个会失败
    'https://example.com/another-file.pdf',
  ];

  try {
    const result = await downloadFilesAsZip(urls, {
      zipFilename: '测试文件.zip',
      showResult: false, // 我们自己处理结果显示
      maxConcurrent: 2,
      timeout: 15000, // 15秒超时
    });

    // 自定义结果处理
    if (result.failed > 0) {
      console.warn('部分文件下载失败:', result.errors);

      // 可以选择重试失败的文件
      const failedUrls = result.errors.map((err) => err.url);

      if (result.success > 0) {
        window.$message?.warning(
          `ZIP文件已生成，包含 ${result.success} 个文件。${result.failed} 个文件下载失败。`
        );
      } else {
        window.$message?.error('所有文件下载失败，无法生成ZIP文件');

        // 可以实现重试逻辑
        const shouldRetry = confirm('是否重试下载失败的文件？');
        if (shouldRetry) {
          await retryFailedDownloads(failedUrls);
        }
      }
    } else {
      window.$message?.success(`所有 ${result.success} 个文件已成功打包下载！`);
    }

    return result;
  } catch (error) {
    console.error('ZIP下载过程中发生错误:', error);
    window.$message?.error('文件打包下载失败，请稍后重试');
  }
}

// ============= 工具函数示例 =============

/**
 * 重试失败的下载
 */
async function retryFailedDownloads(urls: string[], maxRetries = 2) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`第 ${attempt} 次重试下载...`);

    const result = await downloadFilesAsZip(urls, {
      zipFilename: `重试下载_${attempt}.zip`,
      timeout: 30000, // 重试时增加超时时间
      showProgress: true,
    });

    if (result.failed === 0) {
      window.$message?.success('重试成功！所有文件已打包下载');
      break;
    }

    if (attempt === maxRetries) {
      window.$message?.error('重试失败，请检查网络连接或文件链接');
    } else {
      // 更新失败的URL列表，继续重试
      urls = result.errors.map((err) => err.url);
    }
  }
}

/**
 * 根据文件类型分组打包下载
 */
export async function downloadByFileTypeGroups(
  files: Array<{ url: string; type: string; name: string }>
) {
  // 按文件类型分组
  const groups = files.reduce((acc, file) => {
    if (!acc[file.type]) acc[file.type] = [];
    acc[file.type].push(file);
    return acc;
  }, {} as Record<string, typeof files>);

  // 分组打包下载
  const results = [];
  for (const [type, typeFiles] of Object.entries(groups)) {
    console.log(`开始打包下载 ${type} 类型文件...`);

    const fileItems: FileItem[] = typeFiles.map((f) => ({
      url: f.url,
      filename: f.name,
    }));

    const result = await downloadFilesAsZip(fileItems, {
      zipFilename: `${type}文件包.zip`,
      showProgress: true,
    });

    results.push({ type, result });
  }

  return results;
}

/**
 * 大文件优化下载
 */
export async function downloadLargeFiles(urls: string[]) {
  return await downloadFilesAsZip(urls, {
    zipFilename: '大文件包.zip',
    maxConcurrent: 1, // 大文件建议单个下载
    timeout: 120000, // 2分钟超时
    showProgress: true,
    onProgress: (current, total, filename) => {
      console.log(`下载大文件 ${current}/${total}: ${filename}`);
      // 可以在这里显示更详细的进度信息
    },
  });
}
