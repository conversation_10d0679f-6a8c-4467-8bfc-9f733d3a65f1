/// <reference types="vite/client" />
import MockAdapter from 'axios-mock-adapter';
import type { AxiosInstance } from 'axios';

// 自动导入所有子目录的 mock 文件
function importAllMocks() {
  const mockModules: any[] = [];

  // 使用 Vite 的 import.meta.glob 自动导入所有子目录的 mock 文件
  const modules = import.meta.glob('./**/*.ts', { eager: true });

  Object.values(modules).forEach((module: any) => {
    if (module.default && typeof module.default === 'function') {
      mockModules.push(module.default);
    }
  });

  return mockModules;
}

export function setupAxiosMock(axiosInstance: AxiosInstance) {
  const mockAdapter = new MockAdapter(axiosInstance);

  // 自动导入并设置所有 mock
  const mockSetupFunctions = importAllMocks();

  mockSetupFunctions.forEach((setupFunction) => {
    try {
      setupFunction(mockAdapter);
      console.log('✅ Mock setup completed for:', setupFunction.name);
    } catch (error) {
      console.error('❌ Mock setup failed for:', setupFunction.name, error);
    }
  });

  console.log(`🚀 Axios Mock Adapter initialized with ${mockSetupFunctions.length} mock modules`);

  return mockAdapter;
}
