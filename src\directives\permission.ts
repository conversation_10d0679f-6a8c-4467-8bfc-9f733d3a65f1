import { ObjectDirective } from 'vue';
import { usePermission } from '@/hooks/web/usePermission';
import router from '@/router';
export const permission: ObjectDirective = {
  mounted(el: HTMLButtonElement, binding) {
    const { action, effect } = binding.value;
    if (!action) return;
    const { currentRoute } = router;
    const { buttonsPreFix } = currentRoute.value?.meta;
    const code = buttonsPreFix ? buttonsPreFix + action : action;
    const { buttonsHasPermission } = usePermission();
    buttonsHasPermission(code).then((res) => {
      if (!res) {
        if (effect == 'disabled') {
          el.disabled = true;
          el.style['disabled'] = 'disabled';
          el.classList.add('n-button--disabled');
        } else {
          el.remove();
        }
      }
    });
  },
};
export const callPermission: ObjectDirective = {
  mounted(el: HTMLButtonElement, binding) {
    const { effect } = binding.value || {};
    const { roleMdmHasPermission } = usePermission();
    roleMdmHasPermission().then((data) => {
      if (!data) {
        if (effect == 'disabled') {
          el.disabled = true;
          el.style['disabled'] = 'disabled';
          el.classList.add('n-button--disabled');
        } else {
          el.remove();
        }
      }
    });
  },
};
