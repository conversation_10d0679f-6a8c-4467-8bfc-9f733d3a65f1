import { validPhone } from '@/utils/formValidationRules';
import type { FormRules } from 'naive-ui';

const rules: FormRules = {
  username: [
    {
      required: true,
      message: '请输入姓名',
      trigger: 'blur',
    },
  ],
  mobileNo: [
    {
      required: true,
      validator: validPhone,
      trigger: 'blur',
    },
  ],
  roleId: [
    {
      required: true,
      message: '请选择角色',
      trigger: 'change',
      type: 'number',
    },
  ],
  adminBranch: [
    {
      required: true,
      message: '请选择部门',
      trigger: 'change',
      type: 'number',
    },
  ],
  branchName: [
    {
      required: true,
      message: '请输入部门名称',
      trigger: 'blur',
    },
  ],
  deptId: [
    {
      type: 'number',
      required: true,
      message: '请选择移交人部门',
      trigger: ['blur', 'change'],
    },
  ],
  extendUserId: [
    {
      type: 'number',
      required: true,
      message: '请选择移交人',
      trigger: 'change',
    },
  ],
};
export default rules;
