import service from '@/utils/http/axios/request';
import { type AxiosRequestConfig } from 'axios';
import { useMessage } from 'naive-ui';
import qs from 'qs';

const message = useMessage();

// 常用请求封装
export function get<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.get(url, {
    ...config,
    params,
    paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
  });
}

export function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.post(url, data, config);
}

export function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.put(url, data, config);
}

export function del<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
  return service.delete(url, config);
}

export function patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.patch(url, data, config);
}

// 上传文件封装
export function uploadFile<T = any>(
  url: string,
  file: File,
  extraData?: Record<string, any>,
  config?: AxiosRequestConfig
): Promise<T> {
  const formData = new FormData();
  formData.append('file', file);
  if (extraData) {
    Object.entries(extraData).forEach(([key, value]) => {
      formData.append(key, value);
    });
  }

  return service.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    ...config,
  });
}

// 下载文件封装
export function downLoadFile(url: string, params?: any, filename = 'file.xlsx') {
  return service
    .get(url, {
      params,
      responseType: 'blob',
    })
    .then((res) => {
      const blob = new Blob([res.data]);
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      link.click();
      URL.revokeObjectURL(link.href);
    })
    .catch(() => {
      message.error('下载失败');
    });
}
