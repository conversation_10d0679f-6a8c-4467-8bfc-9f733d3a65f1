<template>
  <n-form ref="formRef" :model="model" label-placement="left">
    <n-form-item path="dayLimit" :rule="rules.dayAndEach">
      <template #label>
        <div class="tips-label">
          单日可认领线索数量
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-icon size="15">
                <HelpCircleOutline />
              </n-icon>
            </template>
            单日单账号可认领线索上限，自然日维度计数
          </n-tooltip>
        </div>
      </template>
      <n-input-number v-model:value="model.dayLimit" />
    </n-form-item>
    <n-form-item path="eachLimit" :rule="rules.dayAndEach">
      <template #label>
        <div class="tips-label">
          单次可认领线索数量
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-icon size="15">
                <HelpCircleOutline />
              </n-icon>
            </template>
            单次单账号可认领线索上限
          </n-tooltip>
        </div>
      </template>
      <n-input-number v-model:value="model.eachLimit" />
    </n-form-item>
    <n-form-item path="followLimit" :rule="rules.common">
      <template #label>
        <div class="tips-label">
          累计最大待跟进线索数量
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-icon size="15">
                <HelpCircleOutline />
              </n-icon>
            </template>
            单账号我的客户列表归属于自己的最大待跟进线索上限
          </n-tooltip>
        </div>
      </template>
      <n-input-number v-model:value="model.followLimit" />
    </n-form-item>
  </n-form>
</template>
<script lang="ts" setup>
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { ref, watch } from 'vue';
  import { updateRoleReceive, get_receive_by_id } from '@/api/system/role';
  import { useMessage } from 'naive-ui';
  import { isNullOrUnDef } from '@/utils/is';
  import { useUser } from '@/store/modules/user';
  const userStore = useUser();
  const message = useMessage();
  const defaultModel = {
    dayLimit: null,
    eachLimit: null,
    followLimit: null,
  };
  defineExpose({
    sure,
  });
  const props = defineProps({
    roleId: {
      type: [Number, String],
      default: '',
    },
  });
  watch(
    () => props.roleId,
    () => {
      getData();
    },
    { immediate: true }
  );
  const model = ref({ ...defaultModel });
  const rules = ref({
    common: [
      {
        required: true,
        message: '此项不能为空',
        trigger: ['input', 'blur', 'change'],
        type: 'number',
      },
    ],
    dayAndEach: [
      {
        required: true,
        trigger: ['input', 'blur', 'change'],
        type: 'number',
        validator: (rule, value) => {
          const { field } = rule;
          const otherField = field === 'dayLimit' ? 'eachLimit' : 'dayLimit';
          if (isNullOrUnDef(value)) {
            return new Error('此项不能为空');
          }
          if (!isNullOrUnDef(model.value.dayLimit) && !isNullOrUnDef(model.value.eachLimit)) {
            if (model.value.dayLimit < model.value.eachLimit) {
              const errorMsg =
                field === 'dayLimit'
                  ? '单日可认领线索数量必须大于单次可认领线索数量'
                  : '单次可认领线索数量必须小于单日可认领线索数量';
              return new Error(errorMsg);
            }
            // 清除其他字段的错误提示
            formRef.value.restoreValidation(otherField);
          }
          return true;
        },
      },
    ],
  });
  const formRef = ref();
  function getData() {
    get_receive_by_id(props.roleId).then((res) => {
      if (res.code == 200) {
        model.value = { ...defaultModel, ...res.data };
      }
    });
  }
  function sure() {
    return formRef.value?.validate((errors) => {
      if (!errors) {
        updateRoleReceive({
          roleId: props.roleId,
          ...model.value,
        }).then((res) => {
          if (res.code == 200) {
            message.success('配置成功');
            userStore.getInfo();
          }
        });
        return !errors;
      }
    });
  }
</script>
<style lang="less" scoped>
  .tips-label {
    display: flex;
    align-items: center;
  }
</style>
