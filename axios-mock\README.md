# Axios Mock 系统

这是一个简单的 Axios Mock 系统，支持自动导入子目录中的 mock 数据。

## 目录结构

```
axios-mock/
├── index.ts          # 主入口文件，setupAxiosMock 函数
├── _util.ts          # 工具函数
├── dashboard/        # Dashboard 相关 mock
│   └── index.ts
├── user/            # 用户相关 mock
│   └── index.ts
├── table/           # 表格相关 mock
│   └── index.ts
└── system/          # 系统管理相关 mock
    └── index.ts
```

## 使用方法

### 1. 自动导入
系统会自动导入所有子目录下的 `index.ts` 文件，无需手动配置。

### 2. 添加新的 mock 模块
在任意子目录下创建 `index.ts` 文件，导出一个 setup 函数：

```typescript
import { defineAxiosMock, resultSuccess } from '../_util';
import type MockAdapter from 'axios-mock-adapter';

// 你的 mock 数据
const mockData = {
  // 数据内容
};

export default function setupYourMock(mockAdapter: MockAdapter) {
  defineAxiosMock(mockAdapter, {
    '/api/your-endpoint': () => resultSuccess(mockData),
    '[POST]/api/your-post-endpoint': (config) => resultSuccess({ success: true }),
  });
}
```

### 3. 支持的请求方法
- 默认 GET: `'/api/endpoint'`
- 指定方法: `'[POST]/api/endpoint'`, `'[PUT]/api/endpoint'`, `'[DELETE]/api/endpoint'`

### 4. 工具函数
- `resultSuccess(data, options)`: 返回成功响应格式
- `resultError(message, options)`: 返回错误响应格式
- `defineAxiosMock(mockAdapter, mocks)`: 定义 mock 路由

## 响应格式

```typescript
// 成功响应
{
  code: 200,
  data: any,      // 你的数据
  message: 'ok',
  type: 'success'
}

// 错误响应
{
  code: -1,
  data: null,
  message: 'error message',
  type: 'error'
}
```

## 注意事项

1. 确保每个子目录的 `index.ts` 文件都导出一个默认函数
2. Mock 数据只在开发环境且 `VITE_USE_MOCK=true` 时生效
3. 系统会自动打印 mock 模块的加载状态
