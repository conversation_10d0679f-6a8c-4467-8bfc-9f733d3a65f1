<template>
  <n-drawer
    v-model:show="visible"
    v-bind="$attrs"
    :auto-focus="false"
    :mask-closable="false"
    :close-on-esc="false"
  >
    <n-drawer-content :title="title" :native-scrollbar="false" closable>
      <!-- Body Content Slot -->
      <slot></slot>

      <!-- Footer Slot -->
      <template #footer>
        <slot name="footer">
          <n-space justify="end">
            <n-button @click="handleNegativeClick">{{ negativeText }}</n-button>
            <n-button type="primary" :loading="isLoading" @click="handlePositiveClick">
              {{ positiveText }}
            </n-button>
          </n-space>
        </slot>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { NDrawer, NDrawerContent, NButton, NSpace } from 'naive-ui';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    positiveText: {
      type: String,
      default: '确认',
    },
    negativeText: {
      type: String,
      default: '取消',
    },
    onConfirm: {
      type: Function as PropType<() => Promise<any>>,
      default: () => Promise.resolve(true),
    },
  });

  const visible = defineModel('show', { type: Boolean, default: false });
  const isLoading = ref(false);

  async function handlePositiveClick() {
    isLoading.value = true;
    try {
      const result = await props.onConfirm();
      if (result !== false) {
        visible.value = false;
      }
    } finally {
      isLoading.value = false;
    }
  }

  function handleNegativeClick() {
    visible.value = false;
  }
</script>
