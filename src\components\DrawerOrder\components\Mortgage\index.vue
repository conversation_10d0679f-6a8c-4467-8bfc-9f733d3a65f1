<template>
  <n-space vertical>
    <n-card>
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="GPS、保险、抵押审批" :status="2" />
          </template>

          <div>
            <n-form
              ref="formRef"
              label-placement="left"
              size="medium"
              :model="formModel"
              :rules="formRules"
              label-width="120px"
            >
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="附件资料*" path="appendix">
                    <CombineSelect v-model:value="selectValue" :options="selectOptions" />
                    <UploadFile
                      v-if="selectValue === 'mortgageCertificate'"
                      v-model:fileList="formModel.appendix.mortgageCertificate"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="5"
                      multiple
                      :max-count="mortgageCertificateNumber"
                    />
                    <template v-for="item in selectOptions" :key="item.value">
                      <UploadFile
                        v-if="selectValue === item.value && item.value !== 'mortgageCertificate'"
                        v-model:fileList="formModel.appendix[item.value]"
                        accept=".jpg,.jpeg,.png,.pdf"
                      />
                    </template>
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <!-- <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="姓名" path="name">
                    <n-input v-model:value="formModel.name" placeholder="请输入姓名" />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="手机号" path="phone">
                    <n-input
                      v-model:value="formModel.phone"
                      placeholder="请输入手机号"
                      maxlength="11"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="身份证号" path="idCard">
                    <n-input
                      v-model:value="formModel.idCard"
                      placeholder="请输入身份证号"
                      maxlength="18"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid> -->
              <n-flex justify="center">
                <n-button type="info" size="large" @click="handleSubmit"> 确认提交 </n-button>
              </n-flex>
            </n-form>
            <n-divider dashed />

            <SubTitle title="发起进件" desc="前置要求：姓名、手机号、身份证号完整" />

            <n-data-table :columns="columns" :data="data" />
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <n-card>
      <!-- 授权书签署 -->
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="授权书签署" :status="2" />
          </template>

          <SubTitle title="授权书签署" />
          <n-data-table :columns="columns2" :data="data2" />
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import CombineSelect from '@/components/DrawerOrder/components/CombineSelect/index.vue';
  import { ref, reactive, computed } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';

  const formRef = ref<FormInst | null>(null);
  const formModel = reactive({
    appendix: {
      mortgageCertificate: [], //抵押信息登记证
      commercialInsurancePolicy: '', //商业保单
      vehicleStatus: '', //机动车状态（12123截图)
      policeMail: '', //警邮回执
    },
  });
  const mortgageCertificateNumber = 2; //附件资料-抵押信息登记证上传附件数量
  const formRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    ],
    idCard: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      {
        pattern:
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入正确的身份证号',
        trigger: 'blur',
      },
    ],
    appendix: [
      {
        validator: (_rule, value) => {
          if (value?.mortgageCertificate?.length !== mortgageCertificateNumber) {
            return new Error('请完善抵押信息登记证');
          }
          if (!value?.commercialInsurancePolicy) {
            return new Error('请完善商业保单');
          }
          if (!value?.vehicleStatus) {
            return new Error('请完善机动车状态（12123截图）');
          }
          if (!value?.policeMail) {
            return new Error('请完善警邮回执');
          }
          return true;
        },
        trigger: 'change',
      },
    ],
    portraitImage: [{ required: true, message: '请上传身份证人像面', trigger: 'change' }],
  };
  const selectValue = ref('');
  const selectOptions = computed(() => [
    {
      value: 'mortgageCertificate',
      label: `抵押信息登记证(${mortgageCertificateNumber})*`,
      finish: formModel.appendix.mortgageCertificate?.length === mortgageCertificateNumber,
    },
    {
      value: 'commercialInsurancePolicy',
      label: `商业保单*`,
      finish: !!formModel.appendix.commercialInsurancePolicy,
    },
    {
      value: 'vehicleStatus',
      label: `机动车状态（12123截图）* `,
      finish: !!formModel.appendix.vehicleStatus,
    },
    {
      value: 'policeMail',
      label: `警邮回执*`,
      finish: !!formModel.appendix.policeMail,
    },
  ]);
  const columns = [
    {
      title: '进件状态',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];
  const data = [
    {
      key: '1',
      status: '待审核',
    },
  ];

  const handleSubmit = async () => {
    await formRef.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交表单:', formModel);
      window.$message?.success('提交成功');
    });
  };

  const formRef2 = ref<FormInst | null>(null);
  const formModel2 = reactive({
    area: '',
  });
  const columns2 = [
    {
      title: '授权书签署地址',
      key: 'address',
      align: 'center',
    },
    {
      title: '签署状态',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];
  const data2 = [];
</script>

<style lang="less" scoped></style>
