export async function copyCanvasToClipboard(canvas: HTMLCanvasElement) {
  if (!('clipboard' in navigator) || typeof ClipboardItem === 'undefined') {
    throw new Error('当前浏览器不支持写入图片到剪贴板');
  }
  const blob: Blob = await new Promise((resolve, reject) =>
    canvas.toBlob((b) => (b ? resolve(b) : reject(new Error('toBlob 失败'))), 'image/png')
  );
  const item = new ClipboardItem({ 'image/png': blob });
  await navigator.clipboard.write([item]);
}
