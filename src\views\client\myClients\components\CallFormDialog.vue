<template>
  <n-form class="card-form" ref="formRef" :model="formModel" :rules="rules">
    <div class="title-tips text-center"
      >请选择工作机联系使用的卡槽，选择错误/无效的卡槽可能导致拨打失败</div
    >
    <n-form-item path="cardSlotNum">
      <n-radio-group v-model:value="formModel.cardSlotNum" style="width: 100%">
        <n-space justify="center" :size="40">
          <n-radio-button
            size="large"
            v-for="option in options"
            :key="option.value"
            :value="option.value"
          >
            <div class="flex items-center py-[8px] px-[40px]">
              <n-icon size="18">
                <CardSharp />
              </n-icon>
              <span class="ml-[5px]">{{ option.label }}</span>
            </div>
          </n-radio-button>
        </n-space>
      </n-radio-group>
    </n-form-item>
  </n-form>
</template>
<script lang="ts" setup>
  import { CardSharp } from '@vicons/ionicons5';
  import { ref, defineExpose, defineModel } from 'vue';
  import { type FormRules } from 'naive-ui';
  const options = [
    {
      value: '1',
      label: '卡槽1',
    },
    {
      value: '2',
      label: '卡槽2',
    },
  ];
  interface FormModelType {
    cardSlotNum: string;
  }
  const formModel = defineModel<FormModelType>('form', { default: { cardSlotNum: '' } });
  const formRef = ref();
  const rules = ref<FormRules>({
    cardSlotNum: [
      {
        required: true,
        message: '请选择卡槽',
        trigger: 'change',
      },
    ],
  });
  defineExpose({
    formValid,
  });
  async function formValid() {
    return formRef.value?.validate();
  }
</script>
<style lang="less">
  ::v-deep(
      .card-form
        .n-form-item
        .n-form-item-feedback-wrapper
        .n-form-item-feedback.n-form-item-feedback--error
    ) {
    text-align: center;
  }
</style>
