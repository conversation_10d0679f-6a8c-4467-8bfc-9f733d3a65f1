export function createEnumOptions<T extends string | number>(
  map: Record<T, string>,
  order?: T[]
): { label: string; value: T }[] {
  const entries = order
    ? order.map((key) => [key, map[key]] as [T, string])
    : Object.entries(map).map(([key, label]) => {
        const value = !isNaN(Number(key)) ? (Number(key) as T) : (key as T);
        return [value, label] as [T, string];
      });

  return entries.map(([value, label]) => ({ label, value }));
}
