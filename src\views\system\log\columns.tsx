import { BasicColumn } from '@/components/Table';
import dayjs from 'dayjs';

export interface ListData {
  createTime: number;
  userNane: string;
  operation: string;
  content: string;
}

export const columns: BasicColumn<ListData>[] = [
  {
    title: '操作时间',
    key: 'createTime',
    width: 180,
    align: 'center',
    render: (record: ListData) => {
      return (
        <span>
          {record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '操作人',
    key: 'userNane',
    align: 'center',
    width: 100,
  },
  {
    title: '操作类型',
    key: 'operation',
    align: 'center',
  },
  {
    title: '操作内容',
    key: 'content',
    align: 'center',
  },
];
