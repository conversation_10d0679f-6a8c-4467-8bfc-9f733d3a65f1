<template>
  <n-card class="role" :bordered="false" :content-style="{ padding: 0 }">
    <div class="page-title">角色权限</div>
    <div class="page-container">
      <div class="user-left">
        <div class="user-col">
          <div class="t-label">角色列表</div>
          <n-button
            v-permission="{ action: 'add' }"
            class="add-btn"
            quaternary
            type="primary"
            @click="addRole"
            >新增</n-button
          >
        </div>
        <div
          v-for="item in roleList"
          :key="item.id"
          class="user-col cursor-pointer"
          :class="{ active: curSelectRole?.id === item.id }"
          @click="curSelectRole = item"
        >
          <div class="user-name">{{ item.name }}</div>
          <div v-if="item.code != 'SUPER_ROLE'" class="right-btns">
            <n-button
              class="mr-5px"
              v-permission="{ action: 'edit' }"
              type="primary"
              ghost
              size="tiny"
              @click.stop="editRole(item.id, item)"
              >编辑</n-button
            >
            <n-button
              class="mr-5px"
              v-permission="{ action: 'status' }"
              type="primary"
              ghost
              size="tiny"
              @click.stop="changeUseStatus(item)"
              >{{ item.status === 0 ? '禁用' : '启用' }}</n-button
            >
            <n-button
              class="mr-5px"
              v-permission="{ action: 'remove' }"
              type="primary"
              ghost
              size="tiny"
              @click.stop="deleteRole(item)"
              >删除</n-button
            >
          </div>
        </div>
      </div>
      <div class="user-right" v-loading="loading">
        <div class="t-label flex justify-between pblr-10px bb-1px">
          <span>权限设置</span>
          <n-button v-permission="{ action: 'save' }" size="small" type="primary" @click="roleSure"
            >保存</n-button
          >
        </div>
        <n-tabs
          class="card-tabs"
          default-value="menu"
          size="large"
          animated
          v-model:value="tabValue"
          pane-wrapper-style="border:#ddd solid 1px;margin:10px 0;min-height: 450px;"
          :tab-style="{
            fontSize: '14px',
          }"
        >
          <n-tab-pane
            class="tab-container"
            name="menu"
            tab="菜单权限"
            display-directive="show:lazy"
          >
            <div class="tab-title">菜单查看&操作权限</div>
            <div class="tab-content">
              <RolePermission
                v-if="curSelectRole && curSelectRole.id"
                ref="rolePermissionRef"
                :role-id="curSelectRole.id"
                :tabValue="tabValue"
              />
            </div>
          </n-tab-pane>
          <n-tab-pane
            class="tab-container"
            name="clue-data"
            tab="线索数据权限"
            display-directive="show:lazy"
          >
            <div class="tab-title">线索数据查看权限</div>
            <div class="tab-content">
              <CluePermission
                v-if="curSelectRole && curSelectRole.id"
                ref="cluePermissionRef"
                :role-id="curSelectRole.id"
                :roleDetail="roleDetail"
              />
            </div>
          </n-tab-pane>
          <n-tab-pane
            class="tab-container"
            name="clue-claim"
            tab="线索认领权限"
            display-directive="show:lazy"
          >
            <div class="tab-title">线索认领权限</div>
            <div class="tab-content">
              <ClueClaim
                v-if="curSelectRole && curSelectRole.id"
                ref="clueClaimRef"
                :role-id="curSelectRole.id"
              />
            </div>
          </n-tab-pane>
          <n-tab-pane
            class="tab-container"
            name="dialing-permission"
            tab="工作机拨打权限"
            display-directive="show:lazy"
          >
            <div class="tab-title">工作机拨打权限</div>
            <div class="tab-content">
              <DialingPermission
                v-if="curSelectRole && curSelectRole.id"
                ref="dialingPermissionRef"
                :role-id="curSelectRole.id"
                :roleDetail="roleDetail"
              />
            </div>
          </n-tab-pane>

          <n-tab-pane
            class="tab-container"
            name="other-permission"
            tab="其他权限"
            display-directive="show:lazy"
          >
            <div class="tab-title">其他权限</div>
            <div class="tab-content">
              <OtherPermission
                v-if="curSelectRole && curSelectRole.id"
                ref="otherPermissionRef"
                :role-id="curSelectRole.id"
                :roleDetail="roleDetail"
              />
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
    </div>
    <FormDialog
      v-model="dialogFormVisible"
      :title="dialogOps.title"
      :formModel="formVal"
      @submit="submit"
      :rules="addRules"
      @close="dialogFormVisible = false"
    >
      <n-form-item label="角色名称" path="name">
        <n-input
          :allow-input="noSideSpace"
          v-model:value="formVal.name"
          maxlength="25"
          type="text"
        />
      </n-form-item>
      <n-form-item label="角色CODE" path="code">
        <n-input :allow-input="noSideSpace" v-model:value="formVal.code" type="text" />
      </n-form-item>
    </FormDialog>
  </n-card>
</template>

<script lang="ts" setup>
  import {
    get_admin_role_list,
    updateRoleStatus,
    del_role,
    add_role,
    update_role,
  } from '@/api/system/role';
  import { computed, onMounted, ref } from 'vue';
  import { useUser } from '@/store/modules/user';
  import { useMessage, useDialog } from 'naive-ui';
  import FormDialog from '@/components/FormDialog/index.vue';
  import { noSideSpace } from '@/hooks/formValid/useFormValid';
  import RolePermission from './components/RolePermission.vue';
  import CluePermission from './components/CluePermission.vue';
  import ClueClaim from './components/ClueClaimForm.vue';
  import DialingPermission from './components/DialingPermission.vue';
  import OtherPermission from './components/OtherPermission.vue';
  interface IRoleItem {
    id: number;
    name: string;
    code: string;
    status: number;
    menus: number[];
    permissions: number[];
    buttons: string[];
  }
  interface IFormItem {
    name: string;
    code: string;
  }

  defineOptions({
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'system_role',
  });

  const type = ref('add');
  const dialogOps = ref({
    title: '新增角色',
  });
  const userStore = useUser();
  const defaultForm = {
    name: '',
    code: '',
  };
  const dialogFormVisible = ref(false);
  const roleList = ref<IRoleItem[]>([]);
  const curSelectRole = ref<IRoleItem | null>(null);
  const roleId = ref();
  const dialog = useDialog();
  const message = useMessage();
  const rolePermissionRef = ref();
  const cluePermissionRef = ref();
  const formVal = ref<IFormItem>({ ...defaultForm });
  const addRules = ref({
    name: [{ required: true, message: '此项不能为空', trigger: ['blur', 'change'] }],
    code: [{ required: true, message: '此项不能为空', trigger: ['blur', 'change'] }],
  });
  const tabValue = ref('menu');
  const clueClaimRef = ref();
  const dialingPermissionRef = ref();
  const otherPermissionRef = ref();
  const loading = ref(false);
  onMounted(() => {
    getRoleList();
  });
  const roleDetail = computed(() => {
    return rolePermissionRef.value?.roleDetail ?? {};
  });
  function addRole() {
    type.value = 'add';
    dialogOps.value.title = '新增角色';
    dialogFormVisible.value = true;
    formVal.value = { ...defaultForm };
  }
  function getRoleList() {
    return get_admin_role_list().then((res) => {
      if (res.code == 200 && Array.isArray(res.data)) {
        roleList.value = res.data;
        let index = roleList.value.findIndex((item) => item.id === curSelectRole.value?.id);
        if (index === -1) {
          curSelectRole.value = roleList.value?.[0] ?? {};
        }
      }
      return res;
    });
  }
  function submit(formVal, formDone) {
    let api = type.value === 'add' ? add_role : update_role;
    api(formVal)
      .then((res) => {
        if (res.code == 200) {
          message.success('操作成功');
          dialogFormVisible.value = false;
          success();
        }
      })
      .finally(() => {
        formDone?.();
      });
  }
  function success() {
    getRoleList();
    userStore.getInfo();
  }
  function editRole(id, item) {
    roleId.value = id;
    type.value = 'edit';
    dialogOps.value.title = '编辑角色';
    dialogFormVisible.value = true;
    formVal.value = { ...item };
  }
  function changeUseStatus(item) {
    // 处理禁用启用状态
    dialog.warning({
      title: '操作提示',
      content: `确定要${item.status === 0 ? '禁用' : '启用'}此角色？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        updateRoleStatus(item.id).then((res) => {
          if (res.code == 200) {
            message.success('操作成功');
            success();
          }
        });
      },
    });
  }
  function deleteRole(item) {
    dialog.warning({
      title: '操作提示',
      content: `确定删除该角色？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        del_role(item.id).then((res) => {
          if (res.code == 200) {
            message.success('操作成功');
            success();
          }
        });
      },
    });
  }
  function roleSure() {
    const showLoading = () => {
      loading.value = true;
      setTimeout(() => {
        loading.value = false;
      }, 1000);
    };
    if (['clue-data', 'menu'].includes(tabValue.value)) {
      let postData = cluePermissionRef?.value?.sure?.();
      rolePermissionRef?.value?.sure?.(postData).then(() => {
        showLoading();
      });
      return;
    }

    if (tabValue.value === 'dialing-permission') {
      dialingPermissionRef?.value?.sure?.().then(() => {
        showLoading();
      });
    }

    if (tabValue.value === 'clue-claim') {
      clueClaimRef?.value?.sure?.().then(() => {
        showLoading();
      });
    }

    if (tabValue.value === 'other-permission') {
      otherPermissionRef?.value?.sure?.().then(() => {
        showLoading();
      });
    }
  }
</script>

<style lang="less" scoped>
  .role {
    // background-color: #fff;
    padding: 10px 10px;
    .mr-5px {
      margin-right: 5px;
    }
    .page-title {
      font-size: 14px;
      margin-bottom: 10px;
    }
    .page-container {
      display: flex;
      border: 1px solid #dfdfdf;
    }
    .flex {
      display: flex;
      align-items: center;
      &.justify-between {
        justify-content: space-between;
      }
    }
    .t-label {
      font-size: 14px;
      margin-bottom: 10px;
      &.pblr-10px {
        padding: 0 10px 10px;
      }
      &.bb-1px {
        border-bottom: 1px solid #ddd;
      }
    }
    .user-left {
      max-width: 400px;
      min-width: 350px;
      flex-shrink: 0;
    }
    .user-right,
    .user-left {
      height: 78vh;
      overflow-y: auto;
      overflow-x: hidden;
    }
    .user-right {
      flex-grow: 1;
      border-left: 1px solid #dfdfdf;
      padding: 10px 0;
    }
    .user-col {
      min-height: 46px;
      margin: 0 5px;
      display: flex;
      padding: 0 5px;
      justify-content: space-between;
      align-items: center;
      &.active {
        background-color: rgba(64, 158, 255, 0.2);
      }
    }
    .add-btn {
      // color: #409eff;
    }
    .right-btns {
      display: flex;
      .n-button {
        padding: 5px 5px;
      }
    }
    .cursor-pointer {
      cursor: pointer;
    }
    .card-tabs {
      padding: 15px;
      font-size: 14px;
      min-height: 90%;
      .n-tabs .n-tabs-tab {
        font-size: 14px;
      }
      .n-tabs-pane-wrapper {
        min-height: 80%;
      }
    }
    .tab-container {
      &.n-tab-pane {
        padding: 0 0;
      }
    }
    .tab-title {
      padding: 15px;
      border-bottom: #ddd solid 1px;
    }
    .tab-content {
      padding: 15px;
      min-height: 80%;
    }
  }
</style>
