import { BasicColumn } from '@/components/Table';
import { FormStatusMap } from '@/enums';

export interface ListData {
  id: number;
  paramName: string;
  paramDesc: string;
  paramValue: string;
  status: number;
}

export const columns: BasicColumn<ListData>[] = [
  {
    title: '参数ID',
    key: 'id',
    width: 80,
    align: 'center',
  },
  {
    title: '参数名称',
    key: 'paramName',
    align: 'center',
  },
  {
    title: '参数说明',
    key: 'paramDesc',
    align: 'center',
  },
  {
    title: '类型配置',
    key: 'paramValue',
    align: 'center',
  },
  {
    title: '状态',
    key: 'status',
    align: 'center',
    render: (record: ListData) => {
      return <div>{FormStatusMap[record.status]}</div>;
    },
  },
];
