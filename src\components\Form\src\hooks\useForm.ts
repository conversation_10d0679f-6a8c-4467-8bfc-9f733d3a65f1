import { FormProps, FormActionType, UseFormReturnType } from '../types/form';
import type { DynamicProps } from '/#/utils';

import { ref, onUnmounted, unref, nextTick, watch } from 'vue';
import { isProdMode } from '@/utils/env';
import { getDynamicProps } from '@/utils';
import { isArray, isFunction, isNullOrUnDef, isString } from '@/utils/is';
import { set } from 'lodash-es';

type Props = Partial<DynamicProps<FormProps>>;

export function useForm(props?: Props): UseFormReturnType {
  const formRef = ref<Nullable<FormActionType>>(null);
  const loadedRef = ref<Nullable<boolean>>(false);

  async function getForm() {
    const form = unref(formRef);
    if (!form) {
      console.error(
        'The form instance has not been obtained, please make sure that the form has been rendered when performing the form operation!'
      );
    }
    await nextTick();
    return form as FormActionType;
  }

  function register(instance: FormActionType) {
    isProdMode() &&
      onUnmounted(() => {
        formRef.value = null;
        loadedRef.value = null;
      });
    if (unref(loadedRef) && isProdMode() && instance === unref(formRef)) return;

    formRef.value = instance;
    loadedRef.value = true;

    watch(
      () => props,
      () => {
        props && instance.setProps(getDynamicProps(props));
      },
      {
        immediate: true,
        deep: true,
      }
    );
  }

  const methods: FormActionType = {
    getSchema() {
      return unref(formRef)!.getSchema();
    },
    getActiveColumns(): string[] {
      return unref(formRef)!.getActiveColumns();
    },
    setProps: async (formProps: Partial<FormProps>) => {
      const form = await getForm();
      await form.setProps(formProps);
    },

    resetFields: async () => {
      getForm().then(async (form) => {
        await form.resetFields();
      });
    },

    clearValidate: async (name?: string | string[]) => {
      const form = await getForm();
      await form.clearValidate(name);
    },

    getFieldsValue: <T>() => {
      const formRefInstance = unref(formRef)!;
      // 激活列
      const activeColumns = formRefInstance?.getActiveColumns();
      // 提交form
      const submitForm = formRefInstance?.getFieldsValue();
      // 表单配置项
      const schemas = formRefInstance?.getSchema();
      // 提交form过滤隐藏列
      const res = {};
      for (const item of Object.entries(submitForm)) {
        let [, value] = item;
        const [key] = item;
        if (
          !key ||
          (isArray(value) && value.length === 0) ||
          isFunction(value) ||
          isNullOrUnDef(value) ||
          !activeColumns.includes(key)
        ) {
          continue;
        }
        // 删除空格
        if (isString(value)) {
          value = value.trim();
        }
        set(res, key, value);
      }
      // 处理返回字段，有childKey则取childKey内值
      for (const key of Object.keys(res)) {
        const curSchema = schemas.find((item) => item.field === key);
        if (curSchema?.childKey) {
          res[curSchema.childKey[0]] = res[key][0];
          res[curSchema.childKey[1]] = res[key][1];
          delete res[key];
        }
      }
      return res as T;
    },

    // 新增：获取响应式 formModel
    getFormModel: <T>() => {
      const formRefInstance = unref(formRef)!;
      return formRefInstance?.getFormModel() || ({} as T);
    },

    setFieldsValue: async <T extends Record<string, any>>(values: Partial<T>) => {
      const form = await getForm();
      await form.setFieldsValue(values);
    },

    submit: async (): Promise<any> => {
      const form = await getForm();
      return form.submit();
    },

    validate: async (nameList?: any[]): Promise<Recordable> => {
      const form = await getForm();
      return form.validate(nameList);
    },

    setLoading: (value: boolean) => {
      loadedRef.value = value;
    },

    setSchema: async (values) => {
      const form = await getForm();
      form.setSchema(values);
    },
  };

  return [register, methods];
}
