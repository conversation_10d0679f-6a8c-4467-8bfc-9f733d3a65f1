<template>
  <div class="todo-dialog">
    <n-dialog
      type="info"
      title="消息提醒"
      negative-text="确认"
      positive-text="去处理"
      @positive-click="$emit('positive-click')"
      @negative-click="$emit('negative-click')"
      @close="$emit('close')"
    >
      <div class="todo-dialog-content">
        <div class="title">
          {{ title }}
        </div>
        <div class="subtitle">
          {{ subtitle }}
        </div>
        <div class="time">
          {{ dayjs(time).format('YYYY-MM-DD HH:mm:ss') }}
        </div>
      </div>
    </n-dialog>
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';

  defineProps<{
    title: string;
    subtitle: string;
    time: number;
  }>();

  defineEmits<{
    (e: 'positive-click'): void;
    (e: 'negative-click'): void;
    (e: 'close'): void;
  }>();
</script>

<style lang="less" scoped>
  .todo-dialog {
    position: fixed;
    right: 20px;
    bottom: 20px;
    z-index: 999;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

    .todo-dialog-content {
      display: flex;
      flex-direction: column;
      width: 350px;

      .title {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 8px;
      }
      .subtitle {
        font-size: 14px;
        margin-bottom: 8px;
      }
      .time {
        font-size: 12px;
        text-align: right;
      }
    }
  }
</style>
