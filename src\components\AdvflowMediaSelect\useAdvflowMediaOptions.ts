import { ref, computed } from 'vue';
import { getAdvflowMediaAllApi } from '@/api/client';

// 全局状态
const advflowMediaOptions = ref<any[]>([]);
const loading = ref(false);
const loaded = ref(false);

async function loadOptions() {
  if (loading.value || loaded.value) return;

  loading.value = true;
  try {
    const { data } = await getAdvflowMediaAllApi();
    advflowMediaOptions.value = data;
    loaded.value = true;
  } catch (error) {
    console.error('加载投放平台列表失败:', error);
  } finally {
    loading.value = false;
  }
}

export function useAdvflowMediaOptions() {
  return {
    advflowMediaOptions: computed(() => advflowMediaOptions.value),
    loading: computed(() => loading.value),
    loaded: computed(() => loaded.value),
    loadOptions,
  };
}
