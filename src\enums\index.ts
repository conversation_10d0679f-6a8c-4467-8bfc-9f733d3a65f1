import { createEnumOptions } from '@/utils/createEnumOptions';

// 表单状态枚举
export enum FormStatus {
  // 启用
  Enable = 1,
  // 禁用
  Disable = 0,
}
// 表单状态映射
export const FormStatusMap = {
  [FormStatus.Enable]: '启用',
  [FormStatus.Disable]: '禁用',
};
// 表单状态list
export const FormStatusList = createEnumOptions(FormStatusMap, [
  FormStatus.Enable,
  FormStatus.Disable,
]);

// 性别枚举
export enum Sex {
  // 男
  Male = 1,
  // 女
  Female = 0,
}
export const SexMap = {
  [Sex.Male]: '男',
  [Sex.Female]: '女',
};
export const SexList = createEnumOptions(SexMap, [Sex.Male, Sex.Female]);

// 性别(先生、女士)枚举
export enum SexTitle {
  // 先生
  Male = 1,
  // 女士
  Female = 0,
}
export const SexTitleMap = {
  [SexTitle.Male]: '先生',
  [SexTitle.Female]: '女士',
};
export const SexTitleList = createEnumOptions(SexTitleMap, [SexTitle.Male, SexTitle.Female]);

// 人员状态枚举
export enum UserStatus {
  // 正常
  Normal = 0,
  // 锁定
  Disable = 1,
}
export const UserStatusMap = {
  [UserStatus.Normal]: '正常',
  [UserStatus.Disable]: '锁定',
};
export const UserStatusList = createEnumOptions(UserStatusMap, [
  UserStatus.Normal,
  UserStatus.Disable,
]);

// 人员在职状态 在职 ACTIVE 离职 INACTIVE
export enum JobStatus {
  // 在职
  OnJob = 'ACTIVE',
  // 离职
  OffJob = 'INACTIVE',
}
export const JobStatusMap = {
  [JobStatus.OnJob]: '在职',
  [JobStatus.OffJob]: '离职',
};
export const JobStatusList = createEnumOptions(JobStatusMap, [JobStatus.OnJob, JobStatus.OffJob]);
