import type { FormRules } from 'naive-ui';

const rules: FormRules = {
  paramName: [
    {
      required: true,
      message: '请输入参数名称',
      trigger: 'blur',
    },
  ],
  paramDesc: [
    {
      required: true,
      message: '请输入参数说明',
      trigger: 'blur',
    },
  ],
  paramValue: [
    {
      required: true,
      message: '请输入类型配置',
      trigger: 'blur',
    },
  ],
  status: [
    {
      type: 'number',
      required: true,
      message: '请选择状态',
      trigger: 'change',
    },
  ],
};

export default rules;
