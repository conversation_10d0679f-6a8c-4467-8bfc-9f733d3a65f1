<template>
  <n-modal v-model:show="show" preset="card" style="width: 800px" title="视频预览">
    <video v-if="show" :src="videoUrl" controls autoplay class="preview-video"></video>
  </n-modal>
</template>

<script lang="ts" setup>
  import { defineModel, defineProps } from 'vue';
  import { NModal } from 'naive-ui';

  const show = defineModel('show', { type: Boolean, default: false });
  defineProps({
    videoUrl: {
      type: String,
      required: true,
    },
  });
</script>

<style scoped>
  .preview-video {
    width: 100%;
    max-height: 80vh;
    object-fit: contain;
  }
</style>
