<template>
  <FormDialog
    v-model="formDialogModel"
    title="线索认领"
    :form-model="paramForm"
    :rules="rules"
    style="width: 20%; min-width: 300px"
    @submit="handleSubmit"
    @close="handleClose"
    @open="handleOpen"
  >
    <div class="mb-[10px] text-center text-[16px]"> 请输入你要认领的线索数量 </div>
    <n-form-item label="数量：" path="receiveNum">
      <n-input-number
        v-model:value="paramForm.receiveNum"
        placeholder="请输入认领数量"
        :min="0"
        :max="paramForm.maxReceiveNum"
        clearable
        style="width: 100%"
      />
    </n-form-item>
    <div class="flex items-center justify-between mb-[20px]">
      <div>今日最大可认领线索数</div>
      <div class="flex gap-[5px] items-center"
        ><n-input-number
          :show-button="false"
          v-model:value="paramForm.dayLimit"
          disabled
          size="small"
          class="w-[50px]"
        />(条)</div
      >
    </div>
    <div class="flex items-center justify-between mb-[20px]">
      <div>本次最大可认领线索数</div>
      <div class="flex gap-[5px] items-center"
        ><n-input-number
          :show-button="false"
          v-model:value="paramForm.maxReceiveNum"
          disabled
          size="small"
          class="w-[50px]"
        />(条)</div
      >
    </div>
  </FormDialog>
</template>

<script lang="tsx" setup>
  import FormDialog from '@/components/FormDialog/index.vue';
  import { reactive, ref } from 'vue';
  import { useDialog } from 'naive-ui';
  import { getUserClueLimitApi, claimCustomerApi } from '@/api/client';
  import { useRouter } from 'vue-router';
  import { FollowStatus } from '@/views/client/enum';
  import { EventNames, bufferedEmitter } from '@/utils/eventBus';

  const router = useRouter();
  const dialog = useDialog();
  const rules = {
    receiveNum: [
      {
        required: true,
        validator: (_, value) => {
          if (!value) {
            return new Error('请输入正确的认领数量');
          }
          return true;
        },
        trigger: 'blur',
      },
    ],
  };
  const paramFormState = {
    receiveNum: 0,
    // 单日可认领线索数量
    dayLimit: 0,
    // 单次可认领线索数量
    maxReceiveNum: 0,
  };
  const paramForm = reactive({ ...paramFormState });
  const formDialogModel = ref(false);

  const emits = defineEmits(['close']);
  const handleSubmit = async (params, done) => {
    try {
      const { data } = await claimCustomerApi(params.receiveNum);
      window.$message.success('认领成功');
      createSuccessTip(data);
      formDialogModel.value = false;
    } finally {
      done();
    }
  };
  const handleClose = () => {
    Object.assign(paramForm, paramFormState);
    emits('close');
  };
  const getUserClueLimit = async () => {
    try {
      const { data } = await getUserClueLimitApi();

      paramForm.dayLimit = data.dayLimit;
      paramForm.maxReceiveNum = data.maxReceiveNum;
    } catch (err) {
      console.log(err);
    }
  };
  const handleOpen = () => {
    getUserClueLimit();
  };
  // 创建提示弹窗
  const createSuccessTip = (data = 0) => {
    dialog.success({
      title: '线索已认领完成',
      showIcon: false,
      content: () => {
        return (
          <>
            <div class="flex items-center justify-center mb-[20px] mt-[10px]">
              <div>请求认领线索数量：</div>
              <div class="flex gap-[5px] items-center">
                <n-input-number
                  show-button={false}
                  default-value={paramForm.receiveNum}
                  disabled
                  size="small"
                  class="w-[50px]"
                />
                (条)
              </div>
            </div>
            <div class="flex items-center justify-center mb-[20px]">
              <div>认领成功线索数量：</div>
              <div class="flex gap-[5px] items-center">
                <n-input-number
                  show-button={false}
                  default-value={data}
                  disabled
                  size="small"
                  class="w-[50px]"
                />
                (条)
              </div>
            </div>
            <div class="text-[red] my-[20px] text-center">请及时前往工作台跟进线索！</div>
          </>
        );
      },
      positiveText: '立即前往',
      negativeText: '稍后跟进',
      onPositiveClick: () => {
        bufferedEmitter.emit(EventNames.JUMP_WORKBENCH_TAB, {
          followStatus: FollowStatus.WaitFollow,
        });
        router.push('/dashboard/workplace');
      },
    });
  };

  defineExpose({
    formDialogModel,
  });
</script>
