<template>
  <n-card :bordered="false">
    <BasicForm
      :enable-cache="true"
      @register="register"
      @submit="reloadTable"
      @reset="reloadTable()"
    />
  </n-card>
  <n-card :bordered="false" class="mt-3">
    <BasicTable
      :columns="tableColumns"
      :request="loadDataTable"
      :row-key="(row:ListData) => row.id"
      :actionColumn="actionColumn"
      ref="action"
      :striped="true"
      :row-props="rowProps"
      :checked-row-keys="checkedRowKeys"
      :scroll-x="0"
      @update:checked-row-keys="handleCheck"
    >
      <template #tableTitle>
        <n-space :wrap-item="false" align="center">
          <n-button
            v-permission="{ action: 'message_detail_confirm' }"
            type="primary"
            @click="onBatchConfirm"
          >
            批量确认
          </n-button>
          <n-button
            v-permission="{ action: 'message_detail_delete' }"
            type="primary"
            @click="onBatchDelete"
          >
            批量删除
          </n-button>
        </n-space>
      </template>
    </BasicTable>
  </n-card>
</template>

<script setup lang="tsx">
  import { columns, ListData } from './columns';
  import { BasicTable } from '@/components/Table';
  import { BasicForm, FormSchema, useForm } from '@/components/Form';
  import {
    reactive,
    onMounted,
    computed,
    onUnmounted,
    onActivated,
    useTemplateRef,
    ref,
  } from 'vue';
  import { getUserMessage, getMessageTypes, batchUserMessage } from '@/api/system/message';
  import dayjs from 'dayjs';
  import { createMonthRangeDisabledFn } from '@/utils/datePickerDisabled';
  import emitter, { EventNames, bufferedEmitter } from '@/utils/eventBus';
  import { JumpWorkbench, MessageStatusOptions } from './enum';
  import { promisifyDialog } from '@/utils/nativeUtils';
  import { cloneDeep } from 'lodash-es';
  import { useRouter } from 'vue-router';

  defineOptions({
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'message_detail',
  });
  const checkedRowKeys = ref<number[]>([]);
  const actionRef = useTemplateRef<InstanceType<typeof BasicTable>>('action');

  const messageTypeOptions = ref<{ label: string; value: string }[]>([]);
  const router = useRouter();

  const schemas = computed<FormSchema[]>(() => {
    return [
      {
        field: 'createTimeQuery',
        label: '消息时间',
        component: 'NDatePicker',
        childKey: ['startTime', 'endTime'],
        defaultValue: [
          dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ],
        componentProps: {
          type: 'daterange',
          'value-format': 'yyyy-MM-dd',
          format: 'yyyy-MM-dd',
          'is-date-disabled': createMonthRangeDisabledFn(3),
          clearable: false,
        },
        noHidden: true,
      },
      {
        field: 'messageTypeId',
        component: 'NSelect',
        label: '消息类型',
        componentProps: {
          placeholder: '请选择消息类型',
          options: messageTypeOptions.value,
        },
      },
      {
        field: 'isHandled',
        component: 'NSelect',
        label: '消息状态',
        componentProps: {
          placeholder: '请选择消息状态',
          options: MessageStatusOptions,
        },
      },
    ];
  });
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
  });

  const actionColumn = reactive({
    width: 200,
    title: '操作',
    key: 'action',
    fixed: 'right',
    align: 'center',
    render(row: ListData) {
      return (
        <n-space>
          <n-button
            type="primary"
            text
            disabled={row.isHandled === 1}
            onClick={async () => {
              await onHandleMsg(1, [row.id]);
            }}
          >
            确认
          </n-button>
          <n-button
            type="primary"
            text
            disabled={row.isHandled === 1}
            onClick={async () => {
              await onHandleMsg(1, [row.id]);

              goWorkplace(
                row.messageTypeId === 1 ? JumpWorkbench.OpenDetail : JumpWorkbench.ReceiveClue,
                row.entityId
              );
            }}
          >
            去处理
          </n-button>
          <n-button
            type="error"
            text
            onClick={async () => {
              await onHandleMsg(2, [row.id]);
            }}
          >
            删除
          </n-button>
        </n-space>
      );
    },
  });

  const tableColumns = computed(() => {
    const column = cloneDeep(columns);

    for (const item of column) {
      if (item.key === 'messageTypeId') {
        item.render = (row) => {
          return (
            messageTypeOptions.value.find((item) => item.value === row.messageTypeId)?.label || '-'
          );
        };
      }
    }

    return column;
  });

  const loadDataTable = async (params) => {
    const { data } = await getUserMessage({
      ...getFieldsValue(),
      ...params,
    });
    return data;
  };

  const reloadTable = () => {
    actionRef.value!.reload();
  };

  const rowProps = (row: ListData) => {
    return {
      style: {
        fontWeight: row.isHandled === 0 ? 'bold' : 'normal',
        color: row.isHandled === 0 ? 'black' : 'grey',
      },
    };
  };

  const handleCheck = (rowKeys: number[]) => {
    checkedRowKeys.value = rowKeys;
  };

  const onBatchConfirm = async () => {
    if (checkedRowKeys.value.length === 0) {
      window.$message.warning('请选择消息');
      return;
    }

    const result = await promisifyDialog(window.$dialog.warning)({
      title: '批量确认',
      content: '是否批量确认当前选择消息，确认后消息无高亮提醒',
      positiveText: '确定',
      negativeText: '取消',
      maskClosable: false,
    });

    if (result.source !== 'positive') {
      return;
    }

    await onHandleMsg(1, checkedRowKeys.value);

    checkedRowKeys.value = [];
  };
  const onBatchDelete = async () => {
    if (checkedRowKeys.value.length === 0) {
      window.$message.warning('请选择消息');
      return;
    }

    const result = await promisifyDialog(window.$dialog.warning)({
      title: '批量删除',
      content: '是否删除当前选择已确认的消息提醒',
      positiveText: '确定',
      negativeText: '取消',
      maskClosable: false,
    });

    if (result.source !== 'positive') {
      return;
    }

    await onHandleMsg(2, checkedRowKeys.value);

    checkedRowKeys.value = [];
  };

  const onHandleMsg = async (operationType: 1 | 2, messageIds: number[]) => {
    try {
      const result = await batchUserMessage({
        operationType,
        messageIds,
      });

      if (result.code !== 200) {
        throw new Error(result.msg || '操作失败');
      }

      window.$message.success('操作成功');
      reloadTable();
    } catch (error) {
      throw error;
    }
  };

  const goWorkplace = (type: JumpWorkbench, clueId?: number) => {
    bufferedEmitter.emit(EventNames.JUMP_WORKBENCH_AFTER, {
      fromMessage: type,
      clueId,
    });
    return router.push({
      path: `/dashboard/workplace`,
    });
  };

  onMounted(() => {
    onActivated(() => {
      reloadTable();
    });

    getMessageTypes().then((res) => {
      if (Array.isArray(res.data)) {
        messageTypeOptions.value = res.data.map((item) => ({
          label: item.typeName,
          value: item.id,
        }));
      }
    });

    emitter.on(EventNames.HANDLE_MESSAGE_SUCCESS, reloadTable);
    emitter.on(EventNames.ADD_MESSAGE_SUCCESS, reloadTable);
  });
  onUnmounted(() => {
    emitter.off('workCallFinish');
    emitter.off(EventNames.HANDLE_MESSAGE_SUCCESS);
    emitter.off(EventNames.ADD_MESSAGE_SUCCESS);
  });
</script>
