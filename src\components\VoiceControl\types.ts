// VoiceControl 组件类型定义

export interface VoiceControlProps {
  /** 音频文件URL */
  src?: string;
  /** 是否自动播放 */
  autoplay?: boolean;
  /** 是否循环播放 */
  loop?: boolean;
  /** 预加载策略 */
  preload?: 'none' | 'metadata' | 'auto';
  /** 是否启用播放位置保存功能 */
  enablePlaybackPosition?: boolean;
}

export interface VoiceControlEmits {
  /** 开始播放时触发 */
  play: [];
  /** 暂停播放时触发 */
  pause: [];
  /** 停止播放时触发 */
  stop: [];
  /** 播放结束时触发 */
  ended: [];
  /** 播放错误时触发 */
  error: [error: string];
  /** 播放进度更新时触发 */
  timeupdate: [currentTime: number, duration: number];
  /** 音量变化时触发 */
  volumechange: [volume: number];
  /** 播放速度变化时触发 */
  speedchange: [speed: number];
  /** 播放位置保存时触发 */
  playbackPositionSaved: [position: number];
  /** 播放位置清除时触发 */
  playbackPositionCleared: [];
}

export interface VoiceControlExpose {
  /** 开始播放 */
  play: () => Promise<void>;
  /** 暂停播放 */
  pause: () => void;
  /** 停止播放 */
  stop: () => void;
  /** 重新开始播放 */
  restart: () => void;
  /** 继续播放（从保存的位置） */
  continuePlay: () => Promise<void>;
  /** 设置音量 (0-1) */
  setVolume: (volume: number) => void;
  /** 设置播放速度 */
  setSpeed: (speed: number) => void;
  /** 跳转到指定时间 */
  seek: (time: number) => void;
  /** 保存播放位置 */
  savePlaybackPosition: () => void;
  /** 加载播放位置 */
  loadPlaybackPosition: () => void;
  /** 清除播放位置 */
  clearPlaybackPosition: () => void;
}

export interface SpeedOption {
  label: string;
  value: number;
}

export interface VoiceControlState {
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 当前播放时间 */
  currentTime: number;
  /** 总时长 */
  duration: number;
  /** 音量 */
  volume: number;
  /** 播放速度 */
  playbackRate: number;
  /** 错误信息 */
  errorMessage: string;
  /** 状态信息 */
  statusMessage: string;
  /** 保存的播放位置 */
  savedPlaybackPosition: number;
  /** 是否有保存的播放位置 */
  hasPlaybackPosition: boolean;
}

// 播放器事件类型
export interface AudioEventMap {
  loadstart: Event;
  durationchange: Event;
  loadedmetadata: Event;
  loadeddata: Event;
  progress: Event;
  canplay: Event;
  canplaythrough: Event;
  play: Event;
  playing: Event;
  pause: Event;
  timeupdate: Event;
  ended: Event;
  ratechange: Event;
  volumechange: Event;
  error: Event;
  abort: Event;
  suspend: Event;
  emptied: Event;
  stalled: Event;
  waiting: Event;
}

// 音频格式支持
export type SupportedAudioFormat =
  | 'audio/mp3'
  | 'audio/wav'
  | 'audio/ogg'
  | 'audio/aac'
  | 'audio/m4a'
  | 'audio/webm';

// 播放器配置
export interface VoiceControlConfig {
  /** 默认音量 */
  defaultVolume: number;
  /** 默认播放速度 */
  defaultSpeed: number;
  /** 播放速度选项 */
  speedOptions: SpeedOption[];
  /** 是否显示进度条 */
  showProgress: boolean;
  /** 是否显示音量控制 */
  showVolume: boolean;
  /** 是否显示速度控制 */
  showSpeed: boolean;
  /** 是否显示文件选择 */
  showFileInput: boolean;
  /** 是否显示状态信息 */
  showStatus: boolean;
  /** 是否启用播放位置保存 */
  enablePlaybackPosition: boolean;
}

// 播放位置信息
export interface PlaybackPosition {
  /** 文件标识 */
  fileId: string;
  /** 播放位置（秒） */
  position: number;
  /** 保存时间戳 */
  timestamp: number;
  /** 文件名称 */
  fileName?: string;
}

// 默认配置
export const defaultConfig: VoiceControlConfig = {
  defaultVolume: 1,
  defaultSpeed: 1,
  speedOptions: [
    { label: '0.5x', value: 0.5 },
    { label: '0.75x', value: 0.75 },
    { label: '1x', value: 1 },
    { label: '1.25x', value: 1.25 },
    { label: '1.5x', value: 1.5 },
    { label: '2x', value: 2 },
  ],
  showProgress: true,
  showVolume: true,
  showSpeed: true,
  showFileInput: true,
  showStatus: true,
  enablePlaybackPosition: true,
};
