<template>
  <FormDialog
    v-model="formDialogModel"
    title="新增客户"
    :form-model="paramForm"
    :rules="rules"
    @submit="handleSubmit"
    @close="handleClose"
  >
    <div class="form-title mb-[10px]"> 客户基本信息： </div>
    <n-form-item label="姓名：">
      <n-input v-model:value="paramForm.name" maxlength="10" placeholder="请输入姓名" clearable />
    </n-form-item>
    <n-form-item label="电话号码：" path="mobileNo">
      <n-input
        :value="paramForm.mobileNo"
        maxlength="11"
        clearable
        placeholder="请输入电话号码"
        @input="(val) => (paramForm.mobileNo = val.replace(/\D/g, ''))"
      />
    </n-form-item>
    <n-form-item label="性别：">
      <n-radio-group v-model:value="paramForm.sex">
        <n-space>
          <n-radio v-for="item in SexTitleList" :key="item.value" :value="item.value">
            {{ item.label }}
          </n-radio>
        </n-space>
      </n-radio-group>
    </n-form-item>
    <n-form-item label="车牌号：">
      <n-input
        v-model:value="paramForm.licensePlateNumber"
        maxlength="10"
        clearable
        placeholder="请输入车牌号"
      />
    </n-form-item>
    <n-form-item label="所在地区：">
      <CitySelect
        v-model="paramForm.area"
        change-on-select
        :multiple="false"
        @update="handleCityUpdate"
      />
    </n-form-item>
  </FormDialog>
</template>

<script lang="ts" setup>
  import FormDialog from '@/components/FormDialog/index.vue';
  import { reactive, ref } from 'vue';
  import { SexTitleList } from '@/enums';
  import rules from './rules';
  import CitySelect from '@/components/CitySelect/index.vue';
  import { addMyCustomerApi } from '@/api/client';

  const paramFormState = {
    name: '',
    mobileNo: '',
    sex: null,
    licensePlateNumber: '',
    provinceCode: '',
    provinceName: '',
    cityCode: '',
    cityName: '',
    area: null,
  };
  const paramForm = reactive({ ...paramFormState });
  const formDialogModel = ref(false);

  const emits = defineEmits(['close']);
  const handleSubmit = async (params, done) => {
    try {
      await addMyCustomerApi(params);
      window.$message.success('添加成功');
      formDialogModel.value = false;
    } catch (err) {
      console.log(err);
    } finally {
      done();
    }
  };
  const handleClose = () => {
    Object.assign(paramForm, paramFormState);
    emits('close');
  };
  const handleCityUpdate = (data: Array<any> | null) => {
    if (data) {
      paramForm.provinceCode = data[0].value;
      paramForm.provinceName = data[0].label;
      if (data.length > 1) {
        paramForm.cityCode = data[1].value;
        paramForm.cityName = data[1].label;
      }
    } else {
      paramForm.provinceCode = '';
      paramForm.provinceName = '';
      paramForm.cityCode = '';
      paramForm.cityName = '';
    }
  };

  defineExpose({
    formDialogModel,
  });
</script>
