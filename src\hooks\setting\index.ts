import type { GlobConfig } from '/#/config';

import { getAppEnvConfig } from '@/utils/env';
import { warn } from '@/utils/log';

export const useGlobSetting = (): Readonly<GlobConfig> => {
  const {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_UPLOAD_URL,
    VITE_GLOB_FILE_URL,
    VITE_USE_MOCK,
    VITE_LOGGER_MOCK,
  } = getAppEnvConfig();

  if (!/[a-zA-Z\_]*/.test(VITE_GLOB_APP_SHORT_NAME)) {
    warn(
      `VITE_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.`
    );
  }

  // Take global configuration
  const glob: Readonly<GlobConfig> = {
    title: VITE_GLOB_APP_TITLE,
    apiUrl: VITE_GLOB_API_URL,
    shortName: VITE_GLOB_APP_SHORT_NAME,
    uploadUrl: VITE_GLOB_UPLOAD_URL,
    fileUrl: VITE_GLOB_FILE_URL,
    useMock: VITE_USE_MOCK === 'true',
    loggerMock: VITE_LOGGER_MOCK === 'true',
  };
  return glob as Readonly<GlobConfig>;
};
