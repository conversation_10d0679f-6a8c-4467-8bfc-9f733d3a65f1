import { defineAxiosMock, resultSuccess } from '../_util';
import type MockAdapter from 'axios-mock-adapter';
import Mock from 'mockjs';
const Random = Mock.Random;
const allMenue = [
  {
    id: 28,
    title: '工作台',
    icon: 'el-icon-house',
    path: '/dashboard',
    parentId: 0,
    sort: 999,
    children: [
      {
        id: 29,
        title: '工作台',
        icon: '',
        path: '/dashboard/workplace',
        parentId: 28,
        sort: 999,
        children: null,
      },
    ],
  },
  {
    id: 1,
    title: '系统管理',
    icon: 'el-icon-s-operation',
    path: '/system',
    parentId: 0,
    sort: 0,
    children: [
      {
        id: 21,
        title: '用户管理',
        icon: '',
        path: '/system/userManagement',
        parentId: 1,
        sort: 99,
        children: [
          {
            id: 23,
            title: '用户列表',
            icon: '',
            path: '/userAccount',
            parentId: 21,
            sort: 2,
            children: null,
            hiddenInMenu: true,
          },
          {
            id: 22,
            title: '部门管理',
            icon: '',
            path: '/system/accountRole',
            parentId: 21,
            sort: 1,
            children: null,
            hiddenInMenu: true,
          },
        ],
      },
      {
        id: 3,
        title: '系统角色',
        icon: '',
        path: '/system/role',
        parentId: 1,
        sort: 98,
        children: null,
      },
      {
        id: 4,
        title: '系统菜单',
        icon: '',
        path: '/system/menu',
        parentId: 1,
        sort: 97,
        children: null,
      },
      {
        id: 18,
        title: '系统日志',
        icon: '',
        path: '/system/logRecord',
        parentId: 1,
        sort: 96,
        children: null,
      },
      {
        id: 33,
        title: '系统参数',
        icon: '',
        path: '/system/argument',
        parentId: 1,
        sort: 90,
        children: null,
      },
      {
        id: 34,
        title: 'IP白名单',
        icon: '',
        path: '/system/ipWhiteList',
        parentId: 1,
        sort: 89,
        children: null,
      },
    ],
  },
  {
    id: 246,
    title: '列表页面',
    icon: 'el-icon-house',
    path: '/list',
    parentId: 0,
    sort: 999,
    children: [
      {
        id: 291,
        title: '基础列表',
        icon: '',
        path: '/list/basic-list',
        parentId: 28,
        sort: 999,
        children: null,
      },
    ],
  },
];
const roleList = [
  {
    id: 1,
    name: '超级管理员',
    code: 'SUPER_ROLE',
    status: 0,
    menus: null,
    permissions: null,
    buttons: null,
  },
  {
    id: 2,
    name: '运营',
    code: 'code-d1',
    status: 1,
    menus: null,
    permissions: null,
    buttons: null,
  },
  {
    id: 3,
    name: '开发-测试',
    code: 'test',
    status: 0,
    menus: null,
    permissions: null,
    buttons: null,
  },
  {
    id: 4,
    name: '客服',
    code: 'salo1',
    status: 0,
    menus: null,
    permissions: null,
    buttons: null,
  },
];
const permissionList = {
  menus: [
    {
      id: 1,
      title: '系统管理',
      icon: 'el-icon-s-operation',
      path: '/system',
      parentId: 0,
      sort: 0,
      children: [
        {
          id: 2,
          title: '系统账号',
          icon: '',
          path: '/system/account',
          parentId: 1,
          sort: 96,
          children: null,
        },
        {
          id: 3,
          title: '系统角色',
          icon: '',
          path: '/system/role',
          parentId: 1,
          sort: 98,
          children: null,
        },
        {
          id: 4,
          title: '系统菜单',
          icon: '',
          path: '/system/menu',
          parentId: 1,
          sort: 97,
          children: null,
        },
        {
          id: 18,
          title: '系统日志',
          icon: '',
          path: '/system/logRecord',
          parentId: 1,
          sort: 96,
          children: null,
        },
        {
          id: 21,
          title: '用户管理',
          icon: '',
          path: '/system/userManagement',
          parentId: 1,
          sort: 99,
          children: [
            {
              id: 22,
              title: '部门管理',
              icon: '',
              path: '/system/accountRole',
              parentId: 21,
              sort: 1,
              children: null,
            },
            {
              id: 23,
              title: '用户列表',
              icon: '',
              path: '/userAccount',
              parentId: 21,
              sort: 2,
              children: null,
            },
          ],
        },
        {
          id: 33,
          title: '系统参数',
          icon: '',
          path: '/system/argument',
          parentId: 1,
          sort: 90,
          children: null,
        },
        {
          id: 34,
          title: 'IP白名单',
          icon: '',
          path: '/system/ipWhiteList',
          parentId: 1,
          sort: 89,
          children: null,
        },
      ],
    },
    {
      id: 15,
      title: '机构管理',
      icon: 'el-icon-office-building',
      path: '/mechanism',
      parentId: 0,
      sort: 998,
      children: [
        {
          id: 16,
          title: '机构列表',
          icon: '',
          path: '/mechanism/list',
          parentId: 15,
          sort: 9,
          children: null,
        },
        {
          id: 17,
          title: 'API机构',
          icon: '',
          path: '/mechanism/api-list',
          parentId: 15,
          sort: 8,
          children: null,
        },
      ],
    },
    {
      id: 19,
      title: '财务管理',
      icon: 'el-icon-bank-card',
      path: '/financial',
      parentId: 0,
      sort: 997,
      children: [
        {
          id: 20,
          title: '账户管理',
          icon: '',
          path: '/financial/accountManagement',
          parentId: 19,
          sort: 1,
          children: null,
        },
      ],
    },
    {
      id: 28,
      title: '工作台',
      icon: 'el-icon-house',
      path: '/workbench',
      parentId: 0,
      sort: 999,
      children: [
        {
          id: 29,
          title: '数据统计',
          icon: '',
          path: '/workbench/statistics-data',
          parentId: 28,
          sort: 999,
          children: null,
        },
      ],
    },
    {
      id: 30,
      title: '分发管理',
      icon: 'el-icon-connection',
      path: '/clue',
      parentId: 0,
      sort: 996,
      children: [
        {
          id: 32,
          title: '分发记录',
          icon: '',
          path: '/clue/list',
          parentId: 30,
          sort: 9,
          children: null,
        },
        {
          id: 40,
          title: '撞库记录',
          icon: '',
          path: '/clue/match-record',
          parentId: 30,
          sort: 8,
          children: null,
        },
      ],
    },
    {
      id: 35,
      title: '产品管理',
      icon: 'el-icon-news',
      path: '/product',
      parentId: 0,
      sort: 995,
      children: [
        {
          id: 36,
          title: '产品管理',
          icon: '',
          path: '/product/manage',
          parentId: 35,
          sort: 9,
          children: null,
        },
      ],
    },
    {
      id: 37,
      title: '分发策略',
      icon: 'el-icon-position',
      path: '/distribution',
      parentId: 0,
      sort: 994,
      children: [
        {
          id: 38,
          title: '总策略',
          icon: '',
          path: '/distribution/overall-strategy',
          parentId: 37,
          sort: 9,
          children: null,
        },
        {
          id: 39,
          title: '机构策略',
          icon: '',
          path: '/distribution/institutional-strategy',
          parentId: 37,
          sort: 8,
          children: null,
        },
      ],
    },
  ],
  permissions: [
    {
      id: 1,
      title: '超级管理员',
      code: 'admin:manager',
    },
  ],
  buttons: null,
};
const rolePermission = {
  id: 1,
  name: '超级管理员',
  code: 'SUPER_ROLE',
  status: 0,
  menus: [
    29, 28, 15, 19, 30, 35, 37, 21, 3, 4, 18, 33, 34, 16, 32, 36, 38, 17, 39, 40, 23, 20, 22, 1,
  ],
  permissions: [1],
  buttons: [
    'system_menu_add',
    'system_role_add',
    'system_role_edit',
    'system_role_status',
    'system_role_remove',
    '/system/menu/add',
    '/system/menu/edit',
    '/system/menu/delete',
    '/system/accountRole/review',
    '/system/accountRole/addDepartment',
    '/system/accountRole/edit',
    '/system/accountRole/delete',
    '/userAccount/review',
    '/userAccount/system/accountRole/addUser',
    '/userAccount/system/accountRole/accountEditUser',
    '/userAccount/system/accountRole/accountEditStatus',
    '/mechanism/list/review',
    '/mechanism/list/add',
    '/mechanism/list/detail',
    '/mechanism/list/certification',
    '/mechanism/list/account',
    '/mechanism/list/product',
    '/mechanism/list/status',
    '/mechanism/api-list/review',
    '/mechanism/api-list/detail',
    '/financial/accountManagement/review',
    '/financial/accountManagement/recharge',
    '/financial/accountManagement/deduct',
    '/financial/accountManagement/bail_recharge',
    '/financial/accountManagement/detail',
    '/workbench/statistics-data/review',
    '/clue/list/review',
    '/clue/list/export',
    '/clue/match-record/review',
    '/clue/match-record/export',
    '/clue/match-record/detail',
    '/product/manage/review',
    '/product/manage/create',
    '/product/manage/edit',
    '/product/manage/status',
    '/distribution/overall-strategy/review',
    '/distribution/overall-strategy/create',
    '/distribution/overall-strategy/edit',
    '/distribution/overall-strategy/status',
    '/distribution/institutional-strategy/review',
    '/distribution/institutional-strategy/create',
    '/distribution/institutional-strategy/edit',
    '/distribution/institutional-strategy/status',
    '/distribution/institutional-strategy/remove',
  ],
};
export default function setupSystemMock(mockAdapter: MockAdapter) {
  defineAxiosMock(mockAdapter, {
    '/admin/send/sms': () => resultSuccess({ id: Random.id() }),
    '/admin/login': () => resultSuccess({ id: Random.id(), token: Random.id() }),
    '/admin/me': () =>
      resultSuccess({
        id: Random.id(),
        username: Random.name(),
        menus: [
          {
            id: 28,
            title: '工作台',
            icon: 'el-icon-house',
            path: '/dashboard',
            parentId: 0,
            sort: 999,
            children: [
              {
                id: 29,
                title: '工作台',
                icon: '',
                path: '/dashboard/workplace',
                parentId: 28,
                sort: 999,
                children: null,
              },
            ],
          },
          {
            id: 100,
            title: '组织管理',
            icon: 'el-icon-house',
            path: '/organize',
            parentId: 0,
            sort: 999,
            children: [
              {
                id: 101,
                title: '用户管理',
                icon: '',
                path: '/organize/user',
                parentId: 100,
                sort: 999,
                children: null,
              },
            ],
          },
          {
            id: 110,
            title: '客户管理',
            icon: 'el-icon-house',
            path: '/client',
            parentId: 0,
            sort: 999,
            children: [
              {
                id: 111,
                title: '客户公海',
                icon: '',
                path: '/client/high-seas',
                parentId: 100,
                sort: 999,
                children: null,
              },
            ],
          },
          {
            id: 1,
            title: '系统管理',
            icon: 'el-icon-s-operation',
            path: '/system',
            parentId: 0,
            sort: 0,
            children: [
              {
                id: 21,
                title: '用户管理',
                icon: '',
                path: '/system/userManagement',
                parentId: 1,
                sort: 99,
                children: [
                  {
                    id: 23,
                    title: '用户列表',
                    icon: '',
                    path: '/userAccount',
                    parentId: 21,
                    sort: 2,
                    children: null,
                    hiddenInMenu: true,
                  },
                  {
                    id: 22,
                    title: '部门管理',
                    icon: '',
                    path: '/system/accountRole',
                    parentId: 21,
                    sort: 1,
                    children: null,
                    hiddenInMenu: true,
                  },
                ],
              },
              {
                id: 3,
                title: '系统角色',
                icon: '',
                path: '/system/role',
                parentId: 1,
                sort: 98,
                children: null,
              },
              {
                id: 4,
                title: '系统菜单',
                icon: '',
                path: '/system/menu',
                parentId: 1,
                sort: 97,
                children: null,
              },
              {
                id: 18,
                title: '系统日志',
                icon: '',
                path: '/system/logRecord',
                parentId: 1,
                sort: 96,
                children: null,
              },
              {
                id: 33,
                title: '系统参数',
                icon: '',
                path: '/system/argument',
                parentId: 1,
                sort: 90,
                children: null,
              },
              {
                id: 34,
                title: 'IP白名单',
                icon: '',
                path: '/system/ipWhiteList',
                parentId: 1,
                sort: 89,
                children: null,
              },
            ],
          },
        ],
      }),
    '/menu/list': () => resultSuccess(allMenue),
    '/admin/role/list': () => resultSuccess(roleList),
    '/admin/role/menus/permission': () => resultSuccess(permissionList),
    '/admin/role/1': () => resultSuccess(rolePermission),
  });
}
