// 客户管理接口
import { get, post } from '@/utils/lib/axios.package';
import qs from 'qs';

// 获取客户公海list
export function getCustomerPoolListApi(params) {
  return get('/cms/public-clue/page', params);
}
// 客户公海导出
export function exportCustomerPoolApi(params) {
  return `${import.meta.env.VITE_GLOB_API_URL}/cms/public-clue/export?${qs.stringify(params)}`;
}
// 新增线索
export function addCustomerPoolApi(params) {
  return post('/cms/public-clue/insert', params);
}
// 获取我的客户list
export function getMyCustomerListApi(params) {
  return get('/cms/self-clue/page', params);
}
// 我的客户导出
export function exportMyCustomerApi(params) {
  return `${import.meta.env.VITE_GLOB_API_URL}/cms/self-clue/export?${qs.stringify(params)}`;
}
// 我的客户新增
export function addMyCustomerApi(params) {
  return post('/cms/self-clue/insert', params);
}
// 认领客户
export function claimCustomerApi(receive) {
  return post(`/cms/self-clue/receive/${receive}`);
}
// 根据用户查询线索领取限制
export function getUserClueLimitApi() {
  return get('/cms/clue/role/receive/find-self');
}
// 更新客户详细信息
export function updateCustomerInfoApi(data) {
  return post('/cms/workspace/insertOrUpdate/loan/properties', data);
}

interface IGetCustomerInfoParams {
  clueId: number;
}
// 查询客户详细信息
export function getCustomerInfoApi(params: IGetCustomerInfoParams) {
  return get(`/cms/workspace/query/loan/properties/${params.clueId}`);
}
// 查询渠道
export function getChannelListApi() {
  return get(`/cms/channel/all`);
}

// 容掌通拨打电话
export function customerInfoCall(data) {
  return post('/cms/mdm/dial', data);
}
// 查看跟进记录
export function getFollowUpRecord(params) {
  return get(`/cms/workspace/get/follow-up/record`, params);
}

// 查询投放平台（全部）
export function getAdvflowMediaAllApi() {
  return get('/cms/volumeofflow/advflow/media/all');
}
