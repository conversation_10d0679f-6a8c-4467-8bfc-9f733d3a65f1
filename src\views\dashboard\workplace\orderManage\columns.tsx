import { BasicColumn } from '@/components/Table';
import dayjs from 'dayjs';
import CallPhoneButton from '@/views/modules/tableWorkCallButton.vue';

// 订单状态枚举
export const OrderStatusMap: Record<number, string> = {
  1: '跟进中',
  2: '订单取消',
  3: '订单完结',
};

// 订单节点枚举
export const OrderNodeMap: Record<number, string> = {
  1: '预审阶段',
  2: '进件签约',
  3: '后置阶段',
  4: '放款阶段',
};

export interface ListData {
  id: number; // 进件ID
  loanOrderId: string; // 贷款订单ID
  channelLoanOrderId: string; // 渠道贷款订单ID
  customerName: string; // 客户姓名
  contactPhone: string; // 联系电话
  phoneMd5: string; // 手机号MD5
  capitalProvider: string; // 进件资方
  productInfo: string; // 产品信息
  loanAmount: number; // 贷款金额
  loanPeriod: number; // 贷款期限
  orderStatus: number; // 订单状态
  orderNode: number; // 订单节点
  nodeStatus: string; // 节点状态
  faceSignStatus: string; // 面签节点状态
  gpsInstallStatus: string; // GPS安装节点状态
  mortgageStatus: string; // 抵押节点状态
  orderStartTime: string; // 创建订单时间
  lastFollowTime: string; // 最近跟进时间
  orderEndTime: string; // 订单结束时间
}

export const columns: BasicColumn<ListData>[] = [
  {
    title: '进件ID',
    key: 'id',
    width: 80,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '贷款订单ID',
    key: 'loanOrderId',
    width: 150,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '渠道贷款订单ID',
    key: 'channelLoanOrderId',
    width: 150,
    align: 'center',
  },
  {
    title: '客户姓名',
    key: 'customerName',
    width: 100,
    align: 'center',
  },
  {
    title: '联系电话',
    key: 'contactPhone',
    width: 130,
    align: 'center',
    render: (record: ListData) => {
      return (
        <CallPhoneButton
          record={record}
          clubId={record.id}
          mobileNo={record.contactPhone}
        ></CallPhoneButton>
      );
    },
  },
  {
    title: '手机号MD5',
    key: 'phoneMd5',
    width: 200,
    align: 'center',
  },
  {
    title: '进件资方',
    key: 'capitalProvider',
    width: 120,
    align: 'center',
  },
  {
    title: '产品信息',
    key: 'productInfo',
    width: 120,
    align: 'center',
  },
  {
    title: '贷款金额',
    key: 'loanAmount',
    width: 120,
    align: 'center',
    render: (record: ListData) => {
      return <span>{record.loanAmount ? `¥${record.loanAmount.toLocaleString()}` : '-'}</span>;
    },
  },
  {
    title: '贷款期限',
    key: 'loanPeriod',
    width: 100,
    align: 'center',
    render: (record: ListData) => {
      return <span>{record.loanPeriod ? `${record.loanPeriod}个月` : '-'}</span>;
    },
  },
  {
    title: '订单状态',
    key: 'orderStatus',
    width: 100,
    align: 'center',
    render: (record: ListData) => {
      return <span>{OrderStatusMap[record.orderStatus] || '-'}</span>;
    },
  },
  {
    title: '订单节点',
    key: 'orderNode',
    width: 100,
    align: 'center',
    render: (record: ListData) => {
      return <span>{OrderNodeMap[record.orderNode] || '-'}</span>;
    },
  },
  {
    title: '节点状态',
    key: 'nodeStatus',
    width: 100,
    align: 'center',
  },
  {
    title: '面签节点状态',
    key: 'faceSignStatus',
    width: 120,
    align: 'center',
  },
  {
    title: 'GPS安装节点状态',
    key: 'gpsInstallStatus',
    width: 140,
    align: 'center',
  },
  {
    title: '抵押节点状态',
    key: 'mortgageStatus',
    width: 120,
    align: 'center',
  },
  {
    title: '创建订单时间',
    key: 'orderStartTime',
    width: 180,
    align: 'center',
    sorter: (a, b) => {
      const timeA = dayjs(a.orderStartTime || 0).valueOf();
      const timeB = dayjs(b.orderStartTime || 0).valueOf();
      return timeA - timeB;
    },
    render: (record: ListData) => {
      return (
        <span>
          {record.orderStartTime ? dayjs(record.orderStartTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '最近跟进时间',
    key: 'lastFollowTime',
    width: 180,
    align: 'center',
    sorter: (a, b) => {
      const timeA = dayjs(a.lastFollowTime || 0).valueOf();
      const timeB = dayjs(b.lastFollowTime || 0).valueOf();
      return timeA - timeB;
    },
    render: (record: ListData) => {
      return (
        <span>
          {record.lastFollowTime ? dayjs(record.lastFollowTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '订单结束时间',
    key: 'orderEndTime',
    width: 180,
    align: 'center',
    sorter: (a, b) => {
      const timeA = dayjs(a.orderEndTime || 0).valueOf();
      const timeB = dayjs(b.orderEndTime || 0).valueOf();
      return timeA - timeB;
    },
    render: (record: ListData) => {
      return (
        <span>
          {record.orderEndTime ? dayjs(record.orderEndTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
];
