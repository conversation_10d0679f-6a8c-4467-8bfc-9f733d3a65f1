import Mock from 'mockjs';
import type <PERSON><PERSON><PERSON>dapter from 'axios-mock-adapter';

export function resultSuccess(result: any, { message = 'ok' } = {}) {
  return Mock.mock({
    code: 200,
    data: result,
    message,
    type: 'success',
  });
}

export function resultError(message = 'Request failed', { code = -1, result = null } = {}) {
  return {
    code,
    data: result,
    message,
    type: 'error',
  };
}

export function defineAxiosMock(mockAdapter: MockAdapter, mocks: Record<string, Function>) {
  Object.entries(mocks).forEach(([url, handler]) => {
    // 支持 [METHOD]url 格式，如 [POST]/api/login
    const methodMatch = url.match(/^\[(\w+)\](.+)$/);
    if (methodMatch) {
      const [, method, path] = methodMatch;
      mockAdapter.onAny(path).reply((config) => {
        if (config.method?.toUpperCase() === method.toUpperCase()) {
          const result = handler(config);
          return [200, result];
        }
        return [404, { message: 'Not Found' }];
      });
    } else {
      // 默认 GET 请求
      mockAdapter.onGet(url).reply(() => {
        const result = handler();
        return [200, result];
      });
    }
  });
}
