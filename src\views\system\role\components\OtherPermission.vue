<template>
  <n-space vertical>
    <n-space align="center">
      <span>手机号脱敏(启用后手机号显示&导出将进行脱敏处理)</span
      ><n-tooltip trigger="hover">
        <template #trigger>
          <div class="flex items-center">
            <n-icon size="22" color="#2080f0">
              <HelpCircleOutline />
            </n-icon>
          </div>
        </template>
        <span>生效范围：工作台、我的客户、客户公海、用户管理</span>
      </n-tooltip>
    </n-space>

    <n-checkbox-group :value="checkedKeys" @update:value="handleUpdateValue">
      <n-space item-style="display: flex;" vertical>
        <n-checkbox v-for="item in items" :key="item.id" class="mb-[10px]" :value="item.id">
          {{ item.name }}
        </n-checkbox>
      </n-space>
    </n-checkbox-group>
  </n-space>
</template>

<script setup lang="ts">
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { ref, watch } from 'vue';
  import { isEmpty } from '@/utils/is';
  import { updateRoleMobile } from '@/api/system/role';
  import { useUser } from '@/store/modules/user';

  defineExpose({
    sure,
  });
  const props = defineProps({
    roleId: {
      type: [Number, String],
      default: '',
    },
    roleDetail: {
      type: Object,
      default: () => ({}),
    },
  });
  const userStore = useUser();
  const checkedKeys = ref<any[]>([]);
  watch(
    () => props.roleDetail.id,
    () => {
      getData();
    },
    { immediate: true }
  );

  const items = [
    {
      id: 0,
      name: '启用',
    },
    {
      id: 1,
      name: '禁用',
    },
  ];

  function handleUpdateValue(_, meta) {
    checkedKeys.value = meta.actionType === 'check' ? [meta.value] : [];
  }

  function getData() {
    const { roleMobilePermission } = props.roleDetail;
    if (!isEmpty(roleMobilePermission)) {
      checkedKeys.value = [roleMobilePermission];
    } else {
      checkedKeys.value = [1];
    }
  }

  function sure() {
    const postData = {
      id: Number(props.roleId),
      roleMobilePermission:
        checkedKeys.value?.[0] !== undefined ? Number(checkedKeys.value?.[0]) : null,
    };

    return updateRoleMobile(postData).then((res) => {
      if (res.code == 200) {
        window.$message.success('配置成功');
        userStore.getInfo();
      }
    });
  }
</script>

<style lang="less" scoped></style>
