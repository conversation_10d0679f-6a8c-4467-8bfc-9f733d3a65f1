import mitt from 'mitt';

// 事件名称常量定义，防止事件名称冲突
export enum EventNames {
  // 消息相关事件--处理消息
  HANDLE_MESSAGE_SUCCESS = 'handleMessageSuccess',
  // 消息相关事件--新增消息
  ADD_MESSAGE_SUCCESS = 'addMessageSuccess',
  /** -------------------- 分割线 -------------------- */
  /**下面事件主要用于未来发生事件的传递，使用的事件总线是bufferedEmitter */
  // 跳转工作台后续触发事件
  JUMP_WORKBENCH_AFTER = 'jumpWorkbenchAfter',
  // 领取线索后跳转工作台切换到对应tab栏
  JUMP_WORKBENCH_TAB = 'jumpWorkbenchTab',
}

const emitter = mitt();

type Handler<T = any> = (payload: T) => void;

interface EmitOptions {
  /** true: 仅保留该事件最后一条（覆盖式）；false: 追加（队列式） */
  replace?: boolean;
}

export function createBufferedEmitter() {
  const emitter = mitt();
  // 每个事件名对应一个简单缓冲队列
  const buffer = new Map<string, any[]>();

  function emit<T = any>(event: string, payload?: T, options: EmitOptions = {}) {
    const { replace = true } = options; // 默认覆盖式，更贴合“指令”语义
    const q = buffer.get(event) ?? [];
    if (replace) {
      buffer.set(event, [payload]);
    } else {
      q.push(payload);
      buffer.set(event, q);
    }
    emitter.emit(event, payload);
  }

  function on<T = any>(event: string, handler: Handler<T>) {
    // 回放并清空缓冲，避免重复消费
    const q = buffer.get(event);
    if (q && q.length) {
      buffer.set(event, []);
      q.forEach((item) => handler(item));
    }

    const wrapped = (payload: T) => handler(payload);
    emitter.on(event, wrapped);
    return () => emitter.off(event, wrapped);
  }

  function off(event: string, handler?: Handler) {
    emitter.off(event, handler);
  }

  function clear(event?: string) {
    if (event) {
      buffer.delete(event);
    } else {
      buffer.clear();
    }
  }

  return { emit, on, off, clear };
}

// 单例：全局复用
export const bufferedEmitter = createBufferedEmitter();

export default emitter;
