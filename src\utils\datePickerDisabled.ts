// 日期组件禁用方法
import dayjs from 'dayjs';

/**
 * 创建限制最大月份范围，并禁止选择当天之后日期的 is-date-disabled 函数
 * @param maxMonths 最大可选月份数
 */
export function createMonthRangeDisabledFn(maxMonths: number) {
  const todayEnd = dayjs().endOf('day');

  return (ts: number, phase: 'start' | 'end', value: [number, number] | null): boolean => {
    const current = dayjs(ts);

    // 禁用当天之后日期
    if (current.isAfter(todayEnd)) return true;

    if (!value) return false;

    const [start, end] = value;

    if (phase === 'start' && end) {
      const minStart = dayjs(end).subtract(maxMonths, 'month').startOf('day');
      return current.isBefore(minStart);
    }

    if (phase === 'end' && start) {
      const maxEnd = dayjs(start).add(maxMonths, 'month').endOf('day');
      return current.isAfter(maxEnd);
    }

    return false;
  };
}
