import { RedirectName } from '@/router/constant';

export enum PageEnum {
  // 登录
  BASE_LOGIN = '/login',
  BASE_LOGIN_NAME = 'Login',
  //重定向
  REDIRECT = '/redirect',
  REDIRECT_NAME = RedirectName,
  REDIRECT_SON_NAME = `${RedirectName}Son`,
  // 首页
  BASE_HOME = '/dashboard',
  //首页跳转默认路由
  BASE_HOME_REDIRECT = '/dashboard/welcome',
  // 错误
  ERROR_PAGE_NAME = 'ErrorPage',
  ERROR_SON_PAGE_NAME = 'ErrorPageSon',

  ICON_PREVIEW = '/iconPreview',
  ICON_PREVIEW_NAME = 'IconPreview',
}
