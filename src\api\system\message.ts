import { get, post } from '@/utils/lib/axios.package';

// 用户消息分页查询
export const getUserMessage = (param) => {
  return get('/cms/user-message/page', param);
};
// 获取所有消息类型
export const getMessageTypes = () => {
  return get('/cms/user-message/types');
};
// 批量操作用户消息
export const batchUserMessage = (param: { operationType: 1 | 2; messageIds: number[] }) => {
  return post('/cms/user-message/batch', param);
};
// 查询当前用户消息条数(所有未确认,未读线索,未读消息)
export const getUserMessageCount = () => {
  return get('/cms/user-message/unconfirmed', null, { showErrorMsg: false });
};
// 所有线索消息已确认
export const batchClueReminded = () => {
  return post('/cms/user-message/clue/handled');
};
