<template>
  <div class="flex items-center mb-2">
    <span class="text-md font-bold">{{ title }}</span>

    <n-tooltip v-if="desc" trigger="hover">
      <template #trigger>
        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
          <QuestionCircleTwotone />
        </n-icon>
      </template>
      {{ desc }}
    </n-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { QuestionCircleTwotone } from '@vicons/antd';

  defineProps<{
    title: string;
    desc?: string;
  }>();
</script>
