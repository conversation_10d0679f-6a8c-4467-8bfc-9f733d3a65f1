# mountComponent

用于在任意 DOM 节点上临时挂载组件或 VNode 的轻量工具。参数收敛到单一 `options`，通过 `render` 统一渲染入口，支持三种形态：`render(ctx)` 函数、直接 VNode、或直接组件。

## 特性

- 统一渲染入口：`render` 支持函数/VNode/组件
- 上下文注入：`render(ctx)` 拿到 `unmount/container/mountTarget/animation/isMounted`
- 内置过渡动画：`fade | slide-up | slide-down | slide-left | slide-right`
- 可指定挂载目标：`mountTo` 支持 DOM 或 CSS 选择器
- 返回卸载函数：`const unmount = mountComponent(...)`

## 安装与导入

```ts
import { mountComponent } from '@/utils/mountComponent';
import type { RenderCtx, MountOptions } from '@/utils/mountComponent';
```

## API

```ts
function mountComponent(options: {
  // 统一渲染入口：函数 / VNode / 组件
  render?: ((ctx: RenderCtx) => VNode) | VNode | Component;
  // 可选项
  animation?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right';
  appContext?: AppContext;
  mountTo?: HTMLElement | string;
}): () => void;
```

### RenderCtx

```ts
interface RenderCtx {
  unmount: () => void; // 卸载当前挂载实例
  container: HTMLElement; // 内部创建的容器（真实渲染根）
  mountTarget: HTMLElement; // 最终插入页面的目标节点
  animation?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right';
  isMounted: Ref<boolean>; // 是否处于显示状态（进入/离开过渡）
}
```

## 使用示例

### 1) render(ctx)（推荐）

```tsx
const unmount = mountComponent({
  animation: 'fade',
  render: (ctx) => <MyModal open={ctx.isMounted.value} onClose={ctx.unmount} />,
});
```

### 2) 直接传入 VNode（h/JSX）

```ts
const vnode = h(MyPopover, { text: 'Hello' });
const unmount = mountComponent({ render: vnode });
```

```tsx
const unmount = mountComponent({ render: <MyPopover text="Hello" /> });
```

### 3) 直接传入组件 (这个方式无法传递 props)

```ts
const Component = {
  setup() {
    return () => {
      return h('div', '组件内容');
    };
  },
};
const unmount = mountComponent({ render: Component });
```
