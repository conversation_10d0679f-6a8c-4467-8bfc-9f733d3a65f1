import { createEnumOptions } from '@/utils/createEnumOptions';

// 消息类型
export enum MessageType {
  // 线索领取
  Clue = 0,
  // 联系提醒
  Contact = 1,
}
export const MessageTypeMap = {
  [MessageType.Clue]: '线索领取',
  [MessageType.Contact]: '联系提醒',
};
export const MessageTypeOptions = createEnumOptions(MessageTypeMap);

// 消息状态: 待确认；已确认
export enum MessageStatus {
  // 待确认
  WaitConfirm = 0,
  // 已确认
  Confirmed = 1,
}
export const MessageStatusMap = {
  [MessageStatus.WaitConfirm]: '待确认',
  [MessageStatus.Confirmed]: '已确认',
};
export const MessageStatusOptions = createEnumOptions(MessageStatusMap);

// 跳转工作台：领取线索、打开详情
export enum JumpWorkbench {
  // 打开详情
  OpenDetail = 1,
  // 领取线索
  ReceiveClue = 2,
}
export const JumpWorkbenchMap = {
  [JumpWorkbench.OpenDetail]: '打开详情',
  [JumpWorkbench.ReceiveClue]: '领取线索',
};
export const JumpWorkbenchOptions = createEnumOptions(JumpWorkbenchMap);
