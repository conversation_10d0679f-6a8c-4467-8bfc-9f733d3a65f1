import type { Directive } from 'vue';
import { createVNode, render } from 'vue';
import { NSpin } from 'naive-ui';
import { useDesignSetting } from '@/store/modules/designSetting';

// 获取主题色
const designStore = useDesignSetting();
const { appTheme } = designStore;

// 使用 WeakMap 存储每个元素对应的状态，避免内存泄漏
const elMap = new WeakMap<
  HTMLElement,
  {
    container: HTMLDivElement;
    originalPosition: string;
  }
>();

/**
 * 创建加载动画的函数
 * @param el - 指令所绑定的元素
 */
function createLoading(el: HTMLElement) {
  // 保存元素原始的 position 样式
  const originalPosition = el.style.position;
  const parentStyle = getComputedStyle(el);
  // 如果元素的定位是 static，则设置为 relative，以便遮罩层能正确定位
  if (parentStyle.position === 'static') {
    el.style.position = 'relative';
  }

  // 创建一个 div 作为加载动画的容器（遮罩层）
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.top = '0';
  container.style.left = '0';
  container.style.width = '100%';
  container.style.height = '100%';
  container.style.zIndex = '100';
  container.style.display = 'flex';
  container.style.justifyContent = 'center';
  container.style.alignItems = 'center';
  container.style.backgroundColor = 'rgba(255, 255, 255, 0.75)';

  // 创建 NSpin 组件的虚拟节点
  const spinVNode = createVNode(NSpin, { size: 'large', stroke: appTheme });
  // 将 NSpin 组件渲染到容器中
  render(spinVNode, container);
  // 将容器添加到目标元素
  el.appendChild(container);
  // 存储元素状态
  elMap.set(el, { container, originalPosition });
}

/**
 * 移除加载动画的函数
 * @param el - 指令所绑定的元素
 */
function removeLoading(el: HTMLElement) {
  if (elMap.has(el)) {
    const { container, originalPosition } = elMap.get(el)!;
    // 恢复元素原始的 position 样式
    el.style.position = originalPosition;
    // 卸载 Vue 组件
    render(null, container);
    // 从 DOM 中移除容器
    el.removeChild(container);
    // 从 Map 中删除元素状态
    elMap.delete(el);
  }
}

const loadingDirective: Directive = {
  // 当绑定的元素插入到 DOM 中时
  mounted(el, binding) {
    if (binding.value) {
      createLoading(el);
    }
  },
  // 当 VNode 更新时
  updated(el, binding) {
    // 仅当绑定值变化时才执行
    if (binding.value !== binding.oldValue) {
      if (binding.value) {
        createLoading(el);
      } else {
        removeLoading(el);
      }
    }
  },
  // 当绑定的元素从 DOM 中卸载时
  unmounted(el) {
    removeLoading(el);
  },
};

export default loadingDirective;
