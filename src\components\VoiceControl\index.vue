<template>
  <div class="voice-control">
    <div class="voice-control__container">
      <!-- 音频文件选择 -->
      <div class="voice-control__file-input">
        <n-input-group>
          <n-input v-model:value="audioSrc" placeholder="请输入音频文件URL或选择本地文件" />
          <n-button @click="selectFile">
            <template #icon>
              <n-icon><FolderOpenOutline /></n-icon>
            </template>
            选择文件
          </n-button>
        </n-input-group>
        <input
          ref="fileInput"
          type="file"
          accept="audio/*"
          style="display: none"
          @change="handleFileSelect"
        />
      </div>

      <!-- 音频播放器 -->
      <audio
        ref="audioElement"
        :src="audioSrc"
        @loadedmetadata="onAudioLoaded"
        @timeupdate="onTimeUpdate"
        @ended="onAudioEnded"
        @error="onAudioError"
      ></audio>

      <!-- 播放控制按钮 -->
      <div class="voice-control__controls">
        <n-space align="center" justify="center">
          <n-button type="primary" :disabled="!audioSrc" @click="togglePlay" :loading="isLoading">
            <template #icon>
              <n-icon>
                <PlayOutline v-if="!isPlaying" />
                <PauseOutline v-else />
              </n-icon>
            </template>
            {{ isPlaying ? '暂停' : '播放' }}
          </n-button>

          <n-button type="default" :disabled="!audioSrc" @click="restart">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            重新开始
          </n-button>
        </n-space>
      </div>

      <!-- 进度条 -->
      <div class="voice-control__progress" v-if="audioSrc">
        <div class="voice-control__time-display">
          <span>{{ formatTime(currentTime) }}</span>
          <span>{{ formatTime(duration) }}</span>
        </div>
        <n-slider
          v-model:value="currentTime"
          :max="duration"
          :step="0.1"
          :disabled="!audioSrc"
          @update:value="onSeek"
        />
      </div>

      <!-- 音量控制 -->
      <div class="voice-control__volume" v-if="audioSrc">
        <n-space align="center">
          <n-icon size="16">
            <VolumeHighOutline v-if="volume > 0.5" />
            <VolumeMediumOutline v-else-if="volume > 0" />
            <VolumeMuteOutline v-else />
          </n-icon>
          <n-slider
            v-model:value="volume"
            :min="0"
            :max="1"
            :step="0.1"
            style="width: 100px"
            @update:value="onVolumeChange"
          />
        </n-space>
      </div>

      <!-- 播放速度控制 -->
      <div class="voice-control__speed" v-if="audioSrc">
        <n-space align="center">
          <span>播放速度:</span>
          <n-select
            v-model:value="playbackRate"
            :options="speedOptions"
            size="small"
            style="width: 80px"
            @update:value="onSpeedChange"
          />
        </n-space>
      </div>

      <div class="voice-control__status">
        <n-alert
          v-if="errorMessage"
          type="error"
          :title="errorMessage"
          show-icon
          closable
          @close="errorMessage = ''"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
  import {
    PlayOutline,
    PauseOutline,
    RefreshOutline,
    VolumeHighOutline,
    VolumeMediumOutline,
    VolumeMuteOutline,
    FolderOpenOutline,
  } from '@vicons/ionicons5';
  import { NButton, NSpace, NInput, NInputGroup, NIcon, NSlider, NSelect, NAlert } from 'naive-ui';
  import { formatTime } from '@/utils/dateUtil';
  // 组件属性
  interface Props {
    src?: string;
    autoplay?: boolean;
    loop?: boolean;
    preload?: 'none' | 'metadata' | 'auto';
    enablePlaybackPosition?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    src: '',
    autoplay: false,
    loop: false,
    preload: 'metadata',
    enablePlaybackPosition: true,
  });

  // 组件事件
  const emit = defineEmits<{
    play: [];
    pause: [];
    stop: [];
    ended: [];
    error: [error: string];
    timeupdate: [currentTime: number, duration: number];
    volumechange: [volume: number];
    speedchange: [speed: number];
    playbackPositionSaved: [position: number];
    playbackPositionCleared: [];
  }>();

  // 响应式数据
  const audioElement = ref<HTMLAudioElement>();
  const fileInput = ref<HTMLInputElement>();
  const audioFile = ref('');
  const audioSrc = ref(props.src);
  const isPlaying = ref(false);
  const isLoading = ref(false);
  const currentTime = ref(0);
  const duration = ref(0);
  const volume = ref(1);
  const playbackRate = ref(1);
  const errorMessage = ref('');
  const statusMessage = ref('');
  // 播放位置相关
  const savedPlaybackPosition = ref(0);
  const hasPlaybackPosition = computed(() => savedPlaybackPosition.value > 0);

  // 播放速度选项
  const speedOptions = [
    { label: '0.5x', value: 0.5 },
    { label: '0.75x', value: 0.75 },
    { label: '1x', value: 1 },
    { label: '1.25x', value: 1.25 },
    { label: '1.5x', value: 1.5 },
    { label: '2x', value: 2 },
  ];

  // 计算属性
  const canPlay = computed(() => !!audioSrc.value);

  // 获取存储键名
  const getStorageKey = () => {
    const fileName = audioFile.value || audioSrc.value;
    return `voice_control_position_${btoa(fileName).replace(/[^a-zA-Z0-9]/g, '')}`;
  };

  // 保存播放位置
  const savePlaybackPosition = () => {
    if (!props.enablePlaybackPosition || !audioSrc.value) return;

    const position = currentTime.value;
    if (position > 0 && position < duration.value) {
      savedPlaybackPosition.value = position;
      localStorage.setItem(getStorageKey(), position.toString());
      emit('playbackPositionSaved', position);
    }
  };

  // 加载播放位置
  const loadPlaybackPosition = () => {
    if (!props.enablePlaybackPosition || !audioSrc.value) return;

    try {
      const saved = localStorage.getItem(getStorageKey());
      if (saved) {
        const position = parseFloat(saved);
        if (position > 0) {
          savedPlaybackPosition.value = position;
        }
      }
    } catch (error) {
      console.warn('Failed to load playback position:', error);
    }
  };

  // 清除播放位置
  const clearPlaybackPosition = () => {
    savedPlaybackPosition.value = 0;
    localStorage.removeItem(getStorageKey());
    emit('playbackPositionCleared');
    statusMessage.value = '已清除保存的播放位置';
  };

  // 方法
  const selectFile = () => {
    fileInput.value?.click();
  };

  const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    if (file) {
      audioFile.value = file.name;
      audioSrc.value = URL.createObjectURL(file);
      statusMessage.value = `已选择文件: ${file.name}`;

      // 加载新文件的播放位置
      setTimeout(() => {
        loadPlaybackPosition();
      }, 100);
    }
  };

  const togglePlay = async () => {
    if (!audioElement.value || !audioSrc.value) return;

    try {
      isLoading.value = true;
      if (isPlaying.value) {
        await pause();
      } else {
        await play();
      }
    } catch (error) {
      errorMessage.value = `播放失败: ${error}`;
    } finally {
      isLoading.value = false;
    }
  };

  const play = async () => {
    if (!audioElement.value) return;

    try {
      await audioElement.value.play();
      isPlaying.value = true;
      emit('play');
      statusMessage.value = '开始播放';
    } catch (error) {
      throw new Error(`播放失败: ${error}`);
    }
  };

  const pause = async () => {
    if (!audioElement.value) return;

    audioElement.value.pause();
    isPlaying.value = false;
    emit('pause');
    statusMessage.value = '已暂停';

    // 保存播放位置
    savePlaybackPosition();
  };

  const stop = () => {
    if (!audioElement.value) return;

    audioElement.value.pause();
    audioElement.value.currentTime = 0;
    isPlaying.value = false;
    currentTime.value = 0;
    emit('stop');
    statusMessage.value = '已停止';

    // 保存播放位置
    savePlaybackPosition();
  };

  const restart = () => {
    stop();
    setTimeout(() => {
      play();
    }, 100);
  };

  // 继续播放功能
  const continuePlay = async () => {
    if (!audioElement.value || !hasPlaybackPosition.value) return;

    try {
      isLoading.value = true;

      // 设置到保存的位置
      audioElement.value.currentTime = savedPlaybackPosition.value;
      currentTime.value = savedPlaybackPosition.value;

      // 开始播放
      await audioElement.value.play();
      isPlaying.value = true;
      emit('play');
      statusMessage.value = `从 ${formatTime(savedPlaybackPosition.value)} 继续播放`;
    } catch (error) {
      errorMessage.value = `继续播放失败: ${error}`;
    } finally {
      isLoading.value = false;
    }
  };

  const onSeek = (value: number) => {
    if (!audioElement.value) return;

    audioElement.value.currentTime = value;
    currentTime.value = value;
  };

  const onVolumeChange = (value: number) => {
    if (!audioElement.value) return;

    audioElement.value.volume = value;
    volume.value = value;
    emit('volumechange', value);
  };

  const onSpeedChange = (value: number) => {
    if (!audioElement.value) return;

    audioElement.value.playbackRate = value;
    playbackRate.value = value;
    emit('speedchange', value);
  };

  const onAudioLoaded = (event) => {
    if (!audioElement.value) return;
    console.log(event, '加载成功');

    duration.value = audioElement.value.duration;
    volume.value = audioElement.value.volume;
    playbackRate.value = audioElement.value.playbackRate;
    errorMessage.value = ``;
    // 加载播放位置
    loadPlaybackPosition();

    if (props.autoplay) {
      play();
    }
  };

  const onTimeUpdate = () => {
    if (!audioElement.value) return;

    currentTime.value = audioElement.value.currentTime;
    emit('timeupdate', currentTime.value, duration.value);
  };

  const onAudioEnded = () => {
    isPlaying.value = false;
    emit('ended');
    statusMessage.value = '播放结束';

    // 播放结束时保存位置
    savePlaybackPosition();
  };

  const onAudioError = (event: Event) => {
    const error = event as ErrorEvent;
    errorMessage.value = `音频加载失败: ${error.message}`;
    emit('error', error.message);
  };

  // const formatTime = (seconds: number): string => {
  //   if (isNaN(seconds)) return '00:00';

  //   const mins = Math.floor(seconds / 60);
  //   const secs = Math.floor(seconds % 60);
  //   return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  // };

  // 监听音频源变化
  watch(audioSrc, () => {
    if (audioSrc.value) {
      // 延迟加载播放位置，确保音频元素已加载
      setTimeout(() => {
        loadPlaybackPosition();
      }, 100);
    }
  });

  // 生命周期
  onMounted(() => {
    if (props.src) {
      audioSrc.value = props.src;
    }
  });

  onUnmounted(() => {
    if (audioElement.value) {
      // 保存播放位置
      savePlaybackPosition();
      audioElement.value.pause();
      audioElement.value.src = '';
    }
  });

  // 暴露方法给父组件
  defineExpose({
    play,
    pause,
    stop,
    restart,
    continuePlay,
    setVolume: onVolumeChange,
    setSpeed: onSpeedChange,
    seek: onSeek,
    savePlaybackPosition,
    loadPlaybackPosition,
    clearPlaybackPosition,
  });
</script>

<style scoped>
  .voice-control {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  .voice-control__container {
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .voice-control__file-input {
    margin-bottom: 20px;
  }

  .voice-control__controls {
    margin-bottom: 20px;
  }

  .voice-control__progress {
    margin-bottom: 20px;
  }

  .voice-control__time-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
    color: #666;
  }

  .voice-control__volume {
    margin-bottom: 20px;
  }

  .voice-control__speed {
    margin-bottom: 20px;
  }

  .voice-control__position-info {
    margin-bottom: 20px;
  }

  .voice-control__status {
    margin-top: 20px;
  }
</style>
