import { ref, computed } from 'vue';
import { getSourcesApi } from '@/api/system/trafficRule';

// 全局状态
const sourceOptions = ref<any[]>([]);
const mediaOptions = ref<any[]>([]);
const loading = ref(false);
const loaded = ref(false);
const platformOptions = ref<any[]>([]);

// 加载数据
async function loadOptions() {
  if (loading.value || loaded.value) return;

  loading.value = true;
  try {
    const { data } = await getSourcesApi();

    // 线索来源：第一层数据
    sourceOptions.value = data.map((item: any) => ({
      label: item.label,
      value: item.value,
    }));

    // 来源媒体：第一层的所有 children
    const allMediaOptions: any[] = [];
    const allPlatformOptions: any[] = [];
    data.forEach((item: any) => {
      if (item.children && item.children.length > 0) {
        allMediaOptions.push(...item.children);

        // 第三级：平台（遍历媒体的 children）
        item.children.forEach((media: any) => {
          if (media.children && media.children.length > 0) {
            allPlatformOptions.push(...media.children);
          }
        });
      }
    });
    mediaOptions.value = allMediaOptions.map((item: any) => ({
      label: item.label,
      value: item.value,
    }));

    platformOptions.value = allPlatformOptions.map((item: any) => ({
      label: item.label,
      value: item.value,
    }));

    loaded.value = true;
  } catch (error) {
    console.error('获取线索来源媒体来源列表失败:', error);
  } finally {
    loading.value = false;
  }
}

export function useSourceMediaOptions() {
  return {
    sourceOptions: computed(() => sourceOptions.value),
    mediaOptions: computed(() => mediaOptions.value),
    platformOptions: computed(() => platformOptions.value),
    loading: computed(() => loading.value),
    loaded: computed(() => loaded.value),
    loadOptions,
  };
}
