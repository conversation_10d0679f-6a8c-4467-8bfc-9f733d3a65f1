import { BasicColumn } from '@/components/Table';
import RenderIcon from '@/components/RenderIcon/index.vue';
import { h } from 'vue';
export interface ListData {
  id: number;
  title: string;
  icon: string;
  path: string;
  parentId: number;
  sort: number;
  children?: ListData[];
}
export const columns: BasicColumn<ListData>[] = [
  {
    title: '菜单名',
    key: 'title',
    align: 'left',
  },
  {
    title: '地址',
    key: 'path',
    width: 220,
    align: 'center',
  },
  {
    title: 'ICON图标',
    key: 'icon',
    align: 'center',
    render(record) {
      return h(RenderIcon, { icon: record.icon, size: 30 });
    },
  },
  {
    title: '是否展示在导航栏',
    key: 'hiddenInMenu',
    align: 'center',
    render(record: any) {
      return h('span', null, record.hiddenInMenu ? '否' : '是');
    },
  },
  {
    title: '排序',
    key: 'sort',
    align: 'center',
  },
];
