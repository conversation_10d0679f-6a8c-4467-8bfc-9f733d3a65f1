<template>
  <div class="credentials-display">
    <!-- Part 1: 证照信息 -->
    <div class="category-block">
      <div class="category-header">
        <span>证照信息</span>
        <n-button text type="primary" @click="isExpanded = !isExpanded">
          其他资料
          <n-icon class="ml-1">
            <ChevronDown v-if="!isExpanded" />
            <ChevronUp v-else />
          </n-icon>
        </n-button>
      </div>
      <div class="category-content is-horizontal-scroll" v-wheel-scroll>
        <div
          class="group-container"
          v-for="(group, index) in credentialsData.identity"
          :key="index"
        >
          <div class="item-group-title">{{ group.title }}</div>
          <div class="item-group-wrapper">
            <div class="item-group">
              <div v-for="(item, index) in group.items" :key="index" class="preview-item-wrapper">
                <FilePreview
                  v-if="item.url"
                  :url="item.url"
                  :type="item.type"
                  :label="item.label"
                />
                <div v-else class="empty-placeholder">
                  <div class="empty-box"></div>
                  <div class="empty-label">{{ item.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Part 2: 其他资料 (collapsible) -->
    <n-collapse-transition :show="isExpanded">
      <div class="category-block">
        <div class="category-header">
          <span>其他资料</span>
        </div>
        <div class="category-content is-horizontal-scroll" v-wheel-scroll>
          <div
            class="group-container"
            v-for="(group, index) in credentialsData.others"
            :key="index"
          >
            <div class="item-group-title">{{ group.title }}</div>
            <div class="item-group-wrapper">
              <div class="item-group">
                <div v-for="(item, index) in group.items" :key="index" class="preview-item-wrapper">
                  <FilePreview
                    v-if="item.url"
                    :url="item.url"
                    :type="item.type"
                    :label="item.label"
                  />
                  <div v-else class="empty-placeholder">
                    <div class="empty-box"></div>
                    <div class="empty-label">{{ item.label }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-collapse-transition>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineProps, watch, withDefaults } from 'vue';
  import { NButton, NIcon, NCollapseTransition } from 'naive-ui';
  import { ChevronDown, ChevronUp } from '@vicons/ionicons5';
  import FilePreview from '@/components/FilePreview/FilePreview.vue';
  import type { UploadType } from '@/components/FilePreview/types';
  import type { CustomerDetail } from '@/api/detail';
  import { getFileTypeFromUrl } from '@/components/FilePreview/helper';

  interface PreviewItem {
    label: string;
    type: UploadType;
    url: string;
  }

  interface DataGroup {
    title: string;
    items: PreviewItem[];
  }

  const props = withDefaults(
    defineProps<{
      certificateInfo: CustomerDetail['loanApplicationMaterials']['certificateInfo'];
      otherDocuments: CustomerDetail['loanApplicationMaterials']['otherDocuments'];
    }>(),
    {
      certificateInfo: () => ({
        idCardImgUrl: '',
        idCardImgBackUrl: '',
        drivingLicenseImgUrl: '',
        drivingLicenseImgBackUrl: '',
        paperDriverLicenseImgUrl: '',
        paperDriverLicenseImgBackUrl: '',
        bankCardImgUrl: '',
        bankCardBackUrl: '',
      }),
      otherDocuments: () => ({
        vehiclePhotos: [],
        contactListMedias: [],
        traffic12123Medias: [],
        bankStatements: [],
        bankAccountInfos: [],
        insurances: [],
      }),
    }
  );

  const isExpanded = ref(false);

  const credentialsData = reactive<{
    identity: DataGroup[];
    others: DataGroup[];
  }>({
    identity: [],
    others: [],
  });

  function mapToPreviewItems(urls: string[], labelPrefix = ''): PreviewItem[] {
    if (!urls || urls.length === 0) {
      return [{ label: `${labelPrefix}`, url: '', type: 'img' }];
    }
    return urls.map((url, index) => ({
      label: urls.length > 1 ? `${labelPrefix} ${index + 1}` : labelPrefix,
      url,
      type: getFileTypeFromUrl(url),
    }));
  }

  watch(
    () => [props.certificateInfo, props.otherDocuments],
    ([certInfo, otherDocs]) => {
      if (!certInfo || !otherDocs) {
        credentialsData.identity = [];
        credentialsData.others = [];
        return;
      }

      const typedCertInfo =
        certInfo as CustomerDetail['loanApplicationMaterials']['certificateInfo'];
      const typedOtherDocs =
        otherDocs as CustomerDetail['loanApplicationMaterials']['otherDocuments'];

      // 证照信息
      credentialsData.identity = [
        {
          title: '身份证',
          items: [
            { label: '身份证正面', type: 'img', url: typedCertInfo.idCardImgUrl },
            { label: '身份证反面', type: 'img', url: typedCertInfo.idCardImgBackUrl },
          ],
        },
        {
          title: '行驶证',
          items: [
            { label: '行驶证正面', type: 'img', url: typedCertInfo.drivingLicenseImgUrl },
            { label: '行驶证反面', type: 'img', url: typedCertInfo.drivingLicenseImgBackUrl },
          ],
        },
        {
          title: '纸质驾照',
          items: [
            { label: '纸质驾照正面', type: 'img', url: typedCertInfo.paperDriverLicenseImgUrl },
            { label: '纸质驾照反面', type: 'img', url: typedCertInfo.paperDriverLicenseImgBackUrl },
          ],
        },
        {
          title: '银行卡',
          items: [
            { label: '银行卡正面', type: 'img', url: typedCertInfo.bankCardImgUrl },
            { label: '银行卡反面', type: 'img', url: typedCertInfo.bankCardBackUrl },
          ],
        },
      ];

      // 其他资料
      credentialsData.others = [
        {
          title: '车辆照片',
          items: mapToPreviewItems(typedOtherDocs.vehiclePhotos, '车辆照片'),
        },
        {
          title: '通讯录',
          items: mapToPreviewItems(typedOtherDocs.contactListMedias, '通讯录媒体'),
        },
        {
          title: '交管12123',
          items: mapToPreviewItems(typedOtherDocs.traffic12123Medias, '12123媒体'),
        },
        {
          title: '流水',
          items: mapToPreviewItems(typedOtherDocs.bankStatements, '银行流水'),
        },
        {
          title: '开户行信息',
          items: mapToPreviewItems(typedOtherDocs.bankAccountInfos, '开户行信息'),
        },
        {
          title: '保险',
          items: mapToPreviewItems(typedOtherDocs.insurances, '保险单'),
        },
      ];
    },
    { immediate: true, deep: true }
  );
</script>

<style lang="less" scoped>
  @import '@/styles/custom/scrollbar.less';

  .credentials-display {
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
  }
  .category-block {
    padding: 16px 16px 4px 16px;
    &:first-child {
      border-bottom: 1px solid #f0f0f0;
    }
  }
  .category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: bold;
    font-size: 16px;
  }
  .category-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .is-horizontal-scroll {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: 16px;
    .custom-scrollbar();
  }
  .group-container {
    position: relative;
    padding-top: 12px;
    display: flex;
  }
  .item-group-wrapper {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 16px;
  }
  .item-group-title {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    padding: 0 4px;
    font-size: 14px;
    color: #666;
    white-space: nowrap;
  }
  .item-group {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }
  .is-horizontal-scroll .item-group {
    flex-wrap: nowrap;
  }
  .preview-item-wrapper {
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: -8px;
      top: 50%;
      transform: translateY(-68%);
      width: 1px;
      height: 60%;
      background-color: #e0e0e0;
    }
  }
  .empty-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .empty-box {
    width: 120px;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"%3E%3Cline x1="0" y1="0" x2="30" y2="30" stroke="%23d9d9d9" stroke-width="1"/%3E%3Cline x1="30" y1="0" x2="0" y2="30" stroke="%23d9d9d9" stroke-width="1"/%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 50%;
  }
  .empty-label {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
  }
  .ml-1 {
    margin-left: 4px;
  }
</style>
