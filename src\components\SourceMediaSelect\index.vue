<template>
  <NSelect
    :value="modelValue"
    multiple
    :options="options"
    :placeholder="placeholder"
    filterable
    clearable
    @update:value="$emit('update:modelValue', $event)"
  />
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import { NSelect } from 'naive-ui';
  import { useSourceMediaOptions } from '@/components/SourceMediaSelect/useSourceMediaOptions';

  interface Props {
    modelValue?: string[];
    placeholder?: string;
    type?: 'source' | 'media'; // 'source' for 线索来源, 'media' for 来源媒体
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    placeholder: '请选择',
    type: 'source',
  });

  defineEmits<{
    'update:modelValue': [value: string[]];
  }>();

  // 使用全局状态
  const { sourceOptions, mediaOptions, loadOptions } = useSourceMediaOptions();

  // 根据类型选择对应的选项
  const options = computed(() => {
    return props.type === 'source' ? sourceOptions.value : mediaOptions.value;
  });

  onMounted(() => {
    loadOptions();
  });
</script>
