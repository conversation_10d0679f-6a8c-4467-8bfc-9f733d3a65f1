# 表单组件更新日志

## [1.1.3] - 2024-12-19

### 🐛 Bug修复
- 🔧 **重置功能修复**：修复重置表单时 schema 默认值被清空的问题
- 📝 **逻辑优化**：改进初始化逻辑，确保默认值模型正确保存
- ✅ **测试完善**：新增重置功能测试示例

### 技术改进
- **initDefault 函数优化**：先保存默认值模型，再处理缓存恢复
- **resetFields 函数修复**：正确恢复到 schema 中定义的默认值
- **边界情况处理**：区分 undefined 和 null 值的处理

## [1.1.2] - 2024-12-19

### 新增功能
- ✨ **展开收起状态缓存**：表单的展开收起状态现在也会被自动缓存和恢复
- 🔄 **状态同步**：展开收起状态变化时自动保存，页面刷新后自动恢复

### 优化
- 🔧 **缓存逻辑增强**：同时缓存表单数据和UI状态，提供更完整的用户体验
- 📝 **文档更新**：更新相关文档说明展开收起状态缓存功能

## [1.1.1] - 2024-12-19

### 优化
- 🔧 **缓存时间优化**：默认缓存时间改为当天24点，每天都是新的开始，更符合日常使用习惯
- 📝 **文档更新**：更新相关文档说明新的缓存时间策略

## [1.1.0] - 2024-12-19

### 新增功能
- ✨ **表单缓存功能**：支持本地缓存表单数据，解决同一路由多次使用表单组件的数据保持问题
  - 自动保存表单数据到本地存储
  - 支持自动恢复缓存数据
  - 支持自定义缓存key，解决同一路由多个表单的冲突问题
  - 支持设置缓存过期时间
  - 只缓存有值的字段，避免存储空数据
  - 提供完整的缓存操作API

### 新增配置项
- `enableCache`: 是否启用缓存功能（默认：false）
- `cacheKey`: 自定义缓存key（默认：使用路由路径）
- `cacheTimeout`: 缓存过期时间，单位秒（默认：缓存到当天24点）
- `autoRestoreCache`: 是否自动恢复缓存（默认：true）

### 新增API方法
- `saveFormCache()`: 手动保存表单缓存
- `restoreFormCache()`: 手动恢复表单缓存
- `clearFormCache()`: 清除表单缓存
- `hasCacheData()`: 检查是否有缓存数据

### 技术实现
- 新增 `useFormCache` hook 处理缓存逻辑
- 集成到 `BasicForm` 组件中
- 使用项目现有的 Storage 工具类
- 支持深度监听表单数据变化
- 防抖机制避免频繁缓存操作

### 使用场景
1. **同一路由多个表单**：通过不同的 cacheKey 区分
2. **搜索表单**：保留用户搜索条件
3. **长表单填写**：避免意外刷新导致数据丢失
4. **表单草稿**：临时保存用户输入

### 示例代码
```vue
<template>
  <BasicForm
    :schemas="schemas"
    :enable-cache="true"
    cache-key="user-form"
    :cache-timeout="60 * 60 * 24"
    @submit="handleSubmit"
  />
</template>
```

### 文档和测试
- 新增缓存功能使用文档 `docs/FormCache.md`
- 新增使用示例 `examples/FormCacheExample.vue`
- 新增单元测试 `tests/FormCache.test.ts`

### 兼容性
- ✅ 向后兼容，默认不启用缓存功能
- ✅ 不影响现有表单组件的使用
- ✅ 可选择性启用缓存功能

---

## [1.0.0] - 之前版本

### 基础功能
- 基础表单组件实现
- 动态表单配置
- 表单验证
- 列显示/隐藏配置
- 响应式布局
- 多种表单控件支持
