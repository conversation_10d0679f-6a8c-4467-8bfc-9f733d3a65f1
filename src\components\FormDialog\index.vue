<template>
  <n-modal
    :title="title"
    preset="dialog"
    style="width: 30%"
    :show-icon="false"
    v-bind="$attrs"
    v-model:show="dialogVisible"
    :mask-closable="false"
    @after-leave="handleClose"
    @after-enter="emit('open')"
  >
    <n-spin :show="formLoading">
      <n-form
        ref="formRef"
        :model="formModel"
        :rules="rules"
        :label-width="labelWidth"
        v-bind="formProps"
        label-placement="left"
        style="width: 100%"
      >
        <slot></slot>

        <n-form-item label=" ">
          <div class="flex gap-[10px]">
            <n-button v-if="showCancel" :disabled="submitLoading" @click="dialogVisible = false">
              {{ cancelText }}
            </n-button>
            <n-button
              v-if="showConfirm"
              type="primary"
              :loading="submitLoading"
              @click="handleConfirm"
            >
              {{ confirmText }}
            </n-button>
            <slot name="footer-button"></slot>
          </div>
        </n-form-item>
      </n-form>
    </n-spin>
  </n-modal>
</template>

<script lang="ts" setup>
  import { ref, watch, defineEmits, defineProps } from 'vue';
  import type { FormRules, FormInst } from 'naive-ui';

  interface Props {
    modelValue: boolean;
    title?: string;
    formModel: Record<string, any>;
    rules?: FormRules;
    labelWidth?: number | string;
    formProps?: Record<string, any>;
    cancelText?: string;
    confirmText?: string;
    showConfirm?: boolean;
    showCancel?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '新增',
    labelWidth: 100,
    rules: () => ({}),
    formProps: () => ({}),
    cancelText: '取消',
    confirmText: '确认',
    showConfirm: true,
    showCancel: true,
  });

  const emit = defineEmits(['update:modelValue', 'submit', 'close', 'open']);

  const dialogVisible = ref(props.modelValue);
  const formRef = ref<FormInst | null>(null);
  const formLoading = ref(false);
  const submitLoading = ref(false);

  watch(
    () => props.modelValue,
    (val) => {
      dialogVisible.value = val;
    }
  );

  watch(dialogVisible, (val) => {
    emit('update:modelValue', val);
    if (!val) resetForm();
  });

  function handleClose() {
    resetForm();
    submitLoading.value = false;
    formLoading.value = false;
    emit('close');
  }
  function handleConfirm() {
    formRef.value?.validate((errors) => {
      if (!errors) {
        submitLoading.value = true;
        emit('submit', props.formModel, formDone);
      }
    });
  }
  function formDone() {
    submitLoading.value = false;
  }
  function resetForm() {
    formRef.value?.restoreValidation();
    // formRef.value?.resetFields();
  }
  // 加载接口数据
  async function loadFormData(fetchFn: () => Promise<any>, processor?: (data: any) => any) {
    formLoading.value = true;
    try {
      const { code, data } = await fetchFn();
      if (code === 200) {
        const result = processor ? processor(data) : data;
        Object.assign(props.formModel, result);
      }
    } finally {
      formLoading.value = false;
    }
  }
  // 加载静态数据
  function loadStaticData(data: any, processor?: (data: any) => any) {
    formLoading.value = true;
    const result = processor ? processor(data) : data;
    Object.assign(props.formModel, result);
    formLoading.value = false;
  }

  defineExpose({ loadFormData, loadStaticData });
</script>
