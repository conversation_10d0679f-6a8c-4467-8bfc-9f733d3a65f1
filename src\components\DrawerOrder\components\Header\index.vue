<template>
  <div class="order-stages-header">
    <!-- 基础信息 -->
    <div class="basic-info-container">
      <n-descriptions
        title="基本信息"
        :column="6"
        label-placement="left"
        size="small"
        class="basic-info"
      >
        <n-descriptions-item label="贷款订单ID"> 202222232111951 </n-descriptions-item>
        <n-descriptions-item label="姓名"> 张三丰 </n-descriptions-item>
        <n-descriptions-item label="城市"> 张三丰 </n-descriptions-item>
        <n-descriptions-item label="手机号"> 18224424821 </n-descriptions-item>
        <n-descriptions-item label="贷款金额"> 100000 </n-descriptions-item>
        <n-descriptions-item label="贷款期限"> 36 </n-descriptions-item>
      </n-descriptions>
    </div>
    <div class="stages-container-wrapper">
      <!-- 阶段流程 -->
      <div class="stages-container">
        <div
          v-for="stage in stages"
          :key="stage.id"
          class="stage-item"
          :class="getStageClass(stage.status)"
          @click="handleStageClick(stage)"
        >
          <div class="stage-content">
            <div class="stage-title">{{ stage.title }}</div>
          </div>
        </div>
      </div>

      <!-- 小阶段步骤条 -->
      <div
        v-if="currentStage && currentStage.subStages && currentStage.subStages.length > 0"
        class="sub-stages-container"
      >
        <div class="custom-steps">
          <!-- 连接线层（背景层） -->
          <div class="lines-container" :style="contentGridStyle">
            <div
              v-for="(subStage, index) in currentStage.subStages"
              :key="`line-${subStage.id}`"
              class="line-cell"
            >
              <div
                v-if="index < currentStage.subStages!.length - 1"
                class="step-line"
                :class="`line-${subStage.status}`"
              ></div>
            </div>
          </div>

          <!-- 图标和文字层（前景层） -->
          <div class="steps-grid" :style="contentGridStyle">
            <div
              v-for="subStage in currentStage.subStages"
              :key="subStage.id"
              class="step-item"
              :class="`step-${subStage.status}`"
            >
              <div class="step-icon" :class="`icon-${subStage.status}`">
                <div class="inner-circle"></div>
              </div>
              <div class="step-content">
                <div class="step-title">{{ subStage.title }}</div>
                <div v-if="subStage.description" class="step-description">
                  {{ subStage.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';

  // 阶段状态类型
  type StageStatus = 'pending' | 'ongoing' | 'completed';

  // 子阶段类型
  interface SubStage {
    id: string | number;
    title: string;
    description?: string;
    status: StageStatus;
  }

  // 阶段类型
  interface Stage {
    id: string | number;
    title: string;
    status: StageStatus;
    subStages?: SubStage[];
  }

  // 定义 emits
  const emit = defineEmits<{
    stageClick: [stage: Stage];
  }>();

  // 模拟数据
  const stages = ref<Stage[]>([
    {
      id: 1,
      title: '预审阶段',
      status: 'ongoing',
      subStages: [
        { id: '1-1', title: '发起进件', description: '已完成', status: 'ongoing' },
        { id: '1-2', title: '授权书签署', description: '已完成', status: 'pending' },
        { id: '1-3', title: '预审阶段', description: '已完成', status: 'pending' },
      ],
    },
    {
      id: 2,
      title: '进件签约',
      status: 'pending',
      subStages: [
        { id: '2-1', title: '产品查询&车辆评估', description: '已完成', status: 'completed' },
        { id: '2-2', title: '机构进件', description: '已完成', status: 'completed' },
        { id: '2-3', title: '资方进件', description: '已完成', status: 'completed' },
        { id: '2-4', title: '资方绑卡合同签署', description: '已完成', status: 'completed' },
        { id: '2-5', title: '德易绑卡', description: '已完成', status: 'completed' },
        { id: '2-6', title: '德易签约', description: '已完成', status: 'completed' },
      ],
    },
    {
      id: 3,
      title: '面签阶段',
      status: 'pending',
      subStages: [
        { id: '3-1', title: '面签阶段', description: '是大大222222阿达是', status: 'completed' },
      ],
    },
    {
      id: 4,
      title: 'GPS安装',
      status: 'pending',
      subStages: [
        { id: '4-1', title: 'GPS安装要求查询', description: '待处理', status: 'pending' },
        { id: '4-2', title: '安装GPS', description: '待处理', status: 'pending' },
      ],
    },
    {
      id: 5,
      title: '抵押阶段',
      status: 'pending',
      subStages: [
        { id: '5-1', title: '准备抵押材料', description: '待处理', status: 'pending' },
        { id: '5-2', title: '提交抵押申请', description: '待处理', status: 'pending' },
        { id: '5-3', title: '办理抵押登记', description: '待处理', status: 'pending' },
        { id: '5-4', title: '抵押完成', description: '待处理', status: 'pending' },
      ],
    },
    {
      id: 6,
      title: '放款阶段',
      status: 'pending',
    },
  ]);

  // 当前选中的大阶段（默认为进行中的阶段）
  const currentStage = ref<Stage | null>(
    stages.value.find((s) => s.status === 'ongoing') || stages.value[0]
  );

  // 计算步骤内容区域的 grid 布局
  const contentGridStyle = computed(() => {
    const count = currentStage.value?.subStages?.length || 0;
    return {
      gridTemplateColumns: `repeat(${count}, 1fr)`,
    };
  });

  // 获取阶段样式类
  const getStageClass = (status: StageStatus) => {
    return `status-${status}`;
  };

  // 点击大阶段
  const handleStageClick = (stage: Stage) => {
    currentStage.value = stage;
    emit('stageClick', stage);
  };
</script>

<style lang="less" scoped>
  .basic-info-container {
    background-color: #fff;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 12px;
  }
  .order-stages-header {
    .basic-info {
      margin-bottom: 0;
    }
    .stages-container-wrapper {
      background-color: #fff;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 12px;
    }
    .stages-container {
      display: flex;
      align-items: center;
      width: 100%;
      overflow-x: auto;
      margin-bottom: 20px;
    }

    .stage-item {
      position: relative;
      flex: 1;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1890ff;
      color: white;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.3s ease;
      padding: 0 20px;
      margin-right: 5px;

      // 右侧三角形箭头
      &::after {
        content: '';
        position: absolute;
        right: -10px;
        top: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 24px 0 24px 10px;
        border-color: transparent transparent transparent #1890ff;
        z-index: 2;
        transition: border-color 0.3s ease;
      }

      // 左侧凹陷
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 24px 0 24px 10px;
        border-color: transparent transparent transparent #fff;
        z-index: 1;
      }

      &:first-child {
        border-radius: 8px 0 0 8px;
        padding-left: 30px;

        &::before {
          display: none;
        }
      }

      &:last-child {
        border-radius: 0 8px 8px 0;
        padding-right: 30px;
        margin-right: 0;

        &::after {
          display: none;
        }
      }

      &:first-child:last-child {
        border-radius: 8px;
      }

      .stage-content {
        white-space: nowrap;
        position: relative;
        z-index: 2;

        .stage-title {
          position: relative;
          z-index: 1;
          color: white;
        }
      }

      // 已完成状态
      &.status-completed {
        background: #1890ff;
        color: white;

        &::after {
          border-left-color: #1890ff;
        }
      }

      // 进行中状态
      &.status-ongoing {
        background: #1890ff;
        color: white;

        &::after {
          border-left-color: #1890ff;
        }
      }

      // 未开始状态
      &.status-pending {
        background: #d9d9d9;
        color: #8c8c8c;

        &::after {
          border-left-color: #d9d9d9;
        }
      }
    }

    // 小阶段步骤条容器
    .sub-stages-container {
      background: #ffffff;
      border-radius: 8px;
      margin-top: 16px;
    }

    // 自定义步骤条样式
    .custom-steps {
      width: 100%;
      background-color: #f4f9fd;
      padding: 12px;
      position: relative;

      // 连接线层（背景层）
      .lines-container {
        position: absolute;
        top: 20px; // 图标中心位置（padding 12px + 图标高度的一半 8px）
        left: 12px;
        right: 12px;
        display: grid;
        gap: 0;
        pointer-events: none;

        .line-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .step-line {
            position: absolute;
            left: 50%;
            width: 100%;
            height: 2px;
            transition: all 0.3s ease;

            &.line-completed {
              background: #52c41a;
            }

            &.line-ongoing,
            &.line-pending {
              background: #d9d9d9;
            }
          }
        }
      }

      // 图标和文字层（前景层）
      .steps-grid {
        display: grid;
        width: 100%;
        gap: 0;
        position: relative;
        z-index: 1;

        .step-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          min-width: 0;

          .step-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
            margin-bottom: 12px;

            .inner-circle {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              transition: all 0.3s ease;
            }

            &.icon-completed {
              background: #c3ffa5;
              border: 1px solid #51d311;

              .inner-circle {
                background: #71dd3c;
              }
            }

            &.icon-ongoing {
              background: #c2e6ff;
              border: 1px solid #1890ff;

              .inner-circle {
                background: #2c8df4;
              }
            }

            &.icon-pending {
              background: #eceeee;
              border: 1px solid #d9d9d9;

              .inner-circle {
                background: #ccccca;
              }
            }
          }

          .step-content {
            text-align: center;
            min-width: 0;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;

            .step-title {
              font-size: 14px;
              color: #262626;
              font-weight: 500;
              line-height: 1.5;
              margin-bottom: 4px;
              word-break: break-word;
            }

            .step-description {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 1.5;
              word-break: break-word;
            }
          }

          // 进行中步骤
          &.step-ongoing {
            .step-content .step-title {
              font-weight: 600;
            }
          }

          // 待处理步骤
          &.step-pending {
            .step-content .step-title {
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }
</style>
