import type { PropType } from 'vue';
import type { UploadType } from './types';

export const filePreviewProps = {
  /**
   * 文件URL
   */
  url: {
    type: String,
    required: true,
  },
  /**
   * 文件类型
   * @type {'img' | 'video' | 'doc'}
   */
  type: {
    type: String as PropType<UploadType>,
    required: true,
  },
  /**
   * 组件宽度
   */
  width: {
    type: String,
    default: '120px',
  },
  /**
   * 组件高度
   */
  height: {
    type: String,
    default: '120px',
  },
  /**
   * 下方的描述文字
   */
  label: {
    type: String,
    default: '',
  },
};
