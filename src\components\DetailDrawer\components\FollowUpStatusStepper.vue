<template>
  <div class="stepper-container">
    <div class="title">跟进详情:</div>
    <div class="steps">
      <div
        v-for="(step, index) in followUpNodeStatusOptions"
        :key="step.value"
        class="step-item"
        :style="{ backgroundImage: `url(${getStepImage(step, index)})` }"
        :class="{
          'is-active': currentStatus === step.value,
        }"
      >
        <div class="step-inner">
          {{ step.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { followUpNodeStatusOptions, FollowUpNodeStatusEnum } from '@/enums/detailEnum';
  import step1 from '@/assets/images/detail-drawer/step1.png';
  import step1_check from '@/assets/images/detail-drawer/step1_check.png';
  import step2 from '@/assets/images/detail-drawer/step2.png';
  import step2_check from '@/assets/images/detail-drawer/step2_check.png';
  import step3 from '@/assets/images/detail-drawer/step3.png';
  import step3_check from '@/assets/images/detail-drawer/step3_check.png';

  const props = defineProps<{
    currentStatus: FollowUpNodeStatusEnum;
  }>();

  const getStepImage = (step: { label: string; value: FollowUpNodeStatusEnum }, index: number) => {
    const isActive = props.currentStatus === step.value;
    const totalSteps = followUpNodeStatusOptions.length;

    if (index === 0) {
      return isActive ? step1_check : step1;
    }
    if (index === totalSteps - 1) {
      return isActive ? step3_check : step3;
    }
    return isActive ? step2_check : step2;
  };
</script>

<style lang="less" scoped>
  .stepper-container {
    display: flex;
    align-items: center;
    margin-top: 40px;

    .title {
      font-size: 14px;
      color: #666;
      margin-right: 10px;
    }

    .steps {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .step-item {
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      padding: 0 20px;
      color: black;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 38px;
      min-width: 122px;
      margin-left: -12px;

      &:first-child {
        margin-left: 0;
      }

      &.is-active {
        color: white;
      }
    }
  }
</style>
