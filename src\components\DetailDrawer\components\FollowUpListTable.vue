<template>
  <n-data-table :columns="columns" :data="data" :render-cell="renderCell" />
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { NDataTable } from 'naive-ui';
  import type { DataTableColumns } from 'naive-ui';
  import {
    yesNoOptions,
    vehicleStatusOptions,
    checkStatusOptions,
    phoneCommunicationStatusOptions,
    wechatCommunicationStatusOptions,
  } from '@/enums/detailEnum';
  import type { CustomerDetail } from '@/api/detail';

  const props = defineProps({
    data: {
      type: Object as () => CustomerDetail,
      required: true,
    },
  });

  interface ClueInfo {
    [key: string]: any;
  }

  const data = computed(() => {
    if (!props.data) return [];

    const { mediaPlatformSource, communicationStatus, addWeChat } = props.data.clueInfoVo || {};
    const { vehicleStatus, carOfPersonStatus } =
      props.data.loanApplicationMaterials?.vehicleInfo || {};
    const { threeElementsStatus } = props.data.loanApplicationMaterials?.personalInfo || {};

    return [
      {
        mediaPlatformSource,
        communicationStatus,
        addWeChat,
        vehicleStatus,
        threeElementsStatus,
        carOfPersonStatus,
      } as ClueInfo,
    ];
  });

  const columns: DataTableColumns<ClueInfo> = [
    { title: '线索来源', key: 'mediaPlatformSource', width: 100, align: 'center' },
    {
      title: '通讯状态',
      key: 'communicationStatus',
      width: 100,
      align: 'center',
      render(row) {
        return (
          [...phoneCommunicationStatusOptions, ...wechatCommunicationStatusOptions].find(
            (item) => item.value === row.communicationStatus
          )?.label || '--'
        );
      },
    },
    {
      title: '是否加微',
      key: 'addWeChat',
      width: 100,
      align: 'center',
      render(row) {
        return yesNoOptions.find((item) => item.value === row.addWeChat)?.label || '--';
      },
    },
    {
      title: '车辆状态',
      key: 'vehicleStatus',
      width: 100,
      align: 'center',
      render(row) {
        return vehicleStatusOptions.find((item) => item.value === row.vehicleStatus)?.label || '--';
      },
    },
    {
      title: '三要素校验状态',
      key: 'threeElementsStatus',
      width: 120,
      align: 'center',
      render(row) {
        return (
          checkStatusOptions.find((item) => item.value === row.threeElementsStatus)?.label || '--'
        );
      },
    },
    {
      title: '人车校验状态',
      key: 'carOfPersonStatus',
      width: 120,
      align: 'center',
      render(row) {
        return (
          checkStatusOptions.find((item) => item.value === row.carOfPersonStatus)?.label || '--'
        );
      },
    },
  ];

  const renderCell = (value: any) => {
    if (!value) return '--';
    return value;
  };
</script>
