import { computed } from 'vue';

const DEFAULT_DATE_KEYS = [
  'createTimeQuery',
  'clueEnableAssignQuery',
  'triageTimeQuery',
  'lastFollowTimeQuery',
];

/**
 * 日期字段 clearable, noHidden 状态管理组合式函数
 * @param getFormModel - 从 useForm 获取的 getFormModel 函数
 * @param getActiveColumns - 从 useForm 获取的 getActiveColumns 函数
 * @param relatedKeys - 相关的字段 keys
 * @returns
 */
export function useDateOptions(
  getFormModel: () => any,
  getActiveColumns: () => string[],
  relatedKeys: string[] = DEFAULT_DATE_KEYS
) {
  const formModel = computed(() => {
    const fm = getFormModel(); // 获取响应式 formModel
    const activeColumns = getActiveColumns();

    // 只保留激活列
    return Object.keys(fm).reduce((res, key) => {
      if (relatedKeys.includes(key) && activeColumns.includes(key)) {
        res[key] = fm[key];
      }
      return res;
    }, {});
  });

  const multiDateClearable = computed<Record<string, boolean>>(() => {
    return Object.keys(formModel.value).reduce((res, key) => {
      res[key] = getClearable(key);
      return res;
    }, {});
  });

  function getClearable(currentKey: string) {
    return Object.keys(formModel.value).some((key) => {
      if (currentKey === key) {
        return false;
      }

      return !!formModel.value[key];
    });
  }

  const multiDateNoHidden = computed<Record<string, boolean>>(() => {
    const obj = Object.keys(formModel.value).reduce((res, key) => {
      res[key] = getNoHidden(key);

      return res;
    }, {});

    if (Object.keys(obj).every((key) => !obj[key])) {
      const keys = Object.keys(formModel.value);
      const key = keys.find((key) => formModel.value[key]);
      if (key) {
        obj[key] = true;
      }
    }

    return obj;
  });

  function getNoHidden(currentKey: string) {
    const keys = Object.keys(formModel.value).filter((key) => key !== currentKey);

    if (formModel.value[currentKey] && keys.every((key) => !formModel.value[key])) {
      return true;
    }

    return false;
  }

  return {
    multiDateClearable,
    multiDateNoHidden,
  };
}
