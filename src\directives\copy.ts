/**
 * v-copy
 * 复制某个值至剪贴板
 * 接收参数：string类型/Ref<string>类型/Reactive<string>类型
 */
import type { Directive, DirectiveBinding } from 'vue';

interface ElType extends HTMLElement {
  copyData: string | number;
  __handleClick__?: (event: Event) => void;
}

const copy: Directive = {
  mounted(el: ElType, binding: DirectiveBinding) {
    el.copyData = binding.value;
    // 创建绑定的事件处理函数并保存引用
    el.__handleClick__ = function (event: Event) {
      handleClick.call(el, event);
    };
    el.addEventListener('click', el.__handleClick__);
  },
  updated(el: ElType, binding: DirectiveBinding) {
    el.copyData = binding.value;
  },
  beforeUnmount(el: ElType) {
    if (el.__handleClick__) {
      el.removeEventListener('click', el.__handleClick__);
      delete el.__handleClick__;
    }
  },
};

async function handleClick(this: ElType, event: Event) {
  event.preventDefault();
  event.stopPropagation();

  const text = String(this.copyData || '');

  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      console.log('复制成功 (Clipboard API)', text);
      // 可以在这里添加成功提示
      if (window.$message) {
        window.$message.success('复制成功');
      }
    } else {
      // 回退到传统方法
      fallbackCopy(text);
    }
  } catch (err) {
    console.error('复制失败，尝试回退方法:', err);
    fallbackCopy(text);
  }
}

function fallbackCopy(text: string) {
  try {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.left = '-999999px';
    textarea.style.top = '-999999px';
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();

    const successful = document.execCommand('copy');
    document.body.removeChild(textarea);

    if (successful) {
      console.log('复制成功 (fallback)', text);
      if (window.$message) {
        window.$message.success('复制成功');
      }
    } else {
      throw new Error('execCommand failed');
    }
  } catch (err) {
    console.error('复制失败:', err);
    if (window.$message) {
      window.$message.error('复制失败，请手动复制');
    }
  }
}

export default copy;
