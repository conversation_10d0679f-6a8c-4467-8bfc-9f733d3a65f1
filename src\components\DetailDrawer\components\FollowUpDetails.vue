<template>
  <div class="follow-up-details">
    <FollowUpStatusStepper :current-status="detail.clueInfoVo.followStatusIndex" />

    <div v-if="detail.clueInfoVo.followStatusIndex === FollowUpNodeStatusEnum.AWAITING_FIRST">
      <AwaitingFirstFollowUp
        :create-time="detail.clueInfoVo.triageTime"
        @start-follow-up="onStart"
      />
    </div>
    <div v-else>
      <FollowUpTimeline :records="records" :current-time="detail.currentTime" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import type { CustomerDetail } from '@/api/detail';
  import { FollowUpNodeStatusEnum } from '@/enums/detailEnum';
  import FollowUpStatusStepper from './FollowUpStatusStepper.vue';
  import AwaitingFirstFollowUp from './AwaitingFirstFollowUp.vue';
  import FollowUpTimeline from './FollowUpTimeline.vue';
  import { computed } from 'vue';
  import { cloneDeep } from 'lodash-es';

  const props = defineProps<{
    detail: CustomerDetail;
  }>();

  const emit = defineEmits(['start-follow-up']);

  const records = computed(() => {
    const list = cloneDeep(props.detail.clueFollowUpRecords);
    const list2 = cloneDeep(props.detail.clueAgentRecords);

    list.forEach((record) => {
      const index = list2.findIndex((item) => item.isCanceled === 1 && item.agentId === record.id);
      if (index !== -1) {
        record.clueAgent = list2[index];
      }
    });

    return list;
  });

  function onStart() {
    emit('start-follow-up');
  }
</script>
