import { BasicColumn } from '@/components/Table';
import dayjs from 'dayjs';

export interface ListData {
  id: number;
  messageTypeId: number;
  content: string;
  contentData: string;
  isHandled: number;
  handleTime: string;
  deleteTime: string;
  createTime: string;
  updateTime: string;
  entityId?: number;
}

export const columns: BasicColumn<ListData>[] = [
  { type: 'selection' as any, key: 'selection' },
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    render: (_, index) => index + 1,
  },
  {
    title: '消息类型',
    key: 'messageTypeId',
    width: 100,
    align: 'center',
  },
  {
    title: '消息内容',
    key: 'content',
    width: 300,
    align: 'center',
  },
  {
    title: '消息对象',
    key: 'contentData',
    width: 150,
    align: 'center',
  },
  {
    title: '消息时间',
    key: 'createTime',
    width: 120,
    align: 'center',
    render: (record: ListData) => {
      return (
        <span>
          {record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
];
